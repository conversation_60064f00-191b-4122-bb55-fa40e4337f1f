<template>
  <div class="mod-report-datasource">
    <el-row>
      <el-form :inline="true" :model="formInline" class="demo-form-inline">
        <el-form-item label="名称:">
          <el-input clearable v-model="formInline.tableName" placeholder="请输入中文名或英文名"></el-input>
        </el-form-item>
        <el-form-item label="所属系统:">
          <selector dictKey="WARNING_BELONG_SYSTEM" v-model="formInline.dataType" placeholder="请选择" clearable></selector>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="pageIndex = 1;getQueryList()">查询</el-button>
          <el-button type="primary" v-if="isAuth('warning:datasource:xinzeng')" @click="addDataSource">新增</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row class="row-bg row-query-separator"></el-row>
    <el-row>
      <el-table
        :data="dataList"
        v-loading="dataListLoading"
        ref="multipleTable"
        tooltip-effect="dark"
        style="width: 100%"
        border
        :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}">
        <el-table-column
          type="index"
          label="序号"
          width="50%">
        </el-table-column>
        <el-table-column
          prop="tableName"
          label="表中文名"
          width=""
        >
        </el-table-column>
        <el-table-column
          label="表英文名  "
          prop="tableCode"
          width="">
        </el-table-column>
        <el-table-column
          prop="dataType"
          label="所属系统"
          :formatter="formatterDataType"
          width="">
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="160%">
        </el-table-column>
        <el-table-column label="操作" width="90%">
          <template slot-scope="scope">
            <el-button
              type="text"
              v-if="isAuth('warning:datasource:bianji')"
              size="mini"
              @click="preview(scope.row)">编辑
            </el-button>
            <el-button
              type="text"
              v-if="isAuth('warning:datasource:shanchu')"
              size="mini"
              @click="deleteItem(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  import selector from '@/components/dict/selector'
  import { Loading } from 'element-ui';
  export default {
    components: {
      selector
    },
    data() {
      return {
        dialogVisible: false,
        formInline: {
          tableName: '',
          dataType: null,
          type: '3'
        },
        dataSource: {
          fieldName: '',
          disableName: '',
          value: ''
        },
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        userId: this.$store.state.user.id,
        // 分页数据
        dataList: [],
        ableCode: ''
      }
    },
    created() {
      let query = this.$route.query;
      this.pageIndex = (query.page && Number(query.page)) || this.pageIndex;
      this.pageSize = (query.limit && Number(query.limit)) || this.pageSize;
      this.formInline.tableName = query.tableName || this.formInline.tableName;
      this.formInline.dataType = query.dataView || this.formInline.dataView;
      this.getQueryList(1)
    },
    activated() {
      this.getQueryList()
    },
    methods: {
      // ===================================================================初始化
      // 查询列表数据 2019-05-31
      getQueryList(isRefresh) {
        let query = {
          'page': this.pageIndex,
          'limit': this.pageSize,
          'tableName': this.formInline.tableName,
          'createUser': this.userId,
          'dataType': this.formInline.dataType,
          'type': this.formInline.type
        };
        if (!isRefresh) {
          this.$router.push({query: query});
          let nameQuery = {name: this.$route.name, query: query};
          this.$store.commit('common/updateMainTabsQuery', nameQuery);
        }
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/warning/warningdatasource/queryPage'),
          method: 'get',
          params: this.$http.adornParams(query)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // --------------------------------------------------------------------------------------------数据过滤 start
      // 过滤数据视图
      formatterDataType(row, column) {
        return this.$store.getters['bizcache/getDictItemByKeyAndId']('WARNING_BELONG_SYSTEM', parseInt(row.dataType));
      },
      // ======================================================================删除
      deleteItem(val) {
        this.$confirm('确认是否删除该项?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 先判断数据源是否被使用，如果是，则不可以删除
          this.$http({
            url: this.$http.adornUrl('/warning/warningrole/list'),
            method: 'get',
            params: this.$http.adornParams({dataView: val.id})
          }).then(({data}) => {
            this.companyList = data && data.code === 0 ? data.list : [];
            if (data && data.code === 0) {
              if (data.datas.length > 0) {
                this.alert('该数据源已经使用，不可删除！');
              } else {
                let loadingInstance = Loading.service({
                  lock: true,
                  text: 'Loading',
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.7)'
                });
                this.$http({
                  url: this.$http.adornUrl('/warning/warningdatasource/deleteALL'),
                  method: 'post',
                  data: this.$http.adornParams(val)
                }).then(({data}) => {
                  if (data && data.code === 0) {
                    this.$message({
                      type: 'success',
                      message: '删除成功!'
                    });
                    this.getQueryList()
                  } else {
                    this.$message({
                      type: 'success',
                      message: '删除失败!'
                    });
                  }
                  loadingInstance.close()
                }).catch(() => {
                  loadingInstance.close()
                  this.$message({
                    type: 'success',
                    message: '删除失败!'
                  });
                });
              }
            } else {
              this.alert('删除失败！');
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          })
        });
      },
      // ================================================================新增添加数据  开关
      addDataSource() {
        localStorage.removeItem('params:roleDatasouceUpdate');
        this.$router.push({name: 'roleDatasouceUpdate'});
      },
      // ================================================================修改
      preview(item) {
        this.$router.push({name: 'roleDatasouceUpdate', params: {data: item}});
      },
      // =========================================================分页
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
      }
    }
  }
</script>

<style scoped>

</style>

