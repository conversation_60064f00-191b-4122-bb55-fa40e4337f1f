package com.bonc.rrs.workManager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.branchbalance.service.WorderUsedMaterielService;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.gace.service.GacePushService;
import com.bonc.rrs.honeywell.entity.FinishDispatchRequest;
import com.bonc.rrs.honeywell.service.HoneywellBizService;
import com.bonc.rrs.invoice.enterprises.util.StorageBusiness;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.util.ExcelUtile;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.WorderDataManagerDao;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dto.vo.ExtFileVo;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderapp.constant.Constant;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.bonc.rrs.worderapp.service.WorderOrderService;
import com.bonc.rrs.workManager.dao.AutidOrderMapper;
import com.bonc.rrs.workManager.dao.AutidResultMapper;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.entity.WorderFieldId;
import com.bonc.rrs.workManager.entity.vo.DotPositionAddedMaterielStockVo;
import com.bonc.rrs.workManager.entity.vo.ReceiverInfoVo;
import com.bonc.rrs.workManager.entity.vo.RegionCodeVo;
import com.bonc.rrs.workManager.entity.vo.UsedMaterielVo;
import com.bonc.rrs.workManager.service.AutidOrderService;
import com.common.pay.common.utils.DateUtil;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author：MinJunping
 * @date：2020/03/20
 */
@Service
@Slf4j
public class AutidOrderServiceImpl implements AutidOrderService {

    @Autowired(required = false)
    private AutidOrderMapper autidOrderMapper;

    @Autowired(required = false)
    private AutidResultMapper autidResultMapper;

    @Autowired(required = false)
    private WorkMsgDao workMsgDao;

    @Autowired(required = false)
    private SysFilesMapper sysFilesMapper;

    @Autowired(required = false)
    private WorderDataManagerDao worderDataManagerDao;
    @Autowired
    private FlowCommon flowCommon;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private ProviderBusinessService providerBusinessService;

    @Autowired(required = false)
    private WorderOrderService worderOrderService;

    @Autowired(required = false)
    private ICaApiService caApiService;

    @Autowired(required = false)
    private WorderUsedMaterielService worderUsedMaterielService;
    @Autowired(required = false)
    private WorderExtFieldService worderExtFieldService;
    @Autowired
    private HoneywellBizService honeywellBizService;
    @Autowired
    private WorderInformationAttributeDao worderInformationAttributeDao;

    @Autowired
    private GacePushService gacePushService;
    @Autowired
    private SysUserService sysUserService;


    @Override
    public Results getOrderAuditInfo(String worderNo, Integer purpose) {

        try {
            if (!StringUtils.isEmpty(worderNo) && !StringUtils.isEmpty(purpose)) {
                List<Map<String, Object>> textList = autidOrderMapper.selectFieldValue(worderNo, purpose, 1);
                List<Map<String, Object>> selectList = autidOrderMapper.selectFieldValue(worderNo, purpose, 2);
                List<Map<String, Object>> pictureList = autidOrderMapper.selectFieldValue(worderNo, purpose, 3);
                List<Map<String, Object>> fileList = autidOrderMapper.selectFieldValue(worderNo, purpose, 4);
                List<Map<String, Object>> danSelect = autidOrderMapper.selectFieldValue(worderNo, purpose, 6);
                List<Map<String, Object>> duoSelect = autidOrderMapper.selectFieldValue(worderNo, purpose, 7);
                List<Map<String, Object>> dateList = autidOrderMapper.selectFieldValue(worderNo, purpose, 8);
//                List<Map<String,Object>> view = autidOrderMapper.selectFieldValue(worderNo, purpose, 11);
//                List<Map<String,String>> areaList = autidOrderMapper.selectFieldValue(worderNo, purpose, 5);
                List<Map<String, Object>> pictlist = new ArrayList<>();
                List<Map<String, Object>> wjlist = new ArrayList<>();
                List<Map<String, Object>> viewlist = new ArrayList<>();
                List<Map<String, Object>> text = autidOrderMapper.selectFieldValue(worderNo, purpose, 12);
                textList.addAll(text);

                if (pictureList.size() != 0) {
                    for (Map<String, Object> map : pictureList) {
                        Map<String, Object> stringMap = new HashMap<>();
                        //String url = sysFilesMapper.selectSysFileInfo(Integer.valueOf(map.get("fieldValue").toString()));
//                        String url = FileUtils.getInfoUrl(map.get("fieldValue"));
                        //图片回显处理
                        String fileIds = null;
                        if (!StringUtils.isEmpty(map.get("fieldValue"))) {
                            fileIds = map.get("fieldValue").toString();
                        }
                        if (!StringUtils.isEmpty(fileIds)) {
                            String[] file = fileIds.split(",");
                            List<String> imageUrlList = new ArrayList<>();
                            List<String> fileUrlList = Arrays.stream(file).map(fileId -> {
                                String fileUrl = "";
                                String imageUrl = "";
                                if ("null".equalsIgnoreCase(fileId)) {
                                } else {
                                    List<SysFileEntity> sysFiles = sysFilesMapper.listSysFiles(fileId);
                                    if (sysFiles.size() > IntegerEnum.ZERO.getValue() && StringUtils.isNotBlank(sysFiles.get(IntegerEnum.ZERO.getValue()).getPath())) {
                                        SysFileEntity sysFileEntity = sysFiles.get(IntegerEnum.ZERO.getValue());
                                        String bucketName = sysFileEntity.getPath().replace("http://", "").replace("https://", "")
                                                .split("\\.")[IntegerEnum.ZERO.getValue()];
                                        imageUrl = sysFileEntity.getNewName() + "&bucketName=" + bucketName + "," + sysFileEntity.getPath();
                                        fileUrl = sysFileEntity.getPath();
                                        imageUrlList.add(imageUrl);
                                    }
                                }
                                return fileUrl;
                            }).collect(Collectors.toList());
                            fileUrlList.removeIf(Objects::isNull);
                            stringMap.put("fieldValue", fileUrlList);
                            stringMap.put("fieldImageValue", imageUrlList);
                        }
                        //stringMap.put("fieldValue",url);
                        stringMap.put("fieldName", map.get("fieldName").toString());
                        stringMap.put("fieldId", map.get("fieldId"));
                        stringMap.put("checkStatus", map.get("checkStatus"));
                        pictlist.add(stringMap);
                    }
                }

                if (fileList.size() != 0) {
                    for (Map<String, Object> map : fileList) {
                        Map<String, Object> stringMap = new HashMap<>();
                        //String url = sysFilesMapper.selectSysFileInfo(Integer.valueOf(map.get("fieldValue").toString()));
//                        String url = FileUtils.getInfoUrl(map.get("fieldValue"));
                        //文件回显处理
                        String fileIds = null;
                        if (!StringUtils.isEmpty(map.get("fieldValue"))) {
                            fileIds = map.get("fieldValue").toString();
                        }
                        List<String> fileUrlList = new ArrayList<>();
                        List<ExtFileVo> extFileVos = new ArrayList<>();
                        if (!StringUtils.isEmpty(fileIds)) {
                            String[] file = fileIds.split(",");
                            extFileVos = Arrays.stream(file).map(fileId -> {
                                String fileUrl = "";
                                ExtFileVo extFileVo = new ExtFileVo();
                                if ("null".equalsIgnoreCase(fileId)) {
                                } else {
                                    //fileUrl = sysFilesMapper.selectSysFileInfo(Integer.valueOf(fileId));
                                    extFileVo = worderDataManagerDao.getFileUrl(fileId);
                                }
                                //return fileUrl;
                                return extFileVo;
                            }).collect(Collectors.toList());
                        }
                        //stringMap.put("fieldValue",fileUrlList);
                        extFileVos.removeIf(Objects::isNull);
                        stringMap.put("fieldValue", extFileVos);
                        //stringMap.put("fieldValue",url);
                        stringMap.put("fieldName", map.get("fieldName").toString());
                        stringMap.put("fieldId", map.get("fieldId"));
                        stringMap.put("checkStatus", map.get("checkStatus"));
                        wjlist.add(stringMap);
                    }
                }

//                if (view.size() != 0){
//                    for (Map<String,Object> map : view) {
//                        Map<String,Object> stringMap = new HashMap<>();
//                        String url = sysFilesMapper.selectSysFileInfo(Integer.valueOf(map.get("fieldValue").toString()));
////                        String url = FileUtils.getInfoUrl(map.get("fieldValue"));
//                        stringMap.put("fieldValue",url);
//                        stringMap.put("fieldName",map.get("fieldName").toString());
//                        stringMap.put("fieldId",map.get("fieldId"));
//                        viewlist.add(stringMap);
//                    }
//                }

                Map<String, Object> objectMap = new HashMap<>();
//                if (textList.size() != 0) {
                objectMap.put("text", textList);
//                }
//                if (selectList.size() != 0) {
                objectMap.put("select", selectList);
//                }
                objectMap.put("picture", pictlist);
                objectMap.put("file", wjlist);
                objectMap.put("odd", danSelect);
                objectMap.put("much", duoSelect);
                objectMap.put("view", viewlist);
                objectMap.put("datePicker", dateList);
//                if (areaList.size() != 0) {
//                    objectMap.put("area", areaList);
//                }
                return Results.message(0, "success", objectMap);
            } else {
                return Results.message(100, "参数不能为空", null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Results.message(110, "信息审核查询接口异常", null);
        }
    }

    @Override
    public void downExcelAuditText(String worderNo, Integer purpose,
                                   HttpServletResponse response) {

        if (!StringUtils.isEmpty(worderNo) && !StringUtils.isEmpty(purpose)) {
            List<Map<String, Object>> textList = autidOrderMapper.selectFieldValue(worderNo, purpose, 1);
            List<Map<String, Object>> selectList = autidOrderMapper.selectFieldValue(worderNo, purpose, 2);
            List<Map<String, Object>> danSelect = autidOrderMapper.selectFieldValue(worderNo, purpose, 6);
            List<Map<String, Object>> duoSelect = autidOrderMapper.selectFieldValue(worderNo, purpose, 7);
            for (int i = 0; i < selectList.size(); i++) {
                Map<String, Object> filevalue = selectList.get(i);
                if (!StringUtils.isEmpty(filevalue.get("valueUp"))) {
                    filevalue.replace("fieldValue", filevalue.get("valueUp"));
                    textList.add(filevalue);
                }
            }

            for (int i = 0; i < danSelect.size(); i++) {
                Map<String, Object> filevalue = danSelect.get(i);
                if (!StringUtils.isEmpty(filevalue.get("valueUp"))) {
                    filevalue.replace("fieldValue", filevalue.get("valueUp"));
                    textList.add(filevalue);
                }
            }

            for (int i = 0; i < duoSelect.size(); i++) {
                Map<String, Object> filevalue = duoSelect.get(i);
                if (!StringUtils.isEmpty(filevalue.get("valueUp"))) {
                    filevalue.replace("fieldValue", filevalue.get("valueUp"));
                    textList.add(filevalue);
                }
            }

            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
            String datStr = df.format(new Date());// new Date()为获取当前系统时间
            HSSFWorkbook workbook = new HSSFWorkbook();
            String excelName = "";
            if (purpose == 2) {
                excelName = "勘测报告信息";
            } else if (purpose == 3) {
                excelName = "安装报告信息";
            }
            HSSFSheet sheet = workbook.createSheet(excelName);
            ExcelUtile.excelUtil(workbook, sheet, textList, 18, datStr);
            BufferedOutputStream fos = null;
            try {
                response.setContentType("application/octet-stream");
                //这后面可以设置导出Excel的名称，此例中名为kpi.xls
                String name = null;
                String fileName = datStr + excelName + ".xls";//创建文件名
                fileName = URLEncoder.encode(fileName, "UTF-8");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName);
                //刷新缓冲
                response.flushBuffer();
                //workbook将Excel写入到response的输出流中，供页面下载
//            workbook.write(response.getOutputStream());
                fos = new BufferedOutputStream(response.getOutputStream());
                workbook.write(fos);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (fos != null) {
                    try {
                        //fos.flush();
                        fos.close();
                    } catch (IOException e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }
        }

    }

    /**
     * 审核勘测资料和安装资料不通过
     *
     * @param worderNo
     * @param result
     * @param purpose
     * @param username
     * @return
     */

    @Override
    public Results autidInfoResult(String worderNo, String result,
                                   Integer purpose, String username,
                                   List<WorderFieldId> worderFieldIds) {

        try {
            if (!StringUtils.isEmpty(worderNo) && !StringUtils.isEmpty(result) && !StringUtils.isEmpty(purpose)) {
                Integer userId = workMsgDao.selectUserId(username);
                List<Integer> roleIdlist = workMsgDao.selectRoleId(userId);
                int num = 0;
                int nu = 0;
                int nums = 0;

                for (int i = 0; i < roleIdlist.size(); i++) {
                    if (roleIdlist.get(i) == 2 || roleIdlist.get(i) == 1 || roleIdlist.get(i) == 38 || roleIdlist.get(i) == 83) {
                        num = 2;
                    }
                    if (roleIdlist.get(i) == 3 || roleIdlist.get(i) == 1 || roleIdlist.get(i) == 38 || roleIdlist.get(i) == 83) {
                        nu = 3;
                    }
                }

                if (num != 2 && nu != 3) {
                    return Results.message(1, "用户没有权限", null);
                }

                int memeber = 0;


                OperationRecord operationRecord = new OperationRecord((long) userId, username, result, worderNo);
                Integer worderExecStatus = autidOrderMapper.getWorderExecStatus(worderNo);  //工单执行状态
                //查询工单状态
                Integer worderStatus = autidOrderMapper.getWorderStatus(worderNo);
                operationRecord.setWorderExecStatus(String.valueOf(worderExecStatus));
                operationRecord.setWorderStatus(String.valueOf(worderStatus));
                if (worderExecStatus == 5 || worderExecStatus == 13 || worderExecStatus == 8 || worderExecStatus == 16 || worderExecStatus == 7 || worderExecStatus == 15) {
                    //如果执行状态是待审核或者是车企退回，则按照正常逻辑走
                    if (purpose == 2) {
                        if (flowCommon.hasFlowByWorderNo(worderNo)) {
                            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.SurvDoc2Audit, FlowConstant.ProcessStatus.N);
                            // 流程调用失败直接返回
                            if (!"0".equals(executeFlowResultPo.getCode())) {
                                return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                            }
                            memeber = autidResultMapper.updateWorderTime(worderNo);
                        } else {
                            operationRecord.setWorderExecStatus("6");
                            memeber = autidResultMapper.updataWorderInformation(worderNo, 6, "勘测资料整改中");
                        }
                    } else if (purpose == 3) {
                        if (flowCommon.hasFlowByWorderNo(worderNo)) {
                            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.FixDoc2Audit, FlowConstant.ProcessStatus.N);
                            if (!"0".equals(executeFlowResultPo.getCode())) {
                                return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                            }
                            memeber = autidResultMapper.updateWorderTime(worderNo);
                        } else {
                            operationRecord.setWorderExecStatus("14");
                            memeber = autidResultMapper.updataWorderInformation(worderNo, 14, "安装资料整改中");
                        }
                    }
                    nums = workMsgDao.insertOperation(operationRecord);
                } else {
                    //如果执行状态不是待审核，则不改变执行状态，记录审核结果
                    memeber = 1;
                    nums = workMsgDao.insertOperation(operationRecord);
                }

                if (nums > 0 && memeber > 0) {
                    //autidOrderMapper.updateBatchWorderField(worderNo,purpose,worderFieldIds);
                    worderFieldIds.stream().forEach(o -> {
                        autidOrderMapper.updateFieldStatus(worderNo, o.getFieldId(), o.getState());
                    });
                    //除了审核不通过的字段信息，其它的是审核通过的
                    List<Integer> noPassFieldId = worderFieldIds.stream().map(WorderFieldId::getFieldId).collect(Collectors.toList()); //审核未通过的id
                    List<Integer> fieldIds = autidOrderMapper.getFieldIds(worderNo, purpose);  //获取某个工单的所有要审核的字段id
                    //去除审核不通过的id，剩下的为审核通过的id，两个集合取差集
                    List<Integer> passFieldId = fieldIds.stream().filter(fieldId -> !noPassFieldId.contains(fieldId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(passFieldId)) {
                        passFieldId.stream().forEach(fieldId -> {
                            autidOrderMapper.updateFieldStatus(worderNo, fieldId, 1);
                        });
                    }
                    Results results = Results.message(0, "success", null);
                    if (purpose == 2 && worderExecStatus == 5) {
                        // 勘测资料服务经理审核不通过
                        results.putWorderNo(worderNo).putWorderExecStatus(FieldConstant.CONVEY_REFORM)
                                .putWorderTriggerEvent(WarningConstant.CONVEY_MANAGER_AUDIT_NOT_PASS);
                    } else if (purpose == 3 && worderExecStatus == 13) {
                        // 安装资料服务经理审核不通过
                        results.putWorderNo(worderNo).putWorderExecStatus(FieldConstant.INSTALL_REFORM)
                                .putWorderTriggerEvent(WarningConstant.INSTALL_MANAGER_AUDIT_NOT_PASS);
                    }
                    return results;
                } else {
                    return Results.message(2, "更新数据失败", null);
                }
            } else {
                return Results.message(100, "参数不能为空", null);
            }
        } catch (Exception e) {
            return Results.message(110, "审核资料接口异常", null);
        }
    }

    private String getNum(Integer integer) {
        if (integer > 999999) {
            throw new RuntimeException();
        }
        return String.format("%06d", integer); //25为int型
    }

    private String getLeaveCodeAndTime() {
        Date date = new Date();
        String currDate = DateUtil.dateToString(date, DateUtil.LONG_DATE_FORMAT);
        String moveCodeAndTime = getNum(autidOrderMapper.getOutMoveCodeAndTime(currDate));
        return "OUT-" + DateUtil.dateToString(date, DateUtil.FORMAT_FOUR) + "-" + moveCodeAndTime;
    }

    private ReceiverInfoVo getReceiverInfo(Integer worderId) {
        ReceiverInfoVo receiverInfo = autidOrderMapper.getReceiverInfo(worderId);
        List<RegionCodeVo> maps = autidOrderMapper.selectReginCode();
        Integer regcode = null;
        String v = null;
        if (StringUtils.isNotBlank(receiverInfo.getAddressDup())) {
            String value = receiverInfo.getAddressDup();
            String[] region = value.split("_");
            String[] regions = Arrays.copyOfRange(region, 0, 2);
            for (String i : regions) {
                for (RegionCodeVo regionCodeVo : maps) {
                    if (i.equals(regionCodeVo.getName())) {
                        Integer reg = regionCodeVo.getId();
                        regcode = reg;
                        if (v != null) {
                            v = v + regcode;
                        } else {
                            v = regcode + ",";
                        }
                        break;
                    }
                }

            }
            receiverInfo.setAddressDup(v + "," + region[2] + region[3]);
        }
        return receiverInfo;
    }

    private JSONObject getGoodsInfo(UsedMaterielVo usedMaterielVo, DotPositionAddedMaterielStockVo dotPositionAddedMaterielStockVo) {
        JSONObject goods = new JSONObject();
        goods.put("exNum", usedMaterielVo.getUsedNum());
        goods.put("expectOutNum", usedMaterielVo.getUsedNum());
        goods.put("goodsId", usedMaterielVo.getMaterielId());
        goods.put("goodsType", 4);
        goods.put("leaveNum", usedMaterielVo.getUsedNum());
        goods.put("storePositionId", dotPositionAddedMaterielStockVo.getStorePositionId());
        return goods;
    }

    private JSONObject getLeaveStoreBody(Integer worderId, String worderNo, String leaveNum, Integer storeId) {
        ReceiverInfoVo receiverInfo = getReceiverInfo(worderId);

        JSONObject body = new JSONObject();

        body.put("address", receiverInfo.getAddressDup());
        body.put("fromName", receiverInfo.getUserName());
        body.put("isManufacturerInStorage", "N");
        body.put("leaveNum", leaveNum);
        body.put("outScenario", "0");
        body.put("phone", receiverInfo.getUserPhone());
        body.put("storeId", storeId);
        body.put("worderId", worderNo);
        return body;
    }

    /**
     * 获取用户
     *
     * @return
     */
    public SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    /**
     * 审核勘探和安装信息通过
     *
     * @param worderNo
     * @param result
     * @param purpose  2勘测 3安装
     * @param username
     * @return
     */
    @Override
    public Results autidApproveInfo(String worderNo, String result, Integer purpose, String username) {

        try {

            SysUserEntity userEntity = sysUserService.getOne(new QueryWrapper<SysUserEntity>().eq("username", username));
            // Integer userId = workMsgDao.selectUserId(username);
            Integer userId = Math.toIntExact(userEntity.getUserId());

            List<Integer> roleIdlist = workMsgDao.selectRoleId(userId);

            int num = 0;
            int nu = 0;
            int nums = 0;
            int type = 0;

            for (int i = 0; i < roleIdlist.size(); i++) {
                // 客服经理
                if (roleIdlist.get(i) == 2 || roleIdlist.get(i) == 1 || roleIdlist.get(i) == 38 || roleIdlist.get(i) == 83) {
                    num = 2;
                }
                // 服务经理
                if (roleIdlist.get(i) == 3 || roleIdlist.get(i) == 1 || roleIdlist.get(i) == 38 || roleIdlist.get(i) == 83) {
                    nu = 3;
                }
            }


            if (num != 2 && nu != 3) {
                return Results.message(1, "用户没有权限", null);
            }

            // 客服经理
            if (num == 2) {
                type = 1;
            }

            // 服务经理
            if (nu == 3) {
                type = 0;
            }

            WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));

//            Integer status = autidOrderMapper.getWorderExecStatus(worderNo);  //工单执行状态
            Integer status = worder.getWorderExecStatus();

            if (nu == 3 && num == 2) {
                // 服务经理 13 安装资料已提交待审核
                if (status.intValue() == 5 || status.intValue() == 13) {
                    type = 0;
                    // 客服经理 15 安装资料待客服确认
                } else if (status.intValue() == 7 || status.intValue() == 15) {
                    type = 1;
                }
            }

//        int index = autidResultMapper.getCountAutidResult(worderNo, purpose);

//        AutidResult autidResult = new AutidResult(purpose,worderNo,username,userId,result,type);
            int memeber = 0;
            String worderStatus = null;
            String worderExecStatus = null;
            if (type == 0 && purpose == 2 && status.intValue() == 5) {
                if (flowCommon.hasFlowByWorderNo(worderNo)) {
                    //调用勘测资料待客服确认流程
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.SurvDoc2Audit, FlowConstant.ProcessStatus.Y);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                    }
                    memeber = autidResultMapper.updateWorderTime(worderNo);
                } else {
                    memeber = autidResultMapper.updataWorderInformation(worderNo, 7, "勘测资料客服待确认");
                }
                worderStatus = Constant.CONVEY;
                worderExecStatus = Constant.CONVEY_SERVICE_NOT_AUDIT;
            } else if (type == 1 && purpose == 2 && status.intValue() == 7) {
                if (flowCommon.hasFlowByWorderNo(worderNo)) {
                    //调用勘测资料无误待上传车企流程
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.SurvDoc2Audit, FlowConstant.ProcessStatus.Y);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                    }
                    memeber = autidResultMapper.updateWorderTime(worderNo);
                } else {
                    memeber = autidResultMapper.updataWorderInformation(worderNo, 8, "勘测资料无误待上传车企");
                }
//                autidResultMapper.updateWorderMajorState(worderNo, 2, "安装中");
                worderStatus = Constant.CONVEY;
                worderExecStatus = Constant.CONVEY_NOT_UPLOAD_COMPANY;
            } else if (type == 0 && purpose == 3 && status.intValue() == 13) {
                if (flowCommon.hasFlowByWorderNo(worderNo)) {
                    //调用安装资料无误待上传车企
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.FixDoc2Audit, FlowConstant.ProcessStatus.Y);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                    }
                    memeber = autidResultMapper.updateWorderTime(worderNo);
                } else {
                    memeber = autidResultMapper.updataWorderInformation(worderNo, 15, "安装资料待客服确认");
                }
                worderStatus = Constant.INSTALL;
                worderExecStatus = Constant.INSTALL_SERVICE_NOT_AUDIT;
                // 安装 客服经理审核
            } else if (type == 1 && purpose == 3 && status.intValue() == 15) {

                // TODO: 安装信息信息回传
                if (worder.getWorderTypeId() == 5) {
                    //回传联系信息
                    Result r1 = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushContactInfo");
//                    if(!r1.getCode().equals(0)){
//                        return Results.message(r1.getCode(),r1.getMsg());
//                    }
                    Boolean rollback = worderOrderService.isRollback(worderNo);
                    //如果没有被车企打回过
                    if (!rollback) {
                        //回传勘测信息
                        Result r2 = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushSurveyInfo");
                        if (!r2.getCode().equals(0)) {
                            return Results.message(r2.getCode(), r2.getMsg());
                        }
                    }

                    // 服务商安装信息回传
                    Result pushInstallationInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushInstallationInfo");
                    if (!pushInstallationInfoResult.getCode().equals(0)) {
                        return Results.message(pushInstallationInfoResult.getCode(), pushInstallationInfoResult.getMsg());
                    }
                    // 安装附件信息回传
                    Result pushAccessoriesInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushAccessoriesInfo");
                    if (!pushAccessoriesInfoResult.getCode().equals(0)) {
                        return Results.message(pushAccessoriesInfoResult.getCode(), pushAccessoriesInfoResult.getMsg());
                    }
                    //服务商提交审核
                    Result pushSubmitReviewInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).operator(userEntity.getEmployeeName()).build(), "pushSubmitReviewInfo");
                    if (!pushSubmitReviewInfoResult.getCode().equals(0)) {
                        return Results.message(pushSubmitReviewInfoResult.getCode(), pushSubmitReviewInfoResult.getMsg());
                    }
                    List<WorderExtFieldEntity> fields = worderExtFieldService.getFieldsByWorderNo(worderNo);

                    WorderInformationAttributeEntity attributeEntity =
                            worderInformationAttributeDao.selectAttributeByWorderNo(
                                    worder.getWorderNo(),
                                    "push_dataCheck", "ca"
                            );
                    if (attributeEntity != null && attributeEntity.getAttributeValue().equals("0")) {
                        try {
                            //调用长安接口
                            CaApiResponse response = caApiService.pushInstallDataCheck(worder.getWorderNo(), worder.getCompanyOrderNumber(), fields);
                            if (!response.getSuccess() && !response.getMessage().contains("不允许再次上传安装资料")) {
                                return Results.message(Integer.valueOf(response.getCode()), response.getMessage());
                            }
                        } catch (IOException e) {
                            return Results.message(500, "调用长安接口异常");
                        }
                        attributeEntity.setAttributeValue("1");
                        worderInformationAttributeDao.updateById(attributeEntity);
                    }
                    try {
                        FinishDispatchRequest request = new FinishDispatchRequest();
                        long currentTimeStamp = System.currentTimeMillis();
                        request.setTimestamp(currentTimeStamp);
                        request.setOrderCode(worder.getCompanyOrderNumber());
                        request.setFinishTime(currentTimeStamp);
                        String pileCode = fields.stream().filter(f -> f.getFieldId().equals(950)).findFirst().map(WorderExtFieldEntity::getFieldValue).orElse(null);
                        request.setChargingStationCode(pileCode);
                        request.setRemark("");
                        honeywellBizService.finishDispatch(request);
                    } catch (Exception e) {
                        return Results.message(500, "调用霍尼韦尔接口异常");
                    }
                }

                // TODO: 如果是比亚迪下的单子并且是维修，先调CPIM 服务商售后信息回传接口
                if (worder.getWorderTypeId() == 6) {

                    Integer templateId = worder.getTemplateId();
                    //历史维修单templateId = (476,175,243,475,471,472,479,698)
                    if (templateId == 476 || templateId == 175 || templateId == 243 || templateId == 475 || templateId == 471 || templateId == 472 || templateId == 479 || templateId == 698) {

                        Result pushProcessingResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushProcessing");
                        if (!pushProcessingResult.getCode().equals(0)) {
                            return Results.message(pushProcessingResult.getCode(), pushProcessingResult.getMsg());
                        }

                        // 报修附件信息回传
                        Result pushAccessoriesInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushRepairAccessoriesInfo");
                        if (!pushAccessoriesInfoResult.getCode().equals(0)) {
                            return Results.message(pushAccessoriesInfoResult.getCode(), pushAccessoriesInfoResult.getMsg());
                        }
                    }else{
                        // 报修信息(重构)回传
                        Result pushAccessoriesInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).build(), "pushRepairAccessoriesInfoNew");
                        if (!pushAccessoriesInfoResult.getCode().equals(0)) {
                            return Results.message(pushAccessoriesInfoResult.getCode(), pushAccessoriesInfoResult.getMsg());
                        }
                    }
                    //服务商报修订单审核信息回传
                    Result pushReviewInfoResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).operator(userEntity.getEmployeeName()).build(), "pushReviewInfo");
                    if (!pushReviewInfoResult.getCode().equals(0)) {
                        return Results.message(pushReviewInfoResult.getCode(), pushReviewInfoResult.getMsg());
                    }
                }

                try {
                    gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.COMPLETED, "安装完成");
                } catch (Exception gaceE) {
                    return Results.message(500, "调用广汽接口异常："+gaceE.getMessage());
                }

                if (flowCommon.hasFlowByWorderNo(worderNo)) {
                    //调用安装资料无误待上传车企
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.FixDoc2Audit, FlowConstant.ProcessStatus.Y);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                    }
                    memeber = autidResultMapper.updateWorderTime(worderNo);
                } else {
                    memeber = autidResultMapper.updataWorderInformation(worderNo, 16, "安装资料待上传车企");
                }
                autidResultMapper.updateConfirmTime(worderNo);  //更新客服确认安装完成时间
//                autidResultMapper.updateWorderMajorState(worderNo, 3, "结算中");
                worderStatus = Constant.INSTALL;
                worderExecStatus = Constant.INSTALL_NOT_UPLOAD_COMPANY;
            } else {
                return Results.message(1, "工单已不在该状态或者没有权限审核", null);
            }
            if (purpose == 2) {
                result = username + "勘测资料审核通过";
            } else {
                result = username + "安装资料审核通过";
            }

            OperationRecord operationRecord = new OperationRecord((long) userId, username, result, worderNo);
            operationRecord.setWorderStatus(worderStatus);
            operationRecord.setWorderExecStatus(worderExecStatus);
            nums = workMsgDao.insertOperation(operationRecord);

//        if (index > 0 && memeber > 0){
//
//            nums = autidResultMapper.updateByPrimaryKeySelective(autidResult);
//
//        }else {
//
//            nums = autidResultMapper.insertSelective(autidResult);
//
//        }

            if (nums > 0 && memeber > 0) {
                autidOrderMapper.updateWorderFieldState(worderNo, purpose, 1);
                Results results = Results.message(0, "success", null);
                if (purpose == 2 && type == 0) {
                    // 勘测资料服务经理审核通过
                    results.putWorderNo(worderNo).putWorderExecStatus(FieldConstant.CONVEY_SERVICE_NOT_AUDIT)
                            .putWorderTriggerEvent(WarningConstant.CONVEY_MANAGER_AUDIT_PASS);
                } else if (purpose == 3 && type == 0) {
                    // 安装资料服务经理审核通过
                    results.putWorderNo(worderNo).putWorderExecStatus(FieldConstant.INSTALL_SERVICE_NOT_AUDIT)
                            .putWorderTriggerEvent(WarningConstant.INSTALL_MANAGER_AUDIT_PASS);
                }
                return results;
            } else {
                return Results.message(2, "更新数据失败", null);
            }
        } catch (Exception e) {
            log.error("审核通过资料信息接口异常", e);
            return Results.message(110, "审核通过资料信息接口异常", null);
        }
    }

    /**
     * 小咖接口审核勘探和安装信息通过
     *
     * @param worderNo
     * @return
     */
    @Override
    public OtherApiResponse autidApproveInfoXk(String worderNo) {
        try {
            WorderInformationEntity worder = worderInformationService.getOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
            Integer status = worder.getWorderExecStatus();

            int memeber = 0;
            int nums = 0;
            String worderStatus = null;
            String worderExecStatus = null;
            if (status.intValue() == 15) {
                if (flowCommon.hasFlowByWorderNo(worderNo)) {
                    //调用安装资料无误待上传车企
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.FixDoc2Audit, FlowConstant.ProcessStatus.Y);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return OtherApiResponse.error(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg());
                    }
                    memeber = autidResultMapper.updateWorderTime(worderNo);
                } else {
                    memeber = autidResultMapper.updataWorderInformation(worderNo, 16, "安装资料待上传车企");
                }
                autidResultMapper.updateConfirmTime(worderNo);  //更新客服确认安装完成时间
                worderStatus = Constant.INSTALL;
                worderExecStatus = Constant.INSTALL_NOT_UPLOAD_COMPANY;
            } else {
                return OtherApiResponse.error(1, "工单已不在该状态");
            }

            String result = "小咖安装资料审核通过";

            OperationRecord operationRecord = new OperationRecord((long) 89, "小咖", result, worderNo);
            operationRecord.setWorderStatus(worderStatus);
            operationRecord.setWorderExecStatus(worderExecStatus);
            nums = workMsgDao.insertOperation(operationRecord);
            if (nums > 0 && memeber > 0) {
                autidOrderMapper.updateWorderFieldState(worderNo, 3, 1);
                return OtherApiResponse.ok();
            } else {
                return OtherApiResponse.error(2, "更新数据失败");
            }
        } catch (Exception e) {
            log.error("审核通过资料信息接口异常", e);
            return OtherApiResponse.error(110, "审核通过资料信息接口异常");
        }
    }


    @Override
    public Results selectAutidInfolist(String worderNo, Integer purpose) {
        try {
            List<Map<String, Object>> text = autidOrderMapper.selectFieldValue(worderNo, purpose, 1);
            List<Map<String, Object>> select = autidOrderMapper.selectFieldValue(worderNo, purpose, 2);
            List<Map<String, Object>> picture = autidOrderMapper.selectFieldValue(worderNo, purpose, 3);
            List<Map<String, Object>> file = autidOrderMapper.selectFieldValue(worderNo, purpose, 4);
            List<Map<String, Object>> odd = autidOrderMapper.selectFieldValue(worderNo, purpose, 6);
            List<Map<String, Object>> much = autidOrderMapper.selectFieldValue(worderNo, purpose, 7);
            List<Map<String, Object>> datePicker = autidOrderMapper.selectFieldValue(worderNo, purpose, 8);
            List<Map<String, Object>> textList = autidOrderMapper.selectFieldValue(worderNo, purpose, 12);
            text.addAll(textList);
            List<Object> mapList = new ArrayList<>();

            Map<String, Object> objectMap = new HashMap<>();

            List<Map<String, Object>> pictlist = new ArrayList<>();
            List<Map<String, Object>> wjlist = new ArrayList<>();

            if (picture.size() != 0) {
                for (Map<String, Object> map : picture) {
                    Map<String, Object> stringMap = new HashMap<>();
                    //String url = sysFilesMapper.selectSysFileInfo(Integer.valueOf(map.get("fieldValue").toString()));
                    //                        String url = FileUtils.getInfoUrl(map.get("fieldValue"));

                    //图片回显处理
                    String fileIds = null;
                    if (!StringUtils.isEmpty(map.get("fieldValue"))) {
                        fileIds = map.get("fieldValue").toString();
                    }
                    if (!StringUtils.isEmpty(fileIds)) {
                        String[] ids = fileIds.split(",");
                        List<ExtFileVo> fileUrlList = Arrays.stream(ids).map(fileId -> {
                            ExtFileVo fileVo = null;
                            String fileUrl = "";
                            if ("null".equalsIgnoreCase(fileId)) {
                            } else {
                                //fileUrl = sysFilesMapper.selectSysFileInfo(Integer.valueOf(fileId));
                                fileVo = worderDataManagerDao.getFileUrl(fileId);
                            }
                            //fileVo.setFileId(fileId);
                            //fileVo.setFileUrl(fileUrl);
                            return fileVo;
                        }).collect(Collectors.toList());
                        fileUrlList.removeIf(Objects::isNull);
                        stringMap.put("fieldValue", fileUrlList);
                    }
                    //stringMap.put("fieldValue",url);
                    stringMap.put("fieldName", map.get("fieldName"));
                    stringMap.put("fieldId", map.get("fieldId"));
                    stringMap.put("value", map.get("fieldValue"));
                    stringMap.put("imgPath", map.get("imgPath"));
                    stringMap.put("remake", map.get("remake"));
                    pictlist.add(stringMap);
                }
            }

            if (file.size() != 0) {
                for (Map<String, Object> map : file) {
                    Map<String, Object> stringMap = new HashMap<>();
                    //String url = sysFilesMapper.selectSysFileInfo(Integer.valueOf(map.get("fieldValue").toString()));
                    //                        String url = FileUtils.getInfoUrl(map.get("fieldValue"));

                    //文件回显处理
                    String fileIds = null;
                    if (!StringUtils.isEmpty(map.get("fieldValue"))) {
                        fileIds = map.get("fieldValue").toString();
                    }
                    if (!StringUtils.isEmpty(fileIds)) {
                        String[] ids = fileIds.split(",");
                        List<ExtFileVo> fileUrlList = Arrays.stream(ids).map(fileId -> {
                            ExtFileVo fileVo = null;
                            String fileUrl = "";
                            if ("null".equalsIgnoreCase(fileId)) {
                            } else {
                                //fileUrl = sysFilesMapper.selectSysFileInfo(Integer.valueOf(fileId));
                                fileVo = worderDataManagerDao.getFileUrl(fileId);
                            }
                            //fileVo.setFileId(fileId);
                            //fileVo.setFileUrl(fileUrl);
                            return fileVo;
                        }).collect(Collectors.toList());
                        fileUrlList.removeIf(Objects::isNull);
                        stringMap.put("fieldValue", fileUrlList);
                    }
                    //stringMap.put("fieldValue",url);
                    stringMap.put("fieldName", map.get("fieldName"));
                    stringMap.put("fieldId", map.get("fieldId"));
                    stringMap.put("value", map.get("fieldValue"));
                    wjlist.add(stringMap);
                }
            }

            for (Map<String, Object> map : select) {
                List<Map<String, Object>> selectInfo = autidOrderMapper.selectWorderFieldInfolist(worderNo, 2, Integer.valueOf(map.get("fieldId").toString()));

                mapList.add(selectInfo);
            }

            objectMap.put("select", mapList);

            List<Object> mapList1 = new ArrayList<>();

            for (Map<String, Object> map : odd) {
                //List<Map<String,Object>> selectInfo = autidOrderMapper.selectWorderFieldInfolist(worderNo,6,Integer.valueOf(map.get("fieldId").toString()));

                List<Map<String, Object>> fieldId = autidOrderMapper.selectOddAndMuchField(worderNo, 6, Integer.valueOf(map.get("fieldId").toString()));

                List<Map<String, Object>> selectInfo = new ArrayList<>();
                for (Map<String, Object> field : fieldId) {
                    String selectData = autidOrderMapper.getSelectData(6, Integer.valueOf(field.get("fieldId").toString()));
                    if (!StringUtils.isEmpty(selectData)) {
                        String[] detailName = selectData.split(",");
                        for (String s : detailName) {
                            Map<String, Object> fieldMap = new HashMap<>();
                            fieldMap.put("key", field.get("key"));
                            fieldMap.put("name", field.get("name"));
                            fieldMap.put("isNessary", field.get("isNessary"));
                            fieldMap.put("fieldId", field.get("fieldId"));
                            fieldMap.put("fieldValue", field.get("fieldValue"));
                            fieldMap.put("valueDup", field.get("valueDup"));
                            fieldMap.put("detailName", s);
                            fieldMap.put("number", s);
                            selectInfo.add(fieldMap);
                        }
                    }
                }
                mapList1.add(selectInfo);
            }

            objectMap.put("odd", mapList1);

            List<Object> mapList2 = new ArrayList<>();

            for (Map<String, Object> map : much) {
                //List<Map<String,Object>> selectInfo = autidOrderMapper.selectWorderFieldInfolist(worderNo,7,Integer.valueOf(map.get("fieldId").toString()));

                List<Map<String, Object>> fieldId = autidOrderMapper.selectOddAndMuchField(worderNo, 7, Integer.valueOf(map.get("fieldId").toString()));

                List<Map<String, Object>> selectInfo = new ArrayList<>();
                for (Map<String, Object> field : fieldId) {
                    String selectData = autidOrderMapper.getSelectData(7, Integer.valueOf(field.get("fieldId").toString()));
                    if (!StringUtils.isEmpty(selectData)) {
                        String[] detailName = selectData.split(",");
                        for (String s : detailName) {
                            Map<String, Object> fieldMap = new HashMap<>();
                            fieldMap.put("key", field.get("key"));
                            fieldMap.put("name", field.get("name"));
                            fieldMap.put("isNessary", field.get("isNessary"));
                            fieldMap.put("fieldId", field.get("fieldId"));
                            fieldMap.put("fieldValue", field.get("fieldValue"));
                            fieldMap.put("valueDup", field.get("valueDup"));
                            fieldMap.put("detailName", s);
                            fieldMap.put("number", s);
                            selectInfo.add(fieldMap);
                        }
                    }
                }
                mapList2.add(selectInfo);
            }

            objectMap.put("much", mapList2);

            objectMap.put("text", text);

            objectMap.put("datePicker", datePicker);

            objectMap.put("picture", pictlist);

            objectMap.put("file", wjlist);

            return Results.message(0, "success", objectMap);
        } catch (Exception e) {
            e.printStackTrace();
            return Results.message(110, "接口异常或者参数为空", null);
        }
    }

}
