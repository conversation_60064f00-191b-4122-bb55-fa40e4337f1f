package com.bonc.rrs.worder.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.worder.entity.WorderTypeEntity;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;

import java.util.List;
import java.util.Map;

/**
 * 工单类型业务层
 * <AUTHOR>
 * @date 2020/3/2310:14
 */
public interface WorderTypeService extends IService<WorderTypeEntity> {

    PageUtils queryPage(Map<String, Object> params);

    /**
     * 添加工单类型
     * @param worderType
     */
    void saveWorderType(WorderTypeEntity worderType);

    /**
     * 修改工单类型
     * @param worderType
     */
    void updateWorderType(WorderTypeEntity worderType);

    /**
     * 提供类型级别级联查询数据
     * @param pid
     * @return
     */
    List<WorderTypeEntity> queryType(Integer pid);

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    List<WorderTypeEntity> queryById(Integer id);

    /**
     * 检查工单类型修改工单状态
     * @param
     * @return
     */
    R checkWorderTypeByWorderNo(String worderNo, String event);
    R checkWorderTypeByWorderId(Integer worderId, String event);


    R selectInfo(Integer worderId);
}
