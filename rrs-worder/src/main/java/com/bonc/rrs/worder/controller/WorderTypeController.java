package com.bonc.rrs.worder.controller;

import com.bonc.rrs.worder.entity.WorderTypeEntity;
import com.bonc.rrs.worder.service.WorderTypeService;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 工单类型相关接口
 * <AUTHOR>
 * @date 2020/3/2310:30
 */
@RestController
@RequestMapping("worder/wordertype")
@Api(tags = {"工单类型相关接口"})
public class WorderTypeController {

    @Autowired
    private WorderTypeService worderTypeService;


    /**
     * 获取列表
     * @param params
     * @return
     */
    @GetMapping("/page")
    @ApiOperation(value = "获取page列表", notes = "获取page列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R page(@RequestParam Map<String,Object> params){
        PageUtils page = worderTypeService.queryPage(params);
        return R.ok().put("page",page);
    }

    /**
     * 根据id获取信息
     * @param id
     * @return
     */
    @GetMapping("/info")
    @ApiOperation(value = "获取详情", notes = "获取详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "Integer", name = "id", value = "id", required = true)
    })
    public R info(@RequestParam Integer id){
        List<WorderTypeEntity> worderTypeList = worderTypeService.queryById(id);
        return R.ok().putList(worderTypeList);
    }

    /**
     * 提供类型级别级联查询数据
     * @return
     */
    @GetMapping("/queryType")
    public R queryType(@RequestParam Integer pid){
        worderTypeService.queryType(pid);
        return R.ok();
    }


    /**
     * 工单类型新增
     * @param worderType
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增记录", notes = "新增记录")
    public R save(@RequestBody WorderTypeEntity worderType){
        worderTypeService.saveWorderType(worderType);
        return R.ok();
    }

    /**
     * 修改记录
     * @param worderType
     * @return
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改记录",notes = "修改记录")
    public R update(@RequestBody WorderTypeEntity worderType){
        worderTypeService.updateWorderType(worderType);
        return R.ok();
    }

    /**
     * 删除记录
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除记录",notes = "删除记录")
    public R delete(@RequestBody Integer id){
        worderTypeService.removeById(id);
        return R.ok();
    }


    @RequestMapping("/selectinfo")
    public R test(Integer worderId) {
        return worderTypeService.selectInfo(worderId);
    }
}
