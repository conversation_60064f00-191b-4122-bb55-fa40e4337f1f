package com.bonc.rrs.workManager.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Description:
 * @Author: liujunpeng
 * @Date: 2023/8/28 15:08
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@ToString
public class PerformanceTargerExcelProperty {
    /**
     * 序号
     */
    @ExcelProperty(index = 0)
    private Integer serialNumber;
    /**
     * 周期
     */
    @ExcelProperty(index = 1)
    private String cycle;
    /**
     * 指标名称
     */
    @ExcelProperty(index = 2)
    private String indicatorDesc;
    /**
     * 指标编码
     */
    @ExcelProperty(index = 3)
    private String indicatorId;
    /**
     * 省份
     */
    @ExcelProperty(index = 4)
    private String province;
    /**
     * 省份编码
     */
    @ExcelProperty(index = 5)
    private String provinceId;

    /**
     * 品牌名称
     */
    @ExcelProperty(index = 6)
    private String brandName;

    /**
     * 品牌ID
     */
    @ExcelProperty(index = 7)
    private String brandId;

    /**
     * 目标值
     */
    @ExcelProperty(index = 8)
    private String target;
}
