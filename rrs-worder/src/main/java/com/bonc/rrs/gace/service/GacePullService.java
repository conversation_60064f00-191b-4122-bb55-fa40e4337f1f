package com.bonc.rrs.gace.service;


import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.dto.request.WOrderQueryRequest;
import com.bonc.rrs.gace.dto.response.WOrderQueryResponse;
import com.bonc.rrs.gace.entity.WorderCheckItem;
import com.bonc.rrs.gace.enums.GaceOrderTypeEnum;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.gace.util.GaceRegionInfo;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.util.UserUtil;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.service.*;
import com.youngking.lenmoncore.common.constant.WorderTypeEnum;
import com.youngking.lenmoncore.common.entity.UpstreamSystemIdEnum;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class GacePullService {

    @Resource
    protected GaceConfig gaceConfig;
    @Resource
    protected GaceService gaceService;
    @Resource
    protected WorderIntfMessageService worderIntfMessageService;
    @Resource
    protected WorderInformationService worderInformationService;
    @Resource
    protected WorderExtFieldService worderExtFieldService;
    @Resource
    protected BrandService brandService;
    @Resource
    protected WorderCheckItemService worderCheckItemService;
    @Resource
    protected WorderTemplateService worderTemplateService;
    @Resource
    protected BizRegionService bizRegionService;
    @Resource
    protected GacePushService gacePushService;

    protected static final Pattern PATTERN = Pattern.compile("地址(?<addressStart>[^ ]|[^:]|[^：]｜[^,]｜[^，]｜[^，]｜[^;]|[^；]|[^.]|[^。])?(?<province>[^省]+省|[^自治区]+自治区|[^市]+市)?(?<city>[^市]+市|[^州]+州|[^盟]+盟)?(?<district>[^区]+区|[^县]+县|[^市]+市|[^旗]+旗)?(?<addressDetail>.*)?(?<addressEnd>[^ ]｜[^,]｜[^，]｜[^，]｜[^;]|[^；]|[^.]|[^。])");

    /**
     * 获取工单类型
     */
    public abstract GaceOrderTypeEnum getWorderType();

    /**
     * 拉取广汽维修/安装工单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param billNo    车企订单号
     */
    public void pullOrders(String startTime, String endTime, String billNo) {
        validateInput(startTime, endTime, billNo);
        WOrderQueryRequest request = createQueryRequest(startTime, endTime, billNo);
        //1.分页拉取广汽工单
        List<WOrderQueryResponse.WOrder> wOrders = new ArrayList<>();
        try {
            WOrderQueryResponse queryResponse = gaceService.queryOrderList(request);
            if (queryResponse == null || CollectionUtils.isEmpty(queryResponse.getData())) {
                log.error("未获取到广汽工单，工单类型：{}，开始时间：{}，结束时间：{}，车企订单号：{}", getWorderType().getDescription(), startTime, endTime, billNo);
                return;
            }
            Long totalPage = Math.min(queryResponse.getTotalPage(), 10);
            wOrders.addAll(queryResponse.getData());
            while (queryResponse.getPage() < totalPage) {
                request.setPageNo(Math.toIntExact(queryResponse.getPage() + 1));
                queryResponse = gaceService.queryOrderList(request);
                if (queryResponse == null || CollectionUtils.isEmpty(queryResponse.getData())) {
                    break;
                }
                wOrders.addAll(queryResponse.getData());
            }
        } catch (Exception e) {
            log.error("拉取广汽工单失败", e);
            return;
        }
        //2.筛选出待计划安排的工单
        List<WOrderQueryResponse.WOrder> auditOrders = wOrders.stream().filter(wOrder -> StringUtils.equalsIgnoreCase(wOrder.getStatus(), WorkOrderStatusEnum.WSCH.getCode())).collect(Collectors.toList());

        //3.遍历工单列表,入库-》自动派单
        auditOrders.forEach(wOrder -> {
            if (!checkIfSavedAlready(wOrder)) {
                // 保存工单信息
                saveOrder(wOrder);
            }
        });
    }

    /**
     * 保存工单信息
     *
     * @param wOrder 工单查询响应对象，包含工单相关详细信息
     */
    private void saveOrder(WOrderQueryResponse.WOrder wOrder) {
        // 创建默认登录用户
        UserUtil.createDefaultLoginUser();
        // 保存报文
        WorderIntfMessageEntity messageEntity = saveMessageEntity(wOrder);
        try {
            // 保存工单
            Pair<Long, String> worderIdNoPair = saveWorderInformation(wOrder);
            // 获取工单号
            Long worderId = worderIdNoPair.getFirst();
            String worderNo = worderIdNoPair.getSecond();
            // 更新消息实体中的工单ID
            worderIntfMessageService.updateWorderIdById(messageEntity.getId(), Math.toIntExact(worderId));

            // 工单检查项
            List<WorderCheckItem> checkItems = createCheckItems(wOrder, worderId);
            worderCheckItemService.saveBatch(checkItems);

            //自动派单
            autoSendWorder(worderNo);
            //同步修改车企工单状态
            updateWorderStatus(worderNo);
        } catch (Exception e) {
            handleSaveOrderException(wOrder, messageEntity, e);
        }
    }

    private void validateInput(String startTime, String endTime, String billNo) {
        if (StringUtils.isNoneBlank(startTime, endTime)) {
            try {
                Date start = DateUtil.parse(startTime, DatePattern.NORM_DATETIME_FORMAT);
                Date end = DateUtil.parse(endTime, DatePattern.NORM_DATETIME_FORMAT);
            } catch (Exception e) {
                throw new RRException("起止时间格式错误");
            }
        } else if (StringUtils.isBlank(billNo)) {
            throw new RRException("车企订单号格式错误");
        }
    }

    private WorderIntfMessageEntity saveMessageEntity(WOrderQueryResponse.WOrder wOrder) {
        WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                .intfCode(getWorderType().getCode())
                .worderId(0)
                .bid(UpstreamSystemIdEnum.GACE.getCode())
                .data(JSON.toJSONString(wOrder))
                .createTime(new Date())
                .isTransfer(0)
                .messageType(0)
                .orderCode(wOrder.getBillno())
                .build();
        worderIntfMessageService.save(messageEntity);
        return messageEntity;
    }

    private List<WorderCheckItem> createCheckItems(WOrderQueryResponse.WOrder wOrder, Long worderId) {
        return wOrder.getDigi_wo_checklists().stream().map(workOrderCheck -> {
            WorderCheckItem checkItem = new WorderCheckItem();
            BeanUtils.copyProperties(workOrderCheck, checkItem);
            checkItem.setId(null);
            checkItem.setCkId(workOrderCheck.getId());
            checkItem.setWorderId(worderId);

            WOrderQueryResponse.WOrder.WorkOrderCheckGroup orderCheckGroup = wOrder.getDigi_wo_checklist_groups()
                    .stream()
                    .filter(checkGroup -> workOrderCheck.getCkWoChecklistGroupId().equals(String.valueOf(checkGroup.getId())))
                    .findFirst().orElse(new WOrderQueryResponse.WOrder.WorkOrderCheckGroup());
            checkItem.setDigiClgroupSource(orderCheckGroup.getDigiClgroupSource());
            checkItem.setCkgName(orderCheckGroup.getCkgName());
            checkItem.setCkgScenario(orderCheckGroup.getCkgScenario());
            return checkItem;
        }).collect(Collectors.toList());
    }

    private void autoSendWorder(String worderNo) throws RRException {
        // 自动发送工单
        Results results = worderInformationService.goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME, null);
        // 如果发送工单失败，抛出异常
        if (results.getCode() != 0) {
            throw new RRException(results.getCode() + results.getMsg());
        }
        // 修改工单状态为0（例如：已发送状态）
        worderInformationService.updateWorderStatus(worderNo);
    }

    /**
     * 修改车企工单状态
     *
     * @param worderNo 工单编号
     */
    private void updateWorderStatus(String worderNo) {
        try {
            gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.APPROVED, "工单已入库");
        } catch (Exception e) {
            log.error("同步修改车企工单状态失败", e);
        }
    }

    private void handleSaveOrderException(WOrderQueryResponse.WOrder wOrder, WorderIntfMessageEntity messageEntity, Exception e) {
        log.error("创建广汽工单失败", e);
        //SmsUtil.sendSms("15910305046", "广汽工单入库失败,车企订单号:" + wOrder.getBillno() + ",原因:" + e.getMessage(), "【到每家科技服务】");
        worderIntfMessageService.updateMessageTypeById(messageEntity.getId(), 2, e.getMessage());
    }

    /**
     * 解析数据并保存订单
     * 该方法负责从WOrder对象中解析相关数据，确定品牌、区域和工单模板，
     * 验证工单的唯一性，并最终保存工单信息到数据库
     *
     * @param wOrder 包含订单信息的对象
     * @return 返回一个包含工单ID和工单编号的Pair对象
     * @throws RRException 在保存工单信息失败或遇到特定错误情况时抛出运行时异常
     */
    private Pair<Long, String> saveWorderInformation(WOrderQueryResponse.WOrder wOrder) {
        //1. 确定品牌ID
        Integer brandId = determineBrand(wOrder);
        if (brandId == null || brandId == 0) {
            // 当未找到对应品牌时，发送短信通知，并记录错误日志
            log.error("车企订单号 {}  车辆品牌 {} news中未匹配到对应品牌", wOrder.getBillno(), wOrder.getDigi_professional_class_number());
            throw new RRException("Failed to save order  " + wOrder.getBillno() + "品牌未找到");
        }
        //2. 确定区域信息
        GaceRegionInfo regionInfo = getReginName(wOrder);
        determineRegion(regionInfo);
        //3. 工单类型
        WorderTypeEnum worderTypeEnum = (getWorderType() == GaceOrderTypeEnum.IT ? WorderTypeEnum.SERVE_INSTALL : WorderTypeEnum.SERVE_REPAIR);
        //4. 根据品牌、区域和工单类型查询模板信息
        Integer templateId = determineTemplate(wOrder, brandId, worderTypeEnum, regionInfo);
        if (templateId == null || templateId == 0) {
            log.error("车企订单号 {}  车辆品牌 {} news中未匹配到对应工单模板", wOrder.getBillno(), wOrder.getDigi_professional_class_number());
            SmsUtil.sendSms("15910305046", "广汽工单入库失败,车企订单号:" + wOrder.getBillno() + ",原因:news中未匹配到对应工单模板", "【到每家科技服务】");
            throw new RRException("Failed to save order  " + wOrder.getBillno() + "工单模板未找到");
        }
        //5. 验证车企订单号+工单模版品牌
        if (worderInformationService.validateCompanyOrderNumberAndBrandExsit(wOrder.getBillno(), templateId)) {
            log.warn("车企订单号已存在，无法创建工单: {}", wOrder.getBillno());
            throw new RRException("Failed to save order " + wOrder.getBillno());
        }
        WorderInfoEntity worderInfoEntity = createWorderInfoEntity(wOrder, regionInfo, templateId, brandId, worderTypeEnum);
        // 保存工单信息
        R r = worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
        //维修工单更新客服组为4469-李文雨
        if (worderTypeEnum == WorderTypeEnum.SERVE_REPAIR) {
            worderInformationService.lambdaUpdate()
                    .set(WorderInformationEntity::getCreateBy, 4469)
                    .eq(WorderInformationEntity::getWorderId, r.get("worderId"))
                    .update();
        }
        if (!r.isOk()) {
            throw new RRException("save worder information error");
        }
        // 返回工单ID和工单编号
        return new Pair<>((Long) r.get("worderId"), (String) r.get("worderNo"));
    }

    private WorderInfoEntity createWorderInfoEntity(WOrderQueryResponse.WOrder wOrder, GaceRegionInfo regionInfo, Integer templateId, Integer brandId, WorderTypeEnum worderTypeEnum) {
        // 构建工单实体对象
        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();
        // 构造地址信息，移除表情符号
        String address = regionInfo.getProvinceCode() + "_" + regionInfo.getCityCode() + "_" + regionInfo.getAreaCode() + "_" + regionInfo.getDetailedAddress();
        // 匹配表情符
        address = address.replaceAll("([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])", "");
        //商城备注 埃安二代7kW充电桩（不含立柱）
        String description = wOrder.getDigi_attribute8();
        if (StringUtils.contains(description, "（不含立柱）")) {
            wOrder.setContacts(wOrder.getContacts()+"（不含立柱）");
        }else if (StringUtils.contains(description, "（含立柱）")) {
            wOrder.setContacts(wOrder.getContacts()+"（含立柱）");
        }
        // 设置工单基本信息
        worderInfoEntity.setPushOrderWorderSource("gace");
        worderInfoEntity.setUserName(wOrder.getContacts());
        worderInfoEntity.setUserPhone(wOrder.getContact_mobile());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(wOrder.getBillno());
        worderInfoEntity.setTemplateId(templateId);
        // 设置车辆品牌、车型、公司ID等信息
        worderInfoEntity.setCarBrand(String.valueOf(brandId));
        worderInfoEntity.setCarModel("4");
        worderInfoEntity.setCompanyId(gaceConfig.getCompanyId());
        worderInfoEntity.setCompanyOrderNumberId(String.valueOf(wOrder.getId()));
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue(wOrder.getName());
        worderInfoEntity.setWorderTypeId(worderTypeEnum.getId());
        // 设置工单操作者信息
        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);
        // 创建工单扩展字段列表
        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(101, "车企名称", gaceConfig.getCompanyId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(153, "VIN 车架号", wOrder.getDigi_attribute1()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(306, "工单来源", wOrder.getName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(903, "安装地址", address));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(905, "客户手机", worderInfoEntity.getUserPhone()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(154, "车企派单日期", wOrder.getDigi_attribute7()));

        worderExtFieldEntityList.add(WorderExtFieldEntity.create(gaceConfig.getField().getWoopId(), "任务ID", wOrder.getWorkorder_operations().get(0).getId()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(gaceConfig.getField().getWoopNo(), "任务编号", wOrder.getWorkorder_operations().get(0).getWoop_number()));
        worderExtFieldEntityList.add(WorderExtFieldEntity.create(gaceConfig.getField().getAssetId(), "资产编码", wOrder.getAsset_id_number()));

        //维修工单添加字段 安装完成时间和是否在保
        if (worderTypeEnum == WorderTypeEnum.SERVE_REPAIR) {
            //根据车架号
            try {
                String vin = wOrder.getDigi_attribute1();
                ArrayList<Integer> vinFieldIds = new ArrayList<>();
                vinFieldIds.add(153);
                vinFieldIds.add(1161);
                vinFieldIds.add(1162);
                List<WorderExtFieldEntity> vinExtFields = worderExtFieldService.lambdaQuery()
                        .select(WorderExtFieldEntity::getWorderNo, WorderExtFieldEntity::getCreateTime, WorderExtFieldEntity::getFieldId, WorderExtFieldEntity::getFieldValue)
                        .in(WorderExtFieldEntity::getFieldId, vinFieldIds)
                        .eq(WorderExtFieldEntity::getFieldValue, vin)
                        .orderByDesc(WorderExtFieldEntity::getCreateTime)
                        .list();
                if ( ! CollectionUtils.isEmpty(vinExtFields)) {
                    List<String> worderNos = vinExtFields.stream().map(WorderExtFieldEntity::getWorderNo).collect(Collectors.toList());
                    WorderInformationEntity worderInformation = worderInformationService.lambdaQuery()
                            .in(WorderInformationEntity::getWorderExecStatus, 17, 22)
                            .in(WorderInformationEntity::getWorderTypeId, 2, 5)
                            .eq(WorderInformationEntity::getIsDelete, 0)
                            .in(WorderInformationEntity::getWorderNo, worderNos)
                            .orderByDesc(WorderInformationEntity::getCreateTime)
                            .last("limit 1")
                            .one();
                    if (worderInformation != null) {
                        worderExtFieldEntityList.add(WorderExtFieldEntity.create(147, "安装单号", worderInformation.getWorderNo()));
                        Date installDate = worderInformation.getCreateTime();
                        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1057, "安装完成时间", DateUtil.formatDate(installDate)));
                        Date endDate  = DateUtil.offset(new Date(), DateField.YEAR, -3);
                        //安装订单超过三年，不在保
                        String sfzb = installDate.before(endDate)? "否" : "是";
                        worderExtFieldEntityList.add(WorderExtFieldEntity.create(1142, "线路是否在保", sfzb));
                    }

                }
            } catch (Exception e) {
                log.error("根据车架号查询安装工单异常", e);
            }
        }


        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
        return worderInfoEntity;
    }

    protected abstract WOrderQueryRequest createQueryRequest(String startTime, String endTime, String billNo);

    protected abstract GaceRegionInfo getReginName(WOrderQueryResponse.WOrder wOrder);

    /**
     * 根据订单信息确定品牌ID
     *
     * @param wOrder 订单对象，包含品牌信息
     * @return 品牌实体的ID如果找不到对应品牌或品牌名称为空，则返回0
     */
    protected abstract Integer determineBrand(WOrderQueryResponse.WOrder wOrder);

    protected abstract Integer determineTemplate(WOrderQueryResponse.WOrder wOrder, Integer brandId, WorderTypeEnum worderTypeEnum, GaceRegionInfo regionInfo);

    /**
     * 检查订单是否已经保存
     * 此方法用于判断一个订单是否已经在系统中保存过，以避免重复保存
     * 主要通过查询接口消息实体来判断订单是否已经存在，如果不存在则创建新实体，否则更新现有实体
     *
     * @param wOrder 订单对象，包含订单信息
     * @return 返回一个布尔值，表示订单是否已经成功保存或更新
     */
    private boolean checkIfSavedAlready(WOrderQueryResponse.WOrder wOrder) {
        // 验证公司订单号是否存在
        if (worderInformationService.validateCompanyOrderNumberExsit(wOrder.getBillno())) {
            try {
                // 尝试根据订单号获取接口消息实体
                WorderIntfMessageEntity intfMessageEntity = worderIntfMessageService.getOneByOrderCode(String.valueOf(UpstreamSystemIdEnum.GACE.getCode()), wOrder.getBillno());
                // 如果实体不存在，则创建并保存新的接口消息实体
                if (intfMessageEntity == null) {
                    WorderIntfMessageEntity messageEntity = WorderIntfMessageEntity.builder()
                            .intfCode("pushOrder")
                            .worderId(0)
                            .bid(UpstreamSystemIdEnum.GACE.getCode())
                            .data(JSON.toJSONString(wOrder))
                            .createTime(new Date())
                            .isTransfer(0)
                            .messageType(1)
                            .orderCode(wOrder.getBillno())
                            .build();
                    worderIntfMessageService.save(messageEntity);
                } else {
                    // 如果实体存在但消息类型不是1或订单ID为空，则更新实体
                    if (intfMessageEntity.getMessageType() != 1 || intfMessageEntity.getWorderId() == null) {
                        // 如果订单ID为空，则获取订单信息并设置订单ID
                        if (intfMessageEntity.getWorderId() == null) {
                            WorderInformationEntity byCompanyWorderNo = worderInformationService.getByCompanyWorderNo(wOrder.getBillno());
                            intfMessageEntity.setWorderId(byCompanyWorderNo.getWorderId());
                        }
                        // 更新消息类型
                        intfMessageEntity.setMessageType(1);
                        // 更新实体
                        worderIntfMessageService.updateById(intfMessageEntity);
                    }
                }
            } catch (Exception e) {
                // 记录保存报文失败的错误日志
                log.error("保存报文失败:{}", JSON.toJSONString(wOrder), e);
                // 返回失败
            }
            return true;
        } else {
            // 如果订单号不存在，则返回失败
            return false;
        }
    }

    public void determineRegion(@NotNull GaceRegionInfo regionInfo) {
        // 初始化临时变量
        BizRegionEntity province = null;
        BizRegionEntity city = null;
        BizRegionEntity area = null;
        if (StringUtils.equalsAny(regionInfo.getCityName(), "市辖区","直辖区")) {
            regionInfo.setCityName(regionInfo.getProvinceName());
        }
        if (StringUtils.equalsAny(regionInfo.getAreaName(), "市辖区","直辖区")) {
            regionInfo.setAreaName(regionInfo.getCityName());
        }

        // 根据省市区名称查询数据
        if (StringUtils.isNotBlank(regionInfo.getProvinceName())) {
            province = bizRegionService.lambdaQuery()
                    //.eq(BizRegionEntity::getPid, null)
                    .like(BizRegionEntity::getName, regionInfo.getProvinceName())
                    .eq(BizRegionEntity::getType, 1)
                    .last("limit 1")
                    .one();
        }
        if (StringUtils.isNotBlank(regionInfo.getCityName())) {
            LambdaQueryChainWrapper<BizRegionEntity> wrapper = bizRegionService.lambdaQuery();
            if (province != null) {
                wrapper.eq(BizRegionEntity::getPid, province.getId());
            }
            city = wrapper.like(BizRegionEntity::getName, regionInfo.getCityName())
                    .eq(BizRegionEntity::getType, 2)
                    .last("limit 1")
                    .one();
            if (city == null) {
                wrapper = bizRegionService.lambdaQuery();
                area = wrapper.like(BizRegionEntity::getName, regionInfo.getCityName())
                        .eq(BizRegionEntity::getType, 3)
                        .last("limit 1")
                        .one();
            }
        }
        if (StringUtils.isNotBlank(regionInfo.getAreaName())) {
            LambdaQueryChainWrapper<BizRegionEntity> wrapper = bizRegionService.lambdaQuery();
            if (city != null) {
                wrapper.eq(BizRegionEntity::getPid, city.getId());
            }
            BizRegionEntity area1 = wrapper.like(BizRegionEntity::getName, regionInfo.getAreaName())
                    .eq(BizRegionEntity::getType, 3)
                    .last("limit 1")
                    .one();
            area = area1 == null ? area : area1;
        }

        // 自下而上递归设置省市区编码
        if (area != null) {
            regionInfo.setAreaCode(area.getId());
            regionInfo.setAreaName(area.getName());
            city = city == null ? bizRegionService.getById(area.getPid()) : city;
            if (city != null) {
                regionInfo.setCityCode(city.getId());
                regionInfo.setCityName(city.getName());
                province = province == null ? bizRegionService.getById(city.getPid()) : province;
                if (province != null) {
                    regionInfo.setProvinceCode(province.getId());
                    regionInfo.setProvinceName(province.getName());
                }
            }
        } else if (city != null) {
            regionInfo.setCityCode(city.getId());
            regionInfo.setCityName(city.getName());
            province = province==null? bizRegionService.getById(city.getPid()) : province;
            if (province != null) {
                regionInfo.setProvinceCode(province.getId());
                regionInfo.setProvinceName(province.getName());
            }
            area = bizRegionService.getFirstDistrictByCityId(city.getId());
            if (area != null) {
                regionInfo.setAreaCode(area.getId());
                regionInfo.setAreaName(area.getName());
            }
        }

        // 验证最终结果
        if (regionInfo.getProvinceCode() == null || regionInfo.getCityCode() == null || regionInfo.getAreaCode() == null) {
            throw new RRException("地址错误");
        }
    }
}
