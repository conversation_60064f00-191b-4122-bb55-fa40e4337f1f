<template>
  <div>
      <!--      边距-->
      <div>
        <el-row class="style-1" style="padding:5px;margin-top: 5%;margin-left: 5%">
          <label>上边距</label>
          <el-input-number size="mini" v-model="top"
                           :min="0" @change="topChange" style="margin-right: 3%"></el-input-number>
          <span>%</span>
        </el-row>
        <el-row class="style-1" style="padding:5px;margin-top: 5%;margin-left: 5%">
          <label>左边距</label>
          <el-input-number size="mini" v-model="left"
                           :min="0" @change="leftChange" style="margin-right: 3%"></el-input-number>
          <span>%</span>
        </el-row>
      </div>
  </div>
</template>

<script>
  export default {
    props: {
      params: Object,
      currObj: Object
    },
    data() {
      return {
        top: 0,
        left: 0
      }
    },
    mounted() {
      this.EchoDisplay()
    },
    methods: {
      EchoDisplay() {
        //边距回显
        if(!this.currObj.chdCharts.hasOwnProperty('grid')){
          this.top = 0
          this.left = 0
          return;
        }
        let shang = this.currObj.chdCharts.grid.marginTop
        let zuo = this.currObj.chdCharts.grid.marginLeft
        if (shang.substr(shang.length - 1, 1) === '%'
          || zuo.substr(zuo.length - 1, 1) === '%') {
          this.top = shang.slice(0, shang.length - 1)
          this.left = zuo.slice(0, zuo.length - 1)
        }
      },
      //显示边距
      MarginChange(val) {
        if (val === true) {
          this.section = true
        } else {
          this.section = false
        }
      },
      //上
      topChange(val) {
        if (!this.currObj.chdCharts.hasOwnProperty('grid')) {
          this.currObj.chdCharts.grid = {marginTop: val + '%', marginLeft: this.left + '%'};
        }
        this.currObj.chdCharts.grid.marginTop = val + '%';
        this.$emit('exeParentRefresh');
      },
      //左
      leftChange(val) {
        if (!this.currObj.chdCharts.hasOwnProperty('grid')) {
          this.currObj.chdCharts.grid = {marginTop: this.top + '%', marginLeft: val + '%'};
        }
        this.currObj.chdCharts.grid.marginLeft = val + '%';
        this.$emit('exeParentRefresh');
      }
    }
  }
</script>

<style>
  .style-1 {
    margin-bottom: 10%
  }

  .style-1 > .el-input {
    width: 35%;
  }
</style>
