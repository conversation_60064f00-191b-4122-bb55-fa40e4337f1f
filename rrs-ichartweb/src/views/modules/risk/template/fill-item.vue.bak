<template>
  <div>
    <div style="text-align: right">
      <el-button type="primary" @click="addOrUpdateHandle()" size="mini" v-if="!view">新增</el-button>
    </div>
    <!--表格 tree s-->
    <div id="tab">
      <el-table
        ref="fileTable"
        class="pa20"
        :data="dataList"
        :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
        border
        row-key="id"
        style="width: 100%;text-align: center;">
        <el-table-column
          prop="id"
          width="80"
          label="ID">
        </el-table-column>
        <table-tree-column
          prop="colName"
          treeKey="id"
          parentKey="pid"
          @change="resort"
          label="名称">
        </table-tree-column>
        <!--      <el-table-column
                prop="pid"
                align="center"
                width="120"
                label="上级">
              </el-table-column>-->
        <el-table-column
          prop="inputType"
          align="center"
          label="类型">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.inputType === 0" size="small">文本</el-tag>
            <el-tag v-else-if="scope.row.inputType === 1" size="small" type="success">选择</el-tag>
            <el-tag v-else-if="scope.row.inputType === 2" size="small" type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="orderNo"
          header-align="center"
          align="center"
          label="排序号">
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="150"
          label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{view ? '查看' : '编辑'}}</el-button>
            <el-button type="text" size="small" @click="deleteHandle(scope.row.id)" v-if="!view">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <fill-item-dialog v-if="addOrUpdateVisible" :view="view" :sid="sid" :tid="tid" ref="addOrUpdate" @refreshDataList="getDataList"></fill-item-dialog>
  </div>

</template>

<script>
  import TableTreeColumn from '@/components/table-tree-column'
  import fillItemDialog from './fill-item-edit'
  import { treeDataTranslate } from '@/utils'
  import Sortable from 'sortablejs'

  export default {
    name: 'file-item',
    props: {
      sid: {
        type: Number,
        required: true
      },
      tid: {
        type: Number,
        required: true
      },
      view: {
        type: Boolean,
        required: true
      }
    },
    data () {
      return {
        dataList: [],
        addOrUpdateVisible: false,
        list: [],
        form: {}
      }
    },
    mounted () {
      this.getDataList()
      // window.vu = this;
    },
    methods: {
      rowDrop() {
        const tbody = document.querySelector('#tab .el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          onEnd({ newIndex, oldIndex }) {
            console.log('data len', _this.dataList);
            console.log('( newIndex, oldIndex)', newIndex, oldIndex)
            // const currRow = _this.dataList.splice(oldIndex, 1)[0]
            // console.log(currRow)
            // if(currRow !== undefined){
            //   _this.dataList.splice(newIndex, 0, currRow)
            // }
            _this.$nextTick(() => {
              _this.$refs.fileTable.doLayout()
            })
          }
        })
      },
      resort() {
        
        // this.rowDrop();
      },
      // 获取数据列表
      getDataList () {
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl('/operational/questionnaire/select'),
          method: 'get',
          params: this.$http.adornParams({basic: false, sid: this.sid})
        }).then(({data}) => {
          if (data.code === 0) {
            this.dataList = treeDataTranslate(data.data, 'id', 'pid');
            this.dataListLoading = false
            if (this.dataList && this.dataList.length > 1) {
              this.$nextTick(() => {
                this.rowDrop();
              })
            }
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      addOrUpdateHandle (id) {
        this.addOrUpdateVisible = true;
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      deleteHandle (id) {
        this.$confirm('确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http({
            url: this.$http.adornUrl(`/operational/questionnaire/delete/${id}`),
            method: 'post',
            data: {basic: false}
            // data: this.$http.adornData({basic: true})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 600,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {})
      }
    },
    components: {
      fillItemDialog,
      TableTreeColumn
    }
  }
</script>

<style scoped>

</style>
