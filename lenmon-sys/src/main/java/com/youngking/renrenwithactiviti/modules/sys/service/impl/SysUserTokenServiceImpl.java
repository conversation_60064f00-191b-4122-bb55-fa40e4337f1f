/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.renrenwithactiviti.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserTokenDao;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserTokenEntity;
import com.youngking.renrenwithactiviti.modules.sys.oauth2.TokenGenerator;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserTokenService;
import com.youngking.lenmoncore.common.utils.R;
import org.springframework.stereotype.Service;

import java.util.Date;


@Service("sysUserTokenService")
public class SysUserTokenServiceImpl extends ServiceImpl<SysUserTokenDao, SysUserTokenEntity> implements SysUserTokenService {
    //12小时后过期
    private final static int EXPIRE = 3600 * 12;
    // 5分钟
    //private final static int EXPIRE = 60 * 5;


    @Override
    public R createToken(long userId) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        boolean flag = true;

        //当前时间
        Date now = new Date();
        //过期时间
        Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

        //判断是否生成过token
        SysUserTokenEntity tokenEntity = this.getById(userId);
        if (tokenEntity == null) {
            tokenEntity = new SysUserTokenEntity();
            tokenEntity.setUserId(userId);
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);

            //保存token
            flag = this.save(tokenEntity);
        } else {
            if (userId == Constant.SUPER_ADMIN && tokenEntity.getExpireTime().getTime() > System.currentTimeMillis()) {
                return R.ok().put("token", tokenEntity.getToken()).put("expire", expireTime).put("login", flag);
            }
            if(tokenEntity.getExpireTime().getTime() > System.currentTimeMillis()){
                return R.ok().put("token", tokenEntity.getToken()).put("expire", expireTime).put("login", flag);
            }
            tokenEntity.setToken(token);
            tokenEntity.setUpdateTime(now);
            tokenEntity.setExpireTime(expireTime);

            //更新token
            flag = this.updateById(tokenEntity);
        }

        R r = R.ok().put("token", token).put("expire", EXPIRE).put("login", flag);

        return r;
    }

    @Override
    public void logout(long userId) {
        //生成一个token
        String token = TokenGenerator.generateValue();

        //修改token
        SysUserTokenEntity tokenEntity = new SysUserTokenEntity();
        tokenEntity.setUserId(userId);
        tokenEntity.setToken(token);
        this.updateById(tokenEntity);
    }
}
