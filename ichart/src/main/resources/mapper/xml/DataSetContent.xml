<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.ichart.demo.module.ichartform.dao.DataSetContentDao">
    <select id="findByTableParams" parameterType="java.util.Map" resultType="hashmap">
        select
        <foreach item="col" index="index" collection="columns" open=" " separator="," close="">
            ${col}
        </foreach>
        from
        ${tableName}
        where 1=1
        <if test="deleteFlag!=null">
            and #{deleteFlag} != 0
        </if>
    </select>
    <select id="findByParams" parameterType="java.util.Map" resultType="hashmap">
        select
        <foreach item="col" index="index" collection="columns" open=" " separator="," close="">
            ${col}
        </foreach>
        from
        ${tableName}
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="likeColumn!=null and likeContent!=null">
                AND ${likeColumn} like '%'||#{likeContent}||'%'
            </if>

            <if test="wheres!=null">
                <foreach item="wp" index="index" collection="wheres" open=" " separator=" " close=" ">
                    <!--  查询组 -->
                    <if test="wp.type == 'whereGroup' ">
                        ${wp.relationalOperator} (
                        <trim prefix=" " prefixOverrides="AND|OR">
                            <foreach item="cwp" index="cindex" collection="wp.chdWheres" open=" " separator=" "
                                     close=" ">
                                <if test="cwp.type == 'none' ">
                                    ${cwp.relationalOperator} (
                                    <choose>
                                        <when test="cwp.operator=='in'">
                                            ${cwp.relationalOperator} ${cwp.columnName} in
                                            <foreach item="v" index="index" collection="cwp.val" open="("
                                                     separator=","
                                                     close=")">
                                                #{v}
                                            </foreach>
                                        </when>
                                        <when test="cwp.operator=='like'">
                                            ${cwp.relationalOperator} ${cwp.columnName} ${cwp.operator} CONCAT('%',
                                            #{cwp.val} ,'%')
                                        </when>
                                        <when test="cwp.operator=='gt'">
                                            ${cwp.relationalOperator} ${cwp.columnName} <![CDATA[>]]>  #{cwp.val}
                                        </when>
                                        <when test="cwp.operator=='gte'">
                                            ${cwp.relationalOperator} ${cwp.columnName} <![CDATA[>=]]>  #{cwp.val}
                                        </when>
                                        <when test="cwp.operator=='lt'">
                                            ${cwp.relationalOperator} ${cwp.columnName} <![CDATA[<]]>  #{cwp.val}
                                        </when>
                                        <when test="cwp.operator=='lte'">
                                            ${cwp.relationalOperator} ${cwp.columnName} <![CDATA[<=]]>  #{cwp.val}
                                        </when>
                                        <when test="cwp.operator=='eq'">
                                            ${cwp.relationalOperator} ${cwp.columnName} = #{cwp.val}
                                        </when>

                                        <when test="cwp.operator=='likeLeftOr'">
                                            ${cwp.relationalOperator}
                                            <foreach item="v" index="index" collection="cwp.val" open="("
                                                     separator=" OR "
                                                     close=")">
                                                ${cwp.columnName} like CONCAT(#{v} ,'%')
                                            </foreach>
                                        </when>
                                        <when test="cwp.operator=='teamUsers'">
                                            ${cwp.columnName} in (
                                                SELECT DISTINCT
                                                aa.user_id
                                                FROM
                                                (SELECT
                                                user_id
                                                FROM
                                                sys_user
                                                WHERE user_id IN
                                                <foreach item="v" index="index" collection="cwp.teamUserIds" open="("
                                                         separator=","
                                                         close=")">
                                                    #{v}
                                                </foreach>
                                            UNION
                                                SELECT
                                                user_id
                                                FROM
                                                sys_user_relationship
                                                WHERE
                                                <foreach item="v" index="index" collection="cwp.teamUserLevelCodes" open="("
                                                         separator=" or "
                                                         close=")">
                                                    level_code LIKE concat(#{v},'%')
                                                </foreach>
                                                ) aa
                                            )

                                        </when>
                                        <when test="cwp.operator=='teamUsersOrg'">
                                            ${cwp.columnName} in (
                                            SELECT dept_id FROM pa_sys_dept WHERE
                                            <foreach item="v" index="index" collection="cwp.teamUsersOrgLcode" open="("
                                                     separator=" OR "
                                                     close=")">
                                                dept_code LIKE concat(#{v},'%')
                                            </foreach>
                                            )

                                        </when>
                                        <otherwise>
                                            <!--                                <trim prefix=" " prefixOverrides="AND|OR">-->
                                            <!--                                                  AND ${cwp.columnName} ${cwp.operator} #{cwp.val}-->
                                            <!--                                </trim>-->
                                        </otherwise>
                                    </choose>
                                    )
                                </if>


                                <if test="cwp.type == 'whereGroup' ">
                                    ${cwp.relationalOperator} (
                                    <trim prefix=" " prefixOverrides="AND|OR">
                                        <foreach item="ccwp" index="ccindex" collection="cwp.chdWheres" open=" "
                                                 separator=" "
                                                 close=" ">

                                            <choose>
                                                <when test="ccwp.operator=='in'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} in
                                                    <foreach item="v" index="index" collection="ccwp.val" open="("
                                                             separator=","
                                                             close=")">
                                                        #{v}
                                                    </foreach>
                                                </when>
                                                <when test="ccwp.operator=='like'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} ${ccwp.operator}
                                                    CONCAT('%',
                                                    #{ccwp.val} ,'%')
                                                </when>
                                                <when test="ccwp.operator=='gt'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} <![CDATA[>]]>
                                                    #{ccwp.val}
                                                </when>
                                                <when test="ccwp.operator=='gte'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} <![CDATA[>=]]>
                                                    #{ccwp.val}
                                                </when>
                                                <when test="ccwp.operator=='lt'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} <![CDATA[<]]>
                                                    #{ccwp.val}
                                                </when>
                                                <when test="ccwp.operator=='lte'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} <![CDATA[<=]]>
                                                    #{ccwp.val}
                                                </when>
                                                <when test="ccwp.operator=='eq'">
                                                    ${ccwp.relationalOperator} ${ccwp.columnName} = #{ccwp.val}
                                                </when>

                                                <when test="ccwp.operator=='likeLeftOr'">
                                                    ${ccwp.relationalOperator}
                                                    <foreach item="v" index="index" collection="ccwp.val" open="("
                                                             separator=" OR "
                                                             close=")">
                                                        ${ccwp.columnName} like CONCAT(#{v} ,'%')
                                                    </foreach>
                                                </when>
                                                <when test="ccwp.operator=='teamUsers'">
                                                     ${cwp.columnName} in (
                                                        SELECT DISTINCT
                                                        aa.user_id
                                                        FROM
                                                        (SELECT
                                                        user_id
                                                        FROM
                                                        sys_user
                                                        WHERE user_id IN
                                                        <foreach item="v" index="index" collection="ccwp.teamUserIds" open="("
                                                                 separator=","
                                                                 close=")">
                                                            #{v}
                                                        </foreach>

                                                    UNION
                                                        SELECT
                                                        user_id
                                                        FROM
                                                        sys_user_relationship
                                                        WHERE
                                                        <foreach item="v" index="index" collection="ccwp.teamUserLevelCodes" open="("
                                                                 separator=" or "
                                                                 close=")">
                                                            level_code LIKE concat(#{v},'%')
                                                        </foreach>
                                                        ) aa
                                                    )
                                                </when>
                                                <when test="ccwp.operator=='teamUsersOrg'">
                                                     ${ccwp.columnName} in (
                                                    SELECT dept_id FROM pa_sys_dept WHERE
                                                    <foreach item="v" index="index" collection="ccwp.teamUsersOrgLcode" open="("
                                                             separator=" OR "
                                                             close=")">
                                                        dept_code LIKE concat(#{v},'%')
                                                    </foreach>
                                                    )
                                                </when>
                                                <otherwise>
                                                    <!--                                <trim prefix="WHERE" prefixOverrides="AND|OR">-->
                                                    <!--                                                  AND ${ccwp.columnName} ${ccwp.operator} #{ccwp.val}-->
                                                    <!--                                </trim>-->
                                                </otherwise>
                                            </choose>

                                        </foreach>
                                    </trim>
                                    )
                                </if>
                            </foreach>
                        </trim>
                        )
                    </if>

                    <!--  查询 -->
                    <if test="wp.type == 'none' ">
                        <choose>
                            <when test="wp.operator=='in' and wp.val != null">
                                AND ${wp.columnName} in
                                <foreach item="v" index="index" collection="wp.val" open="(" separator=","
                                         close=")">
                                    #{v}
                                </foreach>
                            </when>
                            <when test="wp.operator=='like' and wp.val != null">
                                AND ${wp.columnName} ${wp.operator} CONCAT('%', #{wp.val} ,'%')
                            </when>
                            <when test="wp.operator=='llike' and wp.val != null">
                                AND ${wp.columnName} like CONCAT( #{wp.val} ,'%')
                            </when>
                            <when test="wp.operator=='gt' and wp.val != null">
                                AND ${wp.columnName} <![CDATA[>]]>  #{wp.val}
                            </when>
                            <when test="wp.operator=='gte' and wp.val != null">
                                AND ${wp.columnName} <![CDATA[>=]]>  #{wp.val}
                            </when>
                            <when test="wp.operator=='lt' and wp.val != null">
                                AND ${wp.columnName} <![CDATA[<]]>  #{wp.val}
                            </when>
                            <when test="wp.operator=='lte' and wp.val != null">
                                AND ${wp.columnName} <![CDATA[<=]]>  #{wp.val}
                            </when>
                            <when test="wp.operator=='eq' and wp.val != null">
                                AND ${wp.columnName} = #{wp.val}
                            </when>
                            <when test="wp.operator=='likeLeftOr' and wp.val != null">
                                ${wp.relationalOperator}
                                <foreach item="v" index="index" collection="wp.val" open="("
                                         separator=" OR "
                                         close=")">
                                    ${wp.columnName} like CONCAT(#{v} ,'%')
                                </foreach>
                            </when>

                            <when test="wp.operator=='teamUsers'">
                                ${wp.relationalOperator}
                                ${wp.columnName} in (
                                    SELECT DISTINCT
                                    aa.user_id
                                    FROM
                                    (SELECT
                                    user_id
                                    FROM
                                    sys_user
                                    WHERE user_id IN
                                    <foreach item="v" index="index" collection="wp.teamUserIds" open="("
                                             separator=","
                                             close=")">
                                        #{v}
                                    </foreach>
                                UNION
                                    SELECT
                                    user_id
                                    FROM
                                    sys_user_relationship
                                    WHERE
                                    <foreach item="v" index="index" collection="wp.teamUserLevelCodes" open="("
                                             separator=" or "
                                             close=")">
                                        level_code LIKE concat(#{v},'%')
                                    </foreach>
                                    ) aa
                                )
                            </when>
                            <when test="wp.operator=='teamUsersOrg'">
                                ${wp.relationalOperator}
                                ${wp.columnName} in (
                                SELECT dept_id FROM pa_sys_dept WHERE
                                <foreach item="v" index="index" collection="wp.teamUsersOrgLcode" open="("
                                         separator=" OR "
                                         close=")">
                                    dept_code LIKE concat(#{v},'%')
                                </foreach>
                                )
                            </when>

                            <otherwise>
                                <!--                                <trim prefix="WHERE" prefixOverrides="AND|OR">-->
                                <!--                                                  AND ${wp.columnName} ${wp.operator} #{wp.val}-->
                                <!--                                </trim>-->
                            </otherwise>
                        </choose>
                    </if>
                </foreach>
            </if>
            <if test="tenantId != null and tenantId != ''">
               and tenant_id = #{tenantId}
            </if>
        </trim>
        <include refid="dynamicGroupBy"/>
        <include refid="dynamicOrderBy"/>
        <if test="limit!=null and rows!=null">
            LIMIT ${rows}, ${limit}
        </if>
        <if test="limit!=null and rows==null">
            LIMIT 0, ${limit}
        </if>
    </select>
    <!-- order by 排序   -->
    <sql id="dynamicOrderBy">
        <if test="orderBy!=null">
            order by ${orderBy}
            <!--            <if test="order!=null">-->
            <!--                ${order}-->
            <!--            </if>-->
        </if>
    </sql>

    <!-- group by 分组   -->
    <sql id="dynamicGroupBy">
        <if test="groupBy!=null">
            group by ${groupBy}
        </if>
    </sql>

</mapper>




