package com.bonc.rrs.supervise.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * 督办工单表;
 *
 * <AUTHOR> liujunpeng
 * @date : 2023-9-26
 */
@ApiModel(value = "督办工单表", description = "")
@Data
public class SuperviseInfomationVo implements Serializable {


    /**
     * 工单号
     */
    @ApiModelProperty(name = "工单号", notes = "")
    private String worderNo;
    /**
     * 用户姓名
     */
    @ApiModelProperty(name = "用户姓名", notes = "")
    @NotNull(message = "缺少用户姓名")
    private String userName;
    /**
     * 用户手机号码
     */
    @ApiModelProperty(name = "用户手机号码", notes = "")
    @NotNull(message = "缺少用户手机号码")
    private String userPhone;
    /**
     * 品牌
     */
    @ApiModelProperty(name = "品牌", notes = "")
    @NotNull(message = "缺少品牌")
    private Integer brandId;
    /**
     * 区域（区县级别）
     */
    @ApiModelProperty(name = "区域（区县级别）", notes = "")
    @NotNull(message = "缺少区域")
    private Integer areaId;
    /**
     * 服务类型
     */
    @ApiModelProperty(name = "服务类型", notes = "")
    @NotNull(message = "缺少服务属性")
    private Integer serviceType;
    /**
     * 督办来源
     */
    @ApiModelProperty(name = "督办来源", notes = "")
    @NotNull(message = "缺少督办来源")
    private Integer superviseClassification;
    /**
     * 督办类型
     */
    @ApiModelProperty(name = "督办类型", notes = "")
    @NotNull(message = "缺少督办类型")
    private Integer superviseType;
    /**
     * 督办内容
     */
    @ApiModelProperty(name = "督办内容", notes = "")
    @NotNull(message = "缺少督办内容")
    private String superviseContent;
    /**
     * 责任人
     */
    @ApiModelProperty(name = "责任人", notes = "")
    private Integer dutyPeo;

    /**
     * 用户要求解决时间
     */
    @ApiModelProperty(name = "用户要求解决时间", notes = "")
    private Date processTime;

    /**
     * 下次联系时间
     */
    @ApiModelProperty(name = "下次联系时间", notes = "")
    private Date nextConnectionTime;

    /**
     * 相关责任人,多个逗号隔开
     */
    @ApiModelProperty(name = "相关责任人,多个逗号隔开", notes = "")
    private String relatedPeo;
    /**
     * 0:自建，1：工单
     */
    @ApiModelProperty(name = "0:自建，1：工单", notes = "")
    private Integer superviseSource;

    /**
     * 跳闸类型
     */
    @ApiModelProperty(name = "跳闸类型", notes = "")
    private Integer tripType;

    /**
     * 网点管理员名称
     */
    @ApiModelProperty(name = "网点管理员名称", notes = "")
    private String dotUserName;

    /**
     * 网点管理员名称电话
     */
    @ApiModelProperty(name = "网点管理员名称电话", notes = "")
    private String dotUserTelephone;

    //网点管理员名称账号id
    @ApiModelProperty(name = "网点账号id", notes = "")
    private Integer dotUserId;

    public void setProcessTime(Date processTime) {
        this.processTime = format(processTime);
    }

    public void setNextConnectionTime(Date nextConnectionTime) {
        this.nextConnectionTime = format(nextConnectionTime);
    }

    // 格式化时间到分钟，秒按照00展示
    public Date format(Date time) {
        if(time == null){
            return null;
        }
        // 创建一个Calendar对象，并将其设置为要修改的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);

        // 将秒部分设置为零
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }
}