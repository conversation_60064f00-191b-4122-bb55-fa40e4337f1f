<template>
  <div>
    <div class="pa-title"><h2>打分规则设置</h2></div>
<!-- <el-row type="flex" class="pa20" align="middle" style="text-align: center">
      <el-col :span="3"><h3>汇总公式：</h3></el-col>
      <el-col :span="15">
        <el-input placeholder="默认公式：s1+s2+..." v-model="formula"/>
      </el-col>
      <el-col :span="2" style="margin-left:5px">
        <el-button type="primary" @click="save()" :loading="submitted">保存</el-button>
      </el-col>
    </el-row>-->
    <el-tabs class="pa20" v-model="activeName" type="card">
      <el-tab-pane
        @click="tabClick()"
        v-for="(item, index) in tabList"
        v-if="index<1"
        :key="'S'+item.id"
        :label="item.sheetName + '(s'+(index +1)+')'"
        :name="index+''">
        <score-rules ref="scoreRules" :sid="item.id"></score-rules>
      </el-tab-pane>
    </el-tabs>
   <!--
   <div class="text-center pa20">
      <el-button type="primary" @click="tianxiexiangTwo = false">保存</el-button>
      <el-button type="warning" @click="tianxiexiangTwo = false">取消</el-button>
    </div>
    -->
  </div>
</template>
<script>
  import scoreRules from './score-rules'
  export default{
    data () {
      return {
        submitted: false,
        activeName: '0',
        tabList: [],
        tid: 0,
        formula: 's1+s2'
      }
    },
    created () {
      this.$destroy();
    },
    mounted () {
      this.submitted = false;
      if (this.$route.query.tempId) {
        this.tid = this.$route.query.tempId;
        this.getTabList();
      } else {
        console.log('创建模板');
      }
      let nameQuery = {name: this.$route.name, query: this.$route.query};
      this.$store.commit('common/updateMainTabsQuery', nameQuery);
    },
    methods: {
      getTabList () {
        this.$http({
          url: this.$http.adornUrl('/operational/templatesheet/select'),
          method: 'get',
          params: this.$http.adornParams({tid: this.tid})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tabList = data.data;
            this.formula = data.formula
          } else {
            this.$message.error(data.msg)
          }
        });
      },
      save () {
        if (this.formula) {
          this.submitted = true;
          this.$http({
            url: this.$http.adornUrl(`/operational/questionnaire/updateTempalte/${this.tid}`),
            method: 'post',
            params: this.$http.adornData({formula: this.formula})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                message: '保存成功',
                type: 'success',
                duration: 600,
                onClose: () => {
                  this.submitted = false;
                  this.getTabList()
                }
              })
            } else {
              this.submitted = false;
              this.$message.error(data.msg)
            }
          });
        } else {
          this.submitted = false;
          this.$message({
            type: 'success',
            message: '请填写汇总公式!'
          })
        }
      }
    },
    components: {
      scoreRules
    }
  }
</script>
<style>
  .pro-table {
    border: 1px solid #ccd0d4;
    width: 100%;
  }

  .pro-table tr td {
    text-align: center;
    padding: 8px;
    border: 1px solid #ccd0d4;
  }

  .area {
    margin-top: 65px;
  }

  .area .btn {
    margin-top: 10px;
  }
</style>
<style scoped>
  .el-input-number.is-controls-right .el-input__inner {
    padding-left: 0px;
    padding-right: 30px;
  }
</style>
