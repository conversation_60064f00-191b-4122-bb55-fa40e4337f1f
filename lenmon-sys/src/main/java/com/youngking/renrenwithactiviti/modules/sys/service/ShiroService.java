/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.renrenwithactiviti.modules.sys.service;

import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserTokenEntity;

import java.util.Map;
import java.util.Set;

/**
 * shiro相关接口
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
public interface ShiroService {

    /**
     * 查询所有权限
     * @param userId
     * @return
     */
    Map queryAllPermits(Long userId);

    /**
     * 获取用户权限列表
     */
    Set<String> getUserPermissions(long userId);

    SysUserTokenEntity queryByToken(String token);

    /**
     * 根据用户ID，查询用户
     *
     * @param userId
     */
    SysUserEntity queryUser(Long userId);
}
