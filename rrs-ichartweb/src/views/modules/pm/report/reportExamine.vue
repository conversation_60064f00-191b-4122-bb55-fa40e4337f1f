<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="visible">
      <div class="pa-title">
        <h2>检视</h2>
      </div>
      <el-form :model="dataForm" ref="dataForm" label-width="100px" style="margin-top: 30px">
        <el-form-item label="检视人" v-if="isAuth('project:mod:super')">
          <el-select v-model="examineId" placeholder="请选择">
            <el-option
              v-for="item in examinePendingList"
              :key="item.id"
              :label="item.userName"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="检视意见"
                      prop="remark"
                      :rules="[{ required: true, message: '检视意见不能为空', trigger: 'blur' }]">
          <!--<el-input type="textarea" v-model.trim="dataForm.remark"></el-input>-->
          <pa-textarea :maxlength="500" placeholder="最多输入500字" v-model="dataForm.remark"></pa-textarea>
        </el-form-item>
        <el-form-item label="说明" v-if="toFillInNum==0 && fillInProgress < 100">
          <div class="remark1" style="color: red">
            当前任务的进度为 <b>{{fillInProgress}}% </b>,待填报数为 <b>{{toFillInNum}} </b>。
            最后一次填报必需所有填报周期填报完成并进度为100%，才可通过此检视。
          </div>
        </el-form-item>
      </el-form>

      <div class="text-center">
        <el-button type="primary" :disabled="toFillInNum==0 && fillInProgress < 100" @click="agreeOrReject(status.AGREE)">通过</el-button>
        <el-button type="primary" @click="reject()">驳回</el-button>
        <el-button type="warning" @click="cancel()">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      width="50%"
      title="审核方式"
      :visible.sync="examineWayVisible"
      append-to-body>
      <el-form ref="dataForm2" :model="dataForm" label-width="150px">
        <el-form-item label="是否重头审核" prop="examineWay"
                      :rules="[{ required: true, message: '请选择审核方式', trigger: 'blur' }]">
          <el-radio-group v-model="dataForm.examineWay">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="text-center">
        <el-button type="primary" @click="agreeOrReject(status.REJECT)">确定</el-button>
        <el-button type="warning" @click="examineWayVisible = false;dataForm.examineWay = undefined">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      width="50%"
      title="驳回类型"
      :visible.sync="rejectTypeVisible"
      append-to-body>
      <el-form ref="dateForm3" :model="dataForm" label-width="150px">
        <el-form-item label="驳回类型" prop="rejectType"
                      :rules="[{ required: true, message: '请选择驳回类型', trigger: 'blur' }]">
          <el-radio-group v-model="dataForm.rejectType">
            <el-radio :label="1">工作进度未更新</el-radio>
            <el-radio :label="2">任务填写有误</el-radio>
            <el-radio :label="3">未上传交付物</el-radio>
            <el-radio :label="4">其他</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="text-center">
        <el-button type="primary" @click="agreeOrReject(status.REJECT)">确定</el-button>
        <el-button type="warning" @click="rejectTypeVisible = false;dataForm.examineWay = undefined">取消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
  import paTextarea from '@/components/component/pa-textarea'
  import { Loading } from 'element-ui';
  export default {
    name: 'reportExamine',
    components: {
      paTextarea
    },
    props: {
      fillInProgress: {
        type: Number,
        required: true
      },
      toFillInNum: {
        type: Number,
        required: true
      }
    },
    data() {
      return {
        examineWayVisible: false,
        visible: false,
        rejectTypeVisible: false,
        report: null,
        examineId: null,
        examinePendingList: [],
        dataForm: {
          remark: '',
          examineWay: 0,
          rejectType: undefined
        },
        status: {
          AGREE: 2,  // 2 同意
          REJECT: 3  // 3 拒绝
        }
      }
    },
    methods: {
      init(report) {
        this.visible = true;
        this.report = report;
        this.examineId = this.report.examineId;
        if (this.isAuth('project:mod:super')) {
          this.getExaminePending(this.report.id);
        }
      },
      reject() {
        // this.examineWayVisible = true;
        // this.dataForm.examineWay = undefined;
        // console.error('ttt');
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.rejectTypeVisible = true
          }
        });
      },
      agreeOrReject(status) {
        let formName = 'dataForm';
        if (this.rejectTypeVisible) {
          formName = 'dateForm3';
        }
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let loading = this.$loading({lock: true, text: 'Loading', spinner: 'el-icon-loading', background: 'rgba(0, 0, 0, 0.7)'});
            if (this.isAuth('project:mod:super')) {
              this.dataForm.remark = '管理员代检视：' + this.dataForm.remark;
            }
            let formData = {
              'id': this.examineId,
              'refId': this.report.id,
              'remark': this.dataForm.remark,
              'status': status,
              'type': 2, // 检视
              'examineWay': this.dataForm.examineWay
            };
            if (status === this.status.AGREE) {
              formData.rejectType = 0;
            }
            if (this.rejectTypeVisible) {
              formData.rejectType = this.dataForm.rejectType;
            }
            this.$http({
              url: this.$http.adornUrl('/pm/examine/updateReportStatus'),
              method: 'POST',
              data: this.$http.adornData(formData)
            }).then(({data}) => {
              if (data && data.code === 0) {
                let project = data.project;
                this.$emit('refreshDataList')
                loading.close();
                this.visible = false
                this.dataForm = {}
                if (status === this.status.AGREE && data.flag) {
                  //发起结项的提醒
                  this.overProject(project.id);
                } else {
                  this.$message.success({
                    message: '操作成功'
                  });
                }
                // this.$emit('inspection', {
                //   taskId: this.report.taskId,
                //   projectId: project.projectId,
                //   statManHour: project.statManHour,
                //   targetRation: project.targetRation
                // })
              } else {
                loading.close();
                this.$message.error({
                  message: data.msg || '操作失败'
                });
              }
              this.examineWayVisible = false;
              this.rejectTypeVisible = false;
            }).catch((e) => {
              this.$message.error({message: '操作失败'});
              this.examineWayVisible = false;
              this.rejectTypeVisible = false;
            });
          }
        });
      },
      cancel() {
        this.visible = false
        this.dataForm = {}
      },
      getExaminePending(reportId) { // 查询当前待检视人
        this.loadingExamine = true;
        this.$http({
          url: this.$http.adornUrl('/pm/examine/examinePendingByReport'),
          method: 'get',
          params: this.$http.adornParams({
            'refId': reportId,
            'type': 2
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.examinePendingList = data.data;
            this.examineId = data.data[0].id;
          } else {
            this.examinePendingList = [];
          }
          this.loadingExamine = false;
        })
      },
      overProject(id) {
        this.$confirm('确认是否结项?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http.post(
            this.$http.adornUrl('/pm/project/update'),
            {id: id, projectStatus: 6}
          ).then((response) => {
            loadingInstance.close();
            if (response.status === 200) {
              this.$message({
                message: '结项成功',
                type: 'success',
                duration: 600,
                onClose: () => {
                  //
                }
              })
            }
          }, (response) => {
            console.log('修改整改项目发生错误')
          })
        }).catch(() => {
          this.alert('取消!');
        });
      }
    }
  }
</script>

<style scoped>

  .remark{
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    left: 0;
  }

</style>
