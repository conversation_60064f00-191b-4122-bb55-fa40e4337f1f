package com.bonc.rrs.gace.controller;

import cn.hutool.core.date.DateUtil;
import com.bonc.rrs.gace.dto.request.AuditStatusRequest;
import com.bonc.rrs.gace.dto.response.AuditStatusResponse;
import com.bonc.rrs.gace.enums.GaceOrderTypeEnum;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.gace.service.GacePullService;
import com.bonc.rrs.gace.service.GacePushService;
import com.bonc.rrs.gace.service.GaceService;
import com.bonc.rrs.gace.service.impl.GaceInstallPullService;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worderAudit.entity.WorderAuditResultEntity;
import com.bonc.rrs.worderAudit.service.WorderAuditResultService;
import com.bonc.rrs.worderapp.dao.WorderOperationRecodeDao;
import com.bonc.rrs.worderapp.entity.WorderOperationRecodeEntity;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.youngking.lenmoncore.common.constant.WorderExecStatusEnum;
import com.youngking.lenmoncore.common.constant.WorderSetStatusEnum;
import com.youngking.lenmoncore.common.constant.WorderStatusEnum;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gace")
@Slf4j
public class GaceController {

    @Autowired
    private List<GacePullService> gacePullServices;

    @Autowired
    private GacePushService gacePushService;

    @Autowired
    private GaceService gaceService;

    @Autowired
    private WorderInformationService worderInformationService;

    @Autowired
    private WorderInformationDao baseMapper;

    @Autowired
    private FlowCommon flowCommon;
    @Resource
    public WorderOperationRecodeDao worderOperationRecodeDao;

    @Autowired
    private WorderAuditResultService worderAuditResultService;

    @Autowired(required = false)
    WorderWaitAccountService worderWaitAccountService;

    @PostMapping("/pullOrder")
    public R pullOrder(String type, String startTime, String endTime,String billNo) {

        log.info("开始时间{},结束时间:{}",startTime,endTime);
        GaceOrderTypeEnum gaceOrderTypeEnum = GaceOrderTypeEnum.fromCode(type);
        Optional<GacePullService> first = gacePullServices.stream()
                .filter(gacePullService -> gacePullService.getWorderType() == gaceOrderTypeEnum)
                .findFirst();
        if (first.isPresent()) {
            first.get().pullOrders(startTime, endTime, billNo);
            return R.ok();
        } else {
            return R.error("工单类型错误");
        }
    }

    /**
     * 提交工单
     * @param worderNo
     * @return
     */
    @PostMapping("/pushCompleted")
    public R pushApproved(String worderNo) {
        gacePushService.checkGaceFlag(worderNo);
        gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.COMPLETED, "安装完成");
        return R.ok();
    }

    /**
     * 修改工单状态
     * @param worderNo
     * @param status
     * @return
     */
    @PostMapping("/updateOrderStatus")
    public R updateOrderStatus(String worderNo, String status) {
        gacePushService.checkGaceFlag(worderNo);
        gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.fromCode(status), "更新工单状态");
        return R.ok();
    }


    /**
     * 取消工单
     * @param worderNo
     * @return
     */
    @PostMapping("/cancel")
    public R cancel(String worderNo) {
        gacePushService.checkGaceFlag(worderNo);
        gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.CANCEL, "取消安装");
        return R.ok();
    }

    /**
     * 查询工单车企审核状态
     */
    @GetMapping("/queryAuditStatus")
    public R queryCompanyOrderStatus(String companyOrderNumber) {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        WorderInformationEntity worderInfoEntity = worderInformationService.getByCompanyWorderNo(companyOrderNumber);
        AuditStatusResponse response = gaceService.queryAuditStatus(AuditStatusRequest.builder().billno(companyOrderNumber).build());
        String status = response.getStatus();
        Map<String, Object> map = new HashMap<>();
        String worderNo = worderInfoEntity.getWorderNo();
        Integer worderId = worderInfoEntity.getWorderId();
        map.put("worderId", worderId);
        if (StringUtils.equals(status, "已完成")) {
            if (Boolean.TRUE.equals(flowCommon.hasFlowByWorderNo(worderNo))) {
                //车企确认安装完成
                flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.CompConfFix, FlowConstant.ProcessStatus.Y);
            } else {
                map.put("worderExecStatus", WorderExecStatusEnum.ANZHUANGWANCHENG.getCode());
                map.put("worderExecStatusValue", WorderExecStatusEnum.ANZHUANGWANCHENG.getName());
                map.put("worderStatus", WorderStatusEnum.JIESUANZHONG.getCode());
                map.put("worderStatusValue", WorderStatusEnum.JIESUANZHONG.getName());
                map.put("worderSetStatus", WorderSetStatusEnum.WORDER_WAIT_CALCULATE.getValue());
                map.put("worderSetStatusValue", "工单待计算");
                baseMapper.updateWorderExecStatus(map);
                baseMapper.updateWorderStatus(map);
                baseMapper.updateWorderSetStatus(map);
            }
            baseMapper.updateWorderFinishTime(map);
            // 加入待工单结算-车企待结算
            worderWaitAccountService.addWorderWaitAccount(worderInfoEntity.getWorderId());
            // 保存操作记录
            WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
            worderOperationRecodeEntity.setUserId(user.getUserId());
            worderOperationRecodeEntity.setOperationUser(user.getEmployeeName());
            worderOperationRecodeEntity.setRecord("安装完成");
            worderOperationRecodeEntity.setWorderNo(worderNo);
            worderOperationRecodeEntity.setCreateTime(DateUtil.formatDateTime(new Date()));
            worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInfoEntity.getWorderStatus()));
            worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInfoEntity.getWorderExecStatus()));
            worderOperationRecodeDao.insert(worderOperationRecodeEntity);
        } else if (StringUtils.equals(status, "被驳回")){
            // 调用流程结束
            if (Boolean.TRUE.equals(flowCommon.hasFlowByWorderNo(worderNo))) {
                //调用网点已接单流程
                flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.N);
            } else {

                // 修改工单状态 2 ，14
                // 如果未匹配到流程强制修改状态，2,23
                map.put("worderExecStatus", WorderExecStatusEnum.ANZHUANGZILIAOZHENGGAIZHONG.getCode());
                map.put("worderExecStatusValue", WorderExecStatusEnum.ANZHUANGZILIAOZHENGGAIZHONG.getName());
                map.put("worderStatus", WorderStatusEnum.ANZHUANGZHONG.getCode());
                map.put("worderStatusValue", WorderStatusEnum.ANZHUANGZHONG.getName());
                baseMapper.updateWorderExecStatus(map);
                baseMapper.updateWorderStatus(map);
            }

            // 记录操作日志并且，记录审核日志
            // 保存操作记录
            WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
            worderOperationRecodeEntity.setUserId(user.getUserId());
            worderOperationRecodeEntity.setOperationUser(user.getEmployeeName());
            worderOperationRecodeEntity.setRecord("广汽工单审核失败工单回退,原因："+ status);
            worderOperationRecodeEntity.setWorderNo(worderNo);
            worderOperationRecodeEntity.setCreateTime(DateUtil.formatDateTime(new Date()));
            worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInfoEntity.getWorderStatus()));
            worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInfoEntity.getWorderExecStatus()));
            worderOperationRecodeDao.insert(worderOperationRecodeEntity);
            // 保存审核记录
            WorderAuditResultEntity worderAuditResultEntity = new WorderAuditResultEntity();
            worderAuditResultEntity.setWorderNo(worderNo);
            worderAuditResultEntity.setWorderStatus(worderAuditResultEntity.getWorderStatus());
            worderAuditResultEntity.setAuditUserId(user.getUserId());
            worderAuditResultEntity.setWorderAuditStatus(23);
            worderAuditResultEntity.setResult(user.getEmployeeName() + "：广汽工单审核失败工单回退,原因：");
            worderAuditResultEntity.setGmtCreate(DateUtil.formatDateTime(new Date()));
            worderAuditResultService.save(worderAuditResultEntity);
        }
        return R.ok();
    }


}
