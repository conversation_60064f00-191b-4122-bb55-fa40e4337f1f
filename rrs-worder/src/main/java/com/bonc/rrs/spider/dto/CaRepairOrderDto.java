package com.bonc.rrs.spider.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CaRepairOrderDto extends SubOrderDto {

    @ApiModelProperty(value = "充电桩是否在保")
    private String isPileSafety;

    @ApiModelProperty(value = "长安-品牌")
    private String changanBrand;

    @ApiModelProperty(value = "故障现象")
    private String faultDesc;

}
