<template>
  <div class="risk-task-execut-confirm">
    <div class="pa-title">
      <h2>任务信息</h2>
    </div>
    <task-info :task="taskItem"></task-info>

    <div class="pa-title"><h2>问卷列表</h2></div>
    <el-tabs class="pa20" v-model="edTabName" type="card">
      <el-tab-pane
        v-for="(item,index) in tabList"
        :key="item.sheetEnName"
        :label="item.sheetName"
        :name="item.sheetEnName"
        @click="tabClick()">
        <span slot="label" style="padding: 8px">
         {{item.sheetName}}
          <!--<span style="margin-left: 10px;"><i class="el-icon-edit" @click="addOrUpdateTab(item.id)"></i></span>-->
        </span>
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="评价范围" name="1" v-if="!item.relOption || item.relOption == '[]'">
            <!--<basic-item :sid="item.id" :companyId="companyId" :task="task"></basic-item>-->
            <base-item ref="basicItem" :sid="item.id" :tid="item.tid" :deptId="taskItem.deptId"
                       :uid="taskItem.receiveUserId || taskItem.sendUserId" :dkUserId="taskItem.dkUserId"
                       @getTemplateIds="getTemplateIds"
                       :view="view"
                       :isOver="true"
                       :showCheckbox="true"></base-item>
          </el-collapse-item>
          <el-collapse-item title="评价维度" name="3">
            <fill-item :sid="item.id" :tid="item.tid" :view="view"></fill-item>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
    </el-tabs>


    <el-form rel="form" label-width="80px" class="pa20">
      <el-form-item label="审核意见:">
        <el-input type="textarea" v-model="form.content"></el-input>
      </el-form-item>
      <el-form-item class="text-center">
        <el-button type="primary" @click="approved()" v-if="taskItem.taskStatus !=6 && taskItem.taskStatus !=7 ">通过
        </el-button>
        <el-button type="primary" @click="reject()"
                   v-if="(taskItem.taskStatus !=6 && taskItem.taskStatus !=7 ) || (taskItem.receiveUserId != userId && taskItem.sendUserId != userId )">
          驳回
        </el-button>
        <!--<el-button type="primary" @click="goback()">关闭</el-button>-->
      </el-form-item>
    </el-form>

    <audit-record :taskItem="taskItem" :ruid="taskItem.receiveUserId" :bizType="bizType"></audit-record>

  </div>
</template>
<script>
  import basicItem from './task-list-check-base-item';
  import taskInfo from './task-list-task-info';
  import fillItem from './template/fill-item';
  import baseItem from './template/basic-item';
  import auditRecord from './task-execut-audit-record';
  import { Loading } from 'element-ui';
  export default {

    components: {
      basicItem, taskInfo, fillItem, baseItem, auditRecord
    },
    data() {
      return {
        //bizType 1.流程审批  2.数据审批  3.任务回退
        bizType: '1',
        view: true,
        activeName: ['1'],
        edTabName: 'second',
        tabList: [],
        task: {},
        taskItem: {},
        taskDept: {},
        companyId: '',
        userId: '',
        auditList: [],
        form: {},
        basicIds: []
      }
    },
    created: function () {
      // this.task = this.$route.params.task;
      /*this.taskItem = this.$route.params.task;
      this.companyId = this.task.companyId; // 对应公司ID
      this.userId = this.getLoginUserId();
      this.initAuditList();
      this.queryTaskInfo(this.taskItem.id)*/

      let task_ = this.$route.params.task;
      if (task_ && this.taskItem.id !== task_.id) {
        this.$destroy();
      }
      if (task_) {
        this.task = this.$route.params.task;
        this.taskItem = this.$route.params.task;
        this.companyId = this.task.companyId; // 对应公司ID
        this.userId = this.getLoginUserId();
        // this.initAuditList();
        this.queryTaskInfo(this.taskItem.id)
      }
    },

    methods: {
      formatterStatus(row, column, cellValue, index) {
        return cellValue === 6 ? '同意' : '驳回';
      },
      goback(row) {
        this.$emit('closeCurrentTab')
      },
      queryTaskInfo(taskId) {
        let me = this;
        // 查询任务信息 evaluatetask
        this.$http({
          url: this.$http.adornUrl('/operational/evaluatetask/getTaskAndTempInfo'),
          method: 'get',
          params: this.$http.adornParams({taskId: taskId})
        }).then(({data}) => {
          me.task = data && data.code === 0 ? data.task : [];
          this.log('getTaskAndTempInfo=', me.task);
          // 查询sheet列表
          this.getTabList(me.task.tempId);
        });
      },
      getTabList(tempId) {
        let me = this;
        this.$http({
          url: this.$http.adornUrl('/operational/templatesheet/select'),
          method: 'get',
          params: this.$http.adornParams({tid: tempId})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tabList = data.data;
            me.edTabName = this.tabList[0].sheetEnName;
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      initAuditList: function () {
        this.$http({
          url: this.$http.adornUrl('/operational/taskauditrecord/list'),
          method: 'get',
          params: this.$http.adornParams({taskId: this.taskItem.id, orderBy: 'create_time desc'})
        }).then(({data}) => {
          this.log('upastatus  =', data);
          if (data && data.code === 0) {
            this.auditList = data.datas;
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      reject() {
        if (this.basicIds.length === 0) {
          this.$message.warning('请选择流程!');
          return;
        }
        this.$confirm('确认是否驳回?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/operational/basic/rejectQuestionBasic'),
            method: 'get',
            params: this.$http.adornParams({
              taskId: this.taskItem.id,
              tempId: this.taskItem.tempId,
              deptId: this.taskItem.deptId,
              content: this.form.content,
              ruid: this.taskItem.receiveUserId,
              suid: this.taskItem.sendUserId,
              dkbmUid: this.taskItem.dkbmUid,
              dkUid: this.taskItem.dkUserId,
              bizType: 1,
              astatus: 7,
              basicIds: this.basicIds.join(',')
            })
            // params: {params:this.currRow}
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              // this.taskItem.taskStatus = 7;
              this.alert('操作成功！');
              this.$changeRouter('risk-task-execut')
              this.closeThisTab()
            } else {
              this.alert('操作失败，请重试。');
            }
            // this.initAuditList();
          });
        }).catch(() => {
          this.alert('取消!');
        });
      },
      // ----------------------------关闭页签
      closeThisTab() {
        let com = this;
        let i = 0;
        while (!com.tabsCloseCurrentHandle && i < 20) {
          com = com.$parent;
          i++;
        }
        console.log('parent level = ', i); // router-view -> 4
        com.tabsCloseCurrentHandle();
        // this.$emit('closeCurrentTab')
      },
      approved() {
        this.$confirm('确认是否通过?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/operational/basic/upastatus'),
            method: 'get',
            params: this.$http.adornParams({
              taskId: this.taskItem.id,
              tempId: this.taskItem.tempId,
              deptId: this.taskItem.deptId,
              content: this.form.content,
              //ruid: this.taskItem.receiveUserId,
              //suid: this.taskItem.bkbmUid,
              ruid: this.taskItem.receiveUserId,
              suid: this.taskItem.sendUserId,
              dkbmUid: this.taskItem.dkbmUid,
              bizType: 1,
              astatus: 6
            })
            // params: {params:this.currRow}
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              // this.taskItem.taskStatus = 6;
              this.alert('操作成功！');
              this.$changeRouter('risk-task-execut')
              this.closeThisTab()
            } else {
              this.alert('操作失败，请重试。');
            }
            // this.initAuditList();
          });
        }).catch(() => {
          this.alert('取消!');
        });
      },
      getTemplateIds(selectList) {
        this.basicIds = selectList
      }
    }

  }
</script>
