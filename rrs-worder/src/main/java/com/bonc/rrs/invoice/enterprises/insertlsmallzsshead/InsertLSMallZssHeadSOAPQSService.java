package com.bonc.rrs.invoice.enterprises.insertlsmallzsshead;

import com.bonc.rrs.wsdlproperties.WsdlProperties;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import java.net.URL;

/**
 * This class was generated by Apache CXF 3.1.7
 * 2020-02-07T11:04:46.060+08:00
 * Generated source version: 3.1.7
 * 
 */
@WebServiceClient(name = "InsertLSMallZssHeadSOAPQSService", 
                  wsdlLocation = "${headlocation}",
                  targetNamespace = "http://www.example.org/InsertLSMallZssHead/") 
public class InsertLSMallZssHeadSOAPQSService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://www.example.org/InsertLSMallZssHead/", "InsertLSMallZssHeadSOAPQSService");
    public final static QName InsertLSMallZssHeadSOAPQSPort = new QName("http://www.example.org/InsertLSMallZssHead/", "InsertLSMallZssHeadSOAPQSPort");
    static {
        //URL url = InsertLSMallZssHeadSOAPQSService.class.getClassLoader().getResource("InsertLSMallZssHead.wsdl");
        URL url = InsertLSMallZssHeadSOAPQSService.class.getClassLoader().getResource(WsdlProperties.headurl);
        if (url == null) {
            java.util.logging.Logger.getLogger(InsertLSMallZssHeadSOAPQSService.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "classpath:InsertLSMallZssHead.wsdl");
        }       
        WSDL_LOCATION = url;   
    }

    public InsertLSMallZssHeadSOAPQSService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public InsertLSMallZssHeadSOAPQSService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public InsertLSMallZssHeadSOAPQSService() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    public InsertLSMallZssHeadSOAPQSService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public InsertLSMallZssHeadSOAPQSService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public InsertLSMallZssHeadSOAPQSService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    




    /**
     *
     * @return
     *     returns InsertLSMallZssHead
     */
    @WebEndpoint(name = "InsertLSMallZssHeadSOAPQSPort")
    public InsertLSMallZssHead getInsertLSMallZssHeadSOAPQSPort() {
        return super.getPort(InsertLSMallZssHeadSOAPQSPort, InsertLSMallZssHead.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns InsertLSMallZssHead
     */
    @WebEndpoint(name = "InsertLSMallZssHeadSOAPQSPort")
    public InsertLSMallZssHead getInsertLSMallZssHeadSOAPQSPort(WebServiceFeature... features) {
        return super.getPort(InsertLSMallZssHeadSOAPQSPort, InsertLSMallZssHead.class, features);
    }

}
