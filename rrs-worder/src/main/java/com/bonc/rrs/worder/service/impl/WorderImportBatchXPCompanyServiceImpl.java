package com.bonc.rrs.worder.service.impl;

import com.bonc.rrs.worder.constant.BrandEnum;
import com.bonc.rrs.worder.constant.TemplateEnum;
import com.bonc.rrs.worder.dto.dto.WorderImportBatchDTO;
import com.bonc.rrs.worder.dto.vo.WorderImportBatchVo;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/30 17:31
 */
@Service
@Scope("prototype")
@Slf4j
public class WorderImportBatchXPCompanyServiceImpl extends AbstractWorderImportBatch {

    @Override
    protected Integer getBrandId() {
        return BrandEnum.XP.getId();
    }

    @Override
    public void setTemplateId() {
        addTemplateId(TemplateEnum.XP_JC0MTB_ZXQ.getId());
        addTemplateId(TemplateEnum.XP_JC0MTB_HBQ.getId());
        addTemplateId(TemplateEnum.XP_JC30MTB.getId());
        addTemplateId(TemplateEnum.XP_YKCZH.getId());
    }

    @Override
    public WorderInfoEntity getData(WorderImportBatchDTO worderImportBatchDTO, WorderImportBatchVo worderImportBatchVo) {
        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address = worderImportBatchDTO.getJ() + "_" + worderImportBatchDTO.getL() + "_" + worderImportBatchDTO.getN() + "_" + worderImportBatchDTO.getO();

        String userName = worderImportBatchDTO.getG() + "(" + worderImportBatchDTO.getR()+")";
        worderInfoEntity.setUserName(userName);
        worderInfoEntity.setUserPhone(worderImportBatchDTO.getH());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(worderImportBatchDTO.getF());
        worderInfoEntity.setTemplateId(worderImportBatchVo.getTemplateId());

        worderInfoEntity.setCarBrand(BrandEnum.ZYSDHW.getId().toString());
        worderInfoEntity.setCarModel("4");
        //
        String companyId = worderImportBatchDTO.getD();
        if (StringUtils.isBlank(companyId)) {
            companyId = "654";
        }
        worderInfoEntity.setCompanyId(Integer.parseInt(companyId));
        // 360校验厂商id
        setCheckResult360(Integer.parseInt(companyId), 1);
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setWorderTypeId(getworderTypeId(worderImportBatchDTO.getC()));

        worderInfoEntity.setCandidate("系统自动");
        worderInfoEntity.setCreator(89L);
        worderInfoEntity.setCreateBy(worderImportBatchVo.getUserId());
        worderInfoEntity.setWorderSourceTypeValue(worderImportBatchDTO.getP());

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();



        worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", 5));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderImportBatchDTO.getF()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", companyId));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(940, "车企系统订单编号", worderImportBatchDTO.getQ()));



        worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", worderImportBatchDTO.getP()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", userName));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderImportBatchDTO.getH()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(911, "联系信息备注", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(939, "车企品牌", "小鹏"));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(952, "业务类型", StringUtils.isBlank(worderImportBatchDTO.getU())? "安装" : worderImportBatchDTO.getU()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1224, "是否预勘测订单", StringUtils.isBlank(worderImportBatchDTO.getS())? "否" : worderImportBatchDTO.getS()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1225, "是否预勘测转化成功", ""));

        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
        return worderInfoEntity;
    }


}
