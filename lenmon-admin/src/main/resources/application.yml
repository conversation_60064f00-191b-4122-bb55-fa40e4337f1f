# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
  port: 8081
  connection-timeout: 5000ms
  servlet:
    context-path: /rrs

spring:
  activiti:
    check-process-definitions: true.

  # 环境 dev|test|prod/aldev
  profiles:
    active: test
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
#  resources:
#    add-mappings: false

swagger:
  enable: false

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.youngking.renrenwithactiviti.modules.*.entity;com.youngking.activiti.modules.act.entity;com.bonc.rrs.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      column-underline: true
      logic-delete-value: -1
      logic-not-delete-value: 0
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


renren:
  redis:
    open: false
  shiro:
    redis: false
  # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
  jwt:
    # 加密秘钥
    secret: f4e2e52034348f86b67cde581c0f9eb5[www.renren.io]
    # token有效时长，7天，单位秒
    expire: 604800
    header: token
baseUrl: www.baidu.com
worderprocess:
  definitionid: worderProcess:1:10

scheduler:
  group: rrs_back
  start: false
#sync:
#  #车企开票状态查询 定时
#  worderInvoice: 0 25 11 * * ?
#  #工单结算发布 定时
#  worderBalance: 0 30 11 * * ?
#  #增项结算发布 定时
#  worderIncre: 0 31 11 * * ?
#  #激励发布 定时
#  worderExci: 0 32 11 * * ?
#   #工单已结算 定时
#  balancePublish: 0 33 11 * * ?
#  #增项已结算 定时
#  increPublish: 0 34 11 * * ?
#  #激励已结算 定时
#  exciPublish: 0 35 11 * * ?

balance:
  ROUND_MODE: 6 #结算金额计算的舍入模式，使用BigDecimal的舍入模式 6：四舍六入五成双（银行家舍入法） 4：四舍五入 1：截取
  RRS_COMPANY_CODE: 0RK0 #日日顺公司代码
  RRS_COMPANY_NAME: 到每家科技服务（上海）有限公司 #日日顺公司名称
  RRS_COMPANY_TAX_NO: 91310117MA1J31BJ0W #日日顺公司税号
  RRS_COMPANY_MOBILE: 021-******** #日日顺电话号
  RRS_COMPANY_ADDRESS: 上海市闵行区新龙路500弄16、17、22、25号701-703单元 #日日顺公司地址
  RRS_COMPANY_BANK: 中国建设银行股份有限公司上海番禺路支行 #日日顺公司开户行
  RRS_COMPANY_BANK_NO: 31050174400000000657 #日日顺公司银行账号
  RRS_COMPANY_ACCOUNT_MAP:  #日日顺各平台的收款账号
    2: **********@********** #微信收款账号
    6: 31050174400000000657 #电汇收款账号，日日顺建行卡号，工单结算使用
  PRODUCT_UNIT:  #单位
  RRS_COMPANY_TAX_RATE: '0.09' #日日顺对厂商收款的税率
  RRS_USER_TAX_RATE: '0.09' #日日顺对用户收款的税率
  PRODUCT_TAX_CODE: '3050200000000000000' #产品分类税码 --金税接口使用
  INVOICE_TYPE: '0' #发票种类(0专用发票 1普通发票 3电子发票) --金税接口使用
  PAYEE: 王晴 #日日顺收款人 --金税接口使用
  REVIEWER: 陈悦 #日日顺复核人 --金税接口使用 陈悦或者杨鸿雁
  GOODS_NO:  #商品代码 --金税接口使用
  GOODS_NAME: '安装服务' #商品名称 --金税接口使用
  GOODS_MODEL:  #商品型号 --金税接口使用
  BALANCE_STAFF_CODE: '********' #结算员工号 --商户通接口使用
  BALANCE_STAFF_NAME: 郭红宇 #结算员姓名 --商户通接口使用
  BUDGET_CODE: BD1001070960 #预算体编码 --商户通接口使用
  BUDGET_NAME: 新能源-总部 #预算体名称 --商户通接口使用
  FEE_TYPE_CODE: '3' #费用类型代码 --商户通接口使用
  FEE_TYPE_NAME: 桩联网 #费用类型名称 --商户通接口使用
  DOT_WORDER_USE_BALANCE_RATE: true #网点工单结算是否乘以网点对应星级的结算系数
  DOT_INCRE_USE_BALANCE_RATE: false #网点增项结算是否乘以网点对应星级的结算系数
  INVOICE_LIMIT_PRICE: 108891    #开票含税限额
  NO_TAX_INVOICE_LIMIT_PRICE: 99900    #开票不含税限额

invoice:
  APP_ID: news
  KEY: XA12#Da
  URL1: http://47.104.102.3:8001/finance/carMergerOrder/insertOrUpdate
