package com.bonc.rrs.worderapp.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhangyibo on 2020-03-19 13:55
 */

@Data
public class WorderInformationVo implements Serializable {

    private Integer worderId;
    // 工单编号
    private String worderNo;
    // 工单状态
    private Integer worderStatus;
    // 勘测图片
    private String conveySignPicture;
    // 安装图片
    private String installSignPicture;

    // 勘测时间
    private String conveyAppointTime;
    // 安装时间
    private String installAppointTime;
    // 勘测签到地点
    private String conveySignArea;
    // 安装签到地点
    private String installSignArea;
    // 勘测签到时间
    private String conveySignTime;
    // 安装签到时间
    private String installSignTime;

    //经度
    private String longitude;

    //维度
    private  String latitude;
    //备注内容
    private String remake;

    private String noInstallReason;

    private String worderLevel;
}
