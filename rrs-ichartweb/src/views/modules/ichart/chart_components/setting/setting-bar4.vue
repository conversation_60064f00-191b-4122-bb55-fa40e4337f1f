<template>
  <div class="chart-card" style="width:100%;height: 100%;padding: 5px 10px; border:0px solid #e0e0e0">
    <el-tabs v-model="activeName">
      <el-tab-pane label="数据设置" name="data">
        <el-form label-width="80px">
          <son-chart ref="sonPie" :tableTitle="tableTitle" :params="params" :currObj="currObj"
                     @screenField="screenField" @setDataName="setDataName"
                     :layout="layout"></son-chart>
          <div class="pt20">
            <setting-font :font="font" :set-font="setFont"/>
          </div>
          <!-- =========================================== 横轴 ============================================== -->
          <div class="pt20">
            <el-row class="data-name-title">
              <el-col :span="12"><label>横轴</label></el-col>
            </el-row>
            <el-row class="data-name-con">
              <el-row style="margin: 3% 5%" v-for="(item,index) in xAxis" :key="index">
                <label style="display: inline-block;width: 30%">列名</label>
                <el-select size="mini" v-model="item.columnName" placeholder="列名">
                  <el-option v-for="item in tableTitle"
                             :key="item.id"
                             :label="item.displayName"
                             :value="item.fieldName"></el-option>
                </el-select>
              </el-row>
              <!--              <el-row style="margin: 3% 5%" v-for="item in xAxis">-->
              <!--                <el-col style="display: inline-block;width: 30%"><label-->
              <!--                  style="display: inline-block;width: 100%">固定值</label></el-col>-->

              <!--                <el-col style="display: inline-block;width: 50%">-->
              <!--                  <el-row v-for="(item,index) in category_items">-->
              <!--                    <el-input v-model="category_items[index]" size="mini" placeholder="名称"-->
              <!--                              style="width: 60%"></el-input>-->
              <!--                    <el-button-->
              <!--                      @click.native.prevent="removeFixVal(index)"-->
              <!--                      type="text"-->
              <!--                      size="small">-->
              <!--                      删除-->
              <!--                    </el-button>-->
              <!--                    <el-button-->
              <!--                      @click.native.prevent="addFixVal(index)"-->
              <!--                      type="text"-->
              <!--                      size="small">-->
              <!--                      新增-->
              <!--                    </el-button>-->
              <!--                  </el-row>-->
              <!--                </el-col>-->
              <!--                <el-col style="display: inline-block;width: 20%">-->
              <!--                </el-col>-->
              <!--              </el-row>-->
            </el-row>
          </div>

          <!-- =========================================== 纵轴 ============================================== -->
          <div class="pt20">
            <el-row class="data-name-title">
              <el-col :span="12"><label>纵轴</label></el-col>
            </el-row>
            <el-row class="data-name-con">
              <el-row style="margin: 3% 5%" v-for="(item,index) in yAxis" :key="index">
                <label style="display: inline-block;width: 30%">纵轴 {{index+1}}</label>
                <el-input v-model="item.name" size="mini" placeholder="名称" style="width: 50%"></el-input>
              </el-row>
            </el-row>
          </div>
          <!-- =========================================== 纵轴 ============================================== -->

          <!-- =========================================== 系列 ============================================== -->
          <div class="pt20">
            <el-row class="data-name-title">
              <el-col :span="12"><label>系列</label></el-col>
              <el-col :span="12" style="text-align: right">
                <span type="info"
                      style="font-size: 14px;color: #15A193;cursor: pointer"
                      @click="addSeries()"> <i class="el-icon-plus nav_btn_i">新增</i>
                </span>
              </el-col>
            </el-row>
            <el-row v-for="(item,index) in series" :key="index">
              <el-row>
                <el-col :span="20">
                  <el-row style="margin: 3% 5%">
                    <label style="display: inline-block;width: 30%">类型</label>
                    <el-radio-group v-model="item.columnName.type" size="mini" @change="onRadio(item)">
                      <el-radio-button label="col">普通</el-radio-button>
                      <el-radio-button label="exp">表达式</el-radio-button>
                    </el-radio-group>
                  </el-row>
                  <div v-if="item.columnName.type=='col'">

                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">所属纵轴</label>
                      <el-select style="width: 50%" v-model="item.yAxisIndex" size="mini" placeholder="列名称">
                        <el-option v-for="(item,index) in yAxis"
                                   :key="item.name"
                                   :label="item.name"
                                   :value="index"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">展示方式</label>
                      <el-select style="width: 50%" size="mini" placeholder="展示方式"
                                 v-model="item.columnName.showType">
                        <el-option label="柱状" value="bar"></el-option>
                        <el-option label="折线" value="line"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">列名称</label>
                      <el-select style="width: 50%" size="mini" v-model="item.columnName.colName" placeholder="列名称"
                                 @change="upName(item)">
                        <el-option v-for="item in tableTitle"
                                   :key="item.id"
                                   :label="item.displayName"
                                   :value="item.fieldName"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">名称</label>
                      <el-input style="width: 50%" v-model="item.name" size="mini" placeholder="名称"></el-input>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">统计方式</label>
                      <el-select style="width: 50%" size="mini" placeholder="统计方式"
                                 v-model="item.columnName.statisticsType">
                        <el-option label="求和" value="sum"></el-option>
                        <el-option label="平均值" value="avg"></el-option>
                        <el-option label="数量" value="count"></el-option>
                        <el-option label="数量(去重)" value="count_distinct"></el-option>
                        <el-option label="最大值" value="max"></el-option>
                        <el-option label="最小值" value="min"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <div style="float: left;height: 28px;line-height: 28px;width: 31%">颜色</div>
                      <el-color-picker
                        v-model="item.columnName.color"
                        :predefine="predefineColors"
                        size="mini">
                      </el-color-picker>
                    </el-row>
                  </div>
                  <!--                  &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->
                  <div v-if="item.columnName.type=='exp'">
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">所属纵轴</label>
                      <el-select style="width: 50%" v-model="item.yAxisIndex" size="mini" placeholder="列名称">
                        <el-option v-for="(item,index) in yAxis"
                                   :key="item.name"
                                   :label="item.name"
                                   :value="index"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">展示方式</label>
                      <el-select style="width: 50%" size="mini" placeholder="展示方式"
                                 v-model="item.columnName.showType">
                        <el-option label="柱状" value="bar"></el-option>
                        <el-option label="折线" value="line"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">名称</label>
                      <el-input style="width: 50%" v-model="item.name" size="mini" placeholder="名称"></el-input>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">参数1</label>
                      <el-select style="width: 50%" v-model="item.columnName.exp.val1" size="mini" placeholder="列名称">
                        <el-option v-for="item in tableTitle"
                                   :key="item.id"
                                   :label="item.displayName"
                                   :value="item.fieldName"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">操作符</label>
                      <el-select style="width: 50%" size="mini" placeholder="操作符"
                                 v-model="item.columnName.exp.op">
                        <el-option label="+" value="sum"></el-option>
                        <el-option label="-" value="avg"></el-option>
                        <el-option label="*" value="count"></el-option>
                        <el-option label="/" value="count_distinct"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">参数2</label>
                      <el-select style="width: 50%" size="mini" placeholder="请选择列名称" v-model="item.columnName.exp.val2">
                        <el-option v-for="item in tableTitle"
                                   :key="item.id"
                                   :label="item.displayName"
                                   :value="item.fieldName"></el-option>
                      </el-select>
                    </el-row>

                    <el-row class="data-name-con">
                      <label style="display:inline-block;width: 30%">统计方式</label>
                      <el-select style="width: 50%" size="mini" placeholder="统计方式"
                                 v-model="item.columnName.statisticsType">
                        <el-option label="求和" value="sum"></el-option>
                        <el-option label="平均值" value="avg"></el-option>
                        <el-option label="数量" value="count"></el-option>
                        <el-option label="数量(去重)" value="count_distinct"></el-option>
                        <el-option label="最大值" value="max"></el-option>
                        <el-option label="最小值" value="min"></el-option>
                      </el-select>
                    </el-row>
                    <el-row class="data-name-con">
                      <div style="float: left;height: 28px;line-height: 28px;width: 31%">颜色</div>
                      <el-color-picker
                        v-model="item.columnName.color"
                        :predefine="predefineColors"
                        size="mini">
                      </el-color-picker>
                    </el-row>
                  </div>
                </el-col>
                <el-col :span="4" style="text-align: center;vertical-align: middle">
                  <el-button
                    @click.native.prevent="removeSeries(index)"
                    type="text"
                    size="small">
                    删除
                  </el-button>
                </el-col>
              </el-row>
              <el-row style="padding-left:30px">
                <hr/>
              </el-row>
            </el-row>

            <el-row class="data-name-con">
              <!--              {{currObj}}-->
              <!--              <el-row v-for="item in series">-->
              <!--                <el-row v-if="item.columnName && item.columnName.exp">-->
              <!--                  <el-col :span="24">-->
              <!--                    <label style="width: 100px;display: inline-block;">类型</label>-->
              <!--                    &lt;!&ndash;                    <el-radio v-model="radio" label="1">备选项</el-radio>&ndash;&gt;-->
              <!--                    &lt;!&ndash;                    <el-radio v-model="radio" label="2">备选项</el-radio>&ndash;&gt;-->
              <!--                    <el-radio-group  v-model="item.columnName.type" size="mini">-->
              <!--                      <el-radio-button label="col" >普通</el-radio-button>-->
              <!--                      <el-radio-button label="exp">表达示</el-radio-button>-->
              <!--                    </el-radio-group>-->
              <!--                  </el-col>-->
              <!--                </el-row>-->

              <!--                <el-row v-if="item.columnName && !item.columnName.exp">-->
              <!--                  <br/>-->
              <!--                  <el-col :span="8">-->
              <!--                    <el-select size="mini" v-model="item.columnName" placeholder="列名称">-->
              <!--                      <el-option v-for="item in tableTitle"-->
              <!--                                 :key="item.id"-->
              <!--                                 :label="item.displayName"-->
              <!--                                 :value="item.fieldName"></el-option>-->
              <!--                    </el-select>-->
              <!--                  </el-col>-->
              <!--                  <el-col :span="8">-->
              <!--                    <el-input v-model="item.name" size="mini" placeholder="名称"></el-input>-->
              <!--                  </el-col>-->
              <!--                  <el-col :span="8">-->
              <!--                    <el-select size="mini" v-model="item.columnName" placeholder="类型">-->
              <!--                      <el-option v-for="item in rptTypeList"-->
              <!--                                 :key="item.val"-->
              <!--                                 :label="item.name"-->
              <!--                                 :value="item.val"></el-option>-->
              <!--                    </el-select>-->
              <!--                  </el-col>-->
              <!--                </el-row>-->
            </el-row>

            <!--              <el-table-->
            <!--                :data="series"-->
            <!--                style="width: 100%"-->
            <!--                :header-cell-style="{background:'#F5F4F4',color:'#606266'}">-->
            <!--                <el-table-column-->
            <!--                  prop="name"-->
            <!--                  label="列名">-->
            <!--                  <template slot-scope="scope">-->
            <!--                    <el-select size="mini" v-model="scope.row.columnName" placeholder="请选择列名称"-->
            <!--                    >-->
            <!--                      <el-option v-for="item in tableTitle"-->
            <!--                                 :key="item.id"-->
            <!--                                 :label="item.displayName"-->
            <!--                                 :value="item.fieldName"></el-option>-->
            <!--                    </el-select>-->
            <!--                  </template>-->
            <!--                </el-table-column>-->
            <!--                <el-table-column-->
            <!--                  prop=""-->
            <!--                  label="名称"-->
            <!--                >-->
            <!--                  <template slot-scope="scope">-->
            <!--                    <el-input v-model="scope.row.name" size="mini" placeholder="名称"></el-input>-->
            <!--                  </template>-->
            <!--                </el-table-column>-->
            <!--                <el-table-column-->
            <!--                  label="操作"-->
            <!--                  width="50px">-->
            <!--                  <template slot-scope="scope">-->
            <!--                    <el-button-->
            <!--                      @click.native.prevent="removeSeries(scope.$index)"-->
            <!--                      type="text"-->
            <!--                      size="small">-->
            <!--                      删除-->
            <!--                    </el-button>-->
            <!--                  </template>-->
            <!--                </el-table-column>-->
            <!--              </el-table>-->
<!--            </el-row>-->
          </div>

<!--          <div class="pt20">-->
<!--            <el-row class="data-name-title">-->
<!--              <el-col :span="12"><label>固定值</label></el-col>-->
<!--            </el-row>-->
<!--            <el-row class="data-name-con">-->
<!--              <el-row style="margin: 3% 5%" v-for="item in xAxis">-->
<!--                <el-col style="display: inline-block;width: 30%"><label-->
<!--                  style="display: inline-block;width: 100%">固定值</label></el-col>-->

<!--                <el-col style="display: inline-block;width: 50%">-->
<!--                  <el-row v-for="(item,index) in category_items">-->
<!--                    <el-input v-model="category_items[index]" size="mini" placeholder="名称"-->
<!--                              style="width: 60%"></el-input>-->
<!--                    <el-button-->
<!--                      @click.native.prevent="removeFixVal(index)"-->
<!--                      type="text"-->
<!--                      size="small">-->
<!--                      删除-->
<!--                    </el-button>-->
<!--                    <el-button-->
<!--                      @click.native.prevent="addFixVal(index)"-->
<!--                      type="text"-->
<!--                      size="small">-->
<!--                      新增-->
<!--                    </el-button>-->
<!--                  </el-row>-->
<!--                </el-col>-->
<!--                <el-col style="display: inline-block;width: 20%">-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--            </el-row>-->
<!--          </div>-->

          <!-- =========================================== 刷新 ============================================== -->
          <el-row class="data-name-title refresh" style="text-align: center">
            <span type="info" style="font-size: 14px;color: #15A193;cursor: pointer"
                  @click="settingRefresh()">
                <i class="el-icon-plus1 nav_btn_i">刷新</i>
            </span>
            <span type="info" style="font-size: 14px;color: #15A193;cursor: pointer"
                  @click="removeEchartItem()">
                <i class="el-icon-plus1 nav_btn_i">删除</i>
            </span>
          </el-row>

          <!-- =========================================== 系列 ============================================== -->
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="样式设置" name="style">
        <son-bar2-SetUp @exeParentRefresh="exeParentRefresh" :params="params" :currObj="currObj"></son-bar2-SetUp>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      :modal="true"
      :close-on-click-modal="false"
      width="50%"
      title="新增图例名称"
      :visible.sync="LegendVisible"
      append-to-body>
      <div style="text-align: center;margin-bottom: 20px;">
        <label>列名</label>
        <el-input v-model="LegendValue" style="width: 50%;"></el-input>
      </div>
      <div class="text-center">
        <el-button type="primary" @click="addLegend">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import sonChart from './subclass/son-chart';
  import SonBar2SetUp from './subclass/son-bar2-SetUp';
  import SettingFont from './setting-font';
  // 记录当前card下的所有的组件引用
  export default {
    props: {
      params: Object,
      currObj: Object,
      layout: Array
    },
    data() {
      return {
        currCard: null,
        activeName: 'data',
        dataSelect: '',
        tableTitle: [],
        typeList: [{name: '分类', val: 'category'}, {name: '值', val: 'value'}],
        calcOpt: [{name: '+', val: '+'}, {name: '-', val: '-'}, {name: '*', val: '*'}, {name: '/', val: '/'}],
        rptTypeList: [{name: '柱', val: 'bar'}, {name: '线', val: 'line'}],
        LegendVisible: false,
        LegendValue: '',
        //提取
        tableExtract: [],
        predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585'
        ],
        seriesData: [],
        //图例显示和隐藏
        Legend: false,
        color: [],
        yAxis: [{columnName: ''}],
        xAxis: [{columnName: ''}],
        series: [{}],
        showSetting: {},
        category_items: [],
        radio: '普通',
        color1: '',
        tongji: '',
        qiehuan: true,
        qiehuan1: false,
        font: {
          color: '#333',
          fontSize: 12,
          fontStyle: 'normal'
        }
      }
    },
    components: {
      sonChart: sonChart,
      SonBar2SetUp: SonBar2SetUp,
      SettingFont: SettingFont
    },
    methods: {
      //点击切换类型
      onRadio(item) {
        let type = item.columnName.type;
        if (type === 'col') {
          item.columnName = {'type': type, statisticsType: 'sum'};
        } else {
          item.columnName = {
            'type': 'exp',
            'exp': {
              'val1': '',
              'op': '/',
              'val2': ''
            },
            'cName': 'wc'
          };
        }
      },
      addXAxis(item) {
        let obj = JSON.parse(JSON.stringify(this.currObj.option.xAxis[0]));
        obj.columnName = '';
        this.xAxis.push(obj);
      },
      removeXAxis(ind) {
        this.xAxis.splice(ind, 1);
      },

      addYAxis(item) {
        let obj = JSON.parse(JSON.stringify(this.currObj.option.yAxis[0]));
        obj.columnName = '';
        if (this.yAxis.length == 2) {
          alert('纵轴最多支持2个！');
          return null;
        }
        this.yAxis.push(obj);
      },
      removeYAxis(ind) {
        this.yAxis.splice(ind, 1);
      },

      addSeries(item) {
        let obj = JSON.parse(JSON.stringify(this.currObj._option.series[0]));
        obj.columnName = {'type': 'col', statisticsType: 'sum'};
        this.series.push(obj);
      },
      removeSeries(ind) {
        this.series.splice(ind, 1);
      },

      addFixVal(item) {
        this.category_items.push('');
      },
      removeFixVal(ind) {
        this.category_items.splice(ind, 1);
      },

      setDataName(name) {
        this.dataSelect = name;
      },
      removeEchartItem() {
        this.getCurrCard();
        let that = this;
        this.layout.forEach((item) => {
          if (item.card.charts) {
            item.card.charts = item.card.charts.filter(function (item1) {
              return item1 != that.currObj
            });
          }
        })
        this.exeParentChangeCurrCard();
      },
      // 执行最上层父级对像的
      exeParentChangeCurrCard(obj) {
        let com = this;
        let i = 0;
        while (com && !com.changeCurrObj && i < 20) {
          com = com.$parent;
          i++;
        }
        if (com && com.changeCurrObj) {
          com.changeCurrObj(this.currCard);
        } else {
        }
      },
      getCurrCard() {
        let that = this;
        this.layout.forEach((item) => {
          if (item.card.charts) {
            item.card.charts.forEach(function (item1) {
              if (item1 == that.currObj) {
                that.currCard = item.card;
              }
            });
          }
        })
      },
      settingRefresh() {
        this.syncData();
        this.exeParentRefresh();
      },
      //找到父级对应的方法进行修改图表
      exeParentRefresh() {
        let com = this;
        let i = 0;
        while (!com._upQueryCurrObj && i < 20) {
          com = com.$parent;
          i++;
        }
        com._upQueryCurrObj(this.currObj);
      },
      //数据设置
      //选中的系列名称下的列名称
      // onChange1(val) {
      //   this.currObj._option.dsSetting.nameColName = val;
      // },
      //数据筛选列名列表渲染
      screenField(data) {
        this.$http({
          // url: this.$http.adornUrl('/report/ReportDataSourceitem/uptateBygetItem'),
          url: this.$http.adornUrl('/ichart/dataSourceItem/getDataSourceByNameAndCode'),
          method: 'get',
          params: this.$http.adornParams({
            'tableCode': data,
            'type': "1"
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tableTitle = data.data;
          }
        })
      },
      //新增图例
      addlog() {
        this.LegendVisible = true
      },
      addLegend() {
        var list = {
          name: this.LegendValue,
          value: 0,
          color: '#' + Math.random().toString(16).slice(2, 8)
        }
        this.seriesData.unshift(list)
        this.LegendVisible = false
        this.exeParentRefresh()
        this.LegendValue = ''
      },
      //图例修改颜色
      changeColor() {
        this.exeParentRefresh()
      },
      //删除图例数据
      deleteSeries(index, rows) {
        rows.splice(index, 1);
        this.currObj._option.series[0].data = rows
        this.exeParentRefresh()
      },
      deleteRow(index, rows) {
        rows.splice(index, 1);
      },
      //样式设置
      setFont(font) {
        this.font = font;
      },
      setAxis(item) {
        if (!item.axisLabel) { item.axisLabel = {} }
        item.axisLabel["color"] = this.font.color;
        item.axisLabel["fontSize"] = this.font.fontSize;
        item.axisLabel["fontStyle"] = this.font.fontStyle;
        item.nameTextStyle = this.font;
        if (this.font.fontSize === "") {
          delete item.axisLabel.fontSize
        }
      },
      syncData() {
        this.currObj._option.tableName = this.dataSelect;
        this.currObj._option.showSetting = this.showSetting;
        this.currObj._option.category_items = this.category_items;
        this.currObj.option.xAxis = this.xAxis.map(item => {
          this.setAxis(item)
          return item;
        })
        this.currObj.option.yAxis = this.currObj.option.yAxis.map((item, index) => {
          this.setAxis(item)
          let temp = this.series.find(d => d.yAxisIndex === index)
          if (temp) {
            item.name = temp.name
          }
          return item;
        })
        if (this.currObj._option.hasOwnProperty('legend')) {
          this.currObj._option.legend = {...this.currObj._option.legend, textStyle: this.font}
        }
        this.currObj._option.yAxis = this.currObj.option.yAxis;
        this.currObj._option.xAxis = this.currObj.option.xAxis;
        this.currObj._option.series = this.series;
        this.currObj.option.series = this.series;
      },
      upName(sitem) {
        let col = this.tableTitle.filter(item => item.fieldName === sitem.columnName.colName)
        if (col && col.length > 0) {
          sitem.name = col[0].displayName;
        }
      }
      //样式设置
    },
    created() {
      if (this.currObj._option.hasOwnProperty('legend') && this.currObj._option.legend.hasOwnProperty('textStyle')) {
        this.font = this.currObj._option.legend.textStyle;
      }
    },
    mounted() {
      this.dataSelect = this.currObj._option.tableName;
      this.yAxis = this.currObj._option.yAxis;
      this.xAxis = this.currObj._option.xAxis;
      this.showSetting = this.currObj._option.showSetting;
      this.category_items = this.currObj._option.category_items;
      this.series = this.currObj._option.series;
    }
  }
</script>
<style>
  .chart-card .el-tabs__item {
    width: 65%;
    text-align: center
  }

  .filter-1 .el-select {
    width: 100%;
  }

  .data-name {
    padding: 5px 10px;
    border-bottom: 1px solid #eee;
    line-height: 30px
  }

  .data-name-title {
    padding: 5px 0px;
    border-bottom: 1px solid #eee;
    color: #000;
    font-size: 16px
  }

  .data-name-con {
    margin: 3% 5%
  }

  /*.data-name-con label {*/
  /*  display: inline-block;*/
  /*  width: 20%*/
  /*}*/
</style>
