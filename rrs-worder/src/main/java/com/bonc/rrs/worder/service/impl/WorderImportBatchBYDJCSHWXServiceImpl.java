package com.bonc.rrs.worder.service.impl;

import com.bonc.rrs.worder.constant.BrandEnum;
import com.bonc.rrs.worder.constant.TemplateEnum;
import com.bonc.rrs.worder.dto.dto.WorderImportBatchDTO;
import com.bonc.rrs.worder.dto.vo.WorderImportBatchVo;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/30 17:31
 */
@Service
@Scope("prototype")
@Slf4j
public class WorderImportBatchBYDJCSHWXServiceImpl extends AbstractWorderImportBatch {

    @Override
    protected Integer getBrandId() {
        return BrandEnum.BYD.getId();
    }

    @Override
    public void setTemplateId() {
        addTemplateId(TemplateEnum.BYD_JCSHWX.getId());
    }

    @Override
    public WorderInfoEntity getData(WorderImportBatchDTO worderImportBatchDTO, WorderImportBatchVo worderImportBatchVo) {
        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address = worderImportBatchDTO.getG() + "_" + worderImportBatchDTO.getI() + "_" + worderImportBatchDTO.getK() + "_" + worderImportBatchDTO.getL();

        worderInfoEntity.setUserName(worderImportBatchDTO.getD());
        worderInfoEntity.setUserPhone(worderImportBatchDTO.getE());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(worderImportBatchDTO.getN());
        worderInfoEntity.setTemplateId(worderImportBatchVo.getTemplateId());

        worderInfoEntity.setCarBrand("22");
        worderInfoEntity.setCarModel("4");
        worderInfoEntity.setCompanyId(516);
        // 360校验厂商id
        setCheckResult360(516, 1);
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setWorderTypeId(6);

        worderInfoEntity.setCandidate("系统自动");
        worderInfoEntity.setCreator(89L);
        worderInfoEntity.setCreateBy(worderImportBatchVo.getUserId());

        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

        worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", 6));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", 516));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", worderImportBatchDTO.getD()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderImportBatchDTO.getE()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1049, "车系-比亚迪", worderImportBatchDTO.getO()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderImportBatchDTO.getN()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1052, "比亚迪工单来源", worderImportBatchDTO.getC()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1053, "比亚迪充电桩功率", worderImportBatchDTO.getP()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(1057, "安装完成时间", worderImportBatchDTO.getQ()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1058, "待维修原充电桩编号", worderImportBatchDTO.getR()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1142, "线路是否在保", worderImportBatchDTO.getS()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1143, "充电桩是否在保", worderImportBatchDTO.getT()));

        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
        return worderInfoEntity;
    }


}
