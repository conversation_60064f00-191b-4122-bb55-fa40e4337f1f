<template>
  <div class="pa-box">
    <el-row v-if="!showTaskInfo && scoreStatus">
      <div class="pa-title">
        <h2>红绿灯图</h2>
      </div>
      <div style="float:right;margin:-30px 30px 0 0 ">
        <el-button type="primary" size="mini" style="margin-right: 10px;" @click="saveImg">保存图片</el-button>
      </div>
      <el-table
        v-loading="loadingData"
        class="pa20"
        id="statMap"
        :data="chartData"
        size="small"
        row-key="id"
        border
        :header-cell-style="{background:'#eef1f8',fontWeight:'bold'}"
        style="width: 100%;text-align: center;">
        <el-table-column
          prop="deptName"
          header-align="center"
          label="专业公司">
        </el-table-column>
        <el-table-column
          prop="cp"
          header-align="center"
          label="产品开发">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.cp >= 0"
              size="mini"
              :type="scope.row.cp | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="yx"
          header-align="center"
          label="营销活动">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.yx >= 0"
              size="mini"
              :type="scope.row.yx | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="xs"
          header-align="center"
          label="销售活动">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.xs >= 0"
              size="mini"
              :type="scope.row.xs | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="fw"
          header-align="center"
          label="服务">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.fw >= 0"
              size="mini"
              :type="scope.row.fw | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="ly"
          header-align="center"
          label="合同履约">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.ly >= 0"
              size="mini"
              :type="scope.row.ly | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="ty"
          header-align="center"
          label="客户体验">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.ty >= 0"
              size="mini"
              :type="scope.row.ty  | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column
          prop="gys"
          header-align="gys"
          label="供应商管理">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.gys >= 0"
              size="mini"
              :type="scope.row.gys | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column
          prop="yd"
          header-align="center"
          label="移动平台运营">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.yd >= 0"
              size="mini"
              :type="scope.row.yd | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column
          prop="xj"
          header-align="center"
          label="整条业务线下架管理">
          <template slot-scope="scope">
            <el-button
              circle
              v-if="scope.row.xj >= 0"
              size="mini"
              :type="scope.row.xj | colorFilter(total)"
              disable-transitions>
            </el-button>
            <div v-else>-</div>
          </template>
        </el-table-column>
      </el-table>
    </el-row>


    <el-row v-if="showTaskInfo && taskItem">
      <div class="pa-title">
        <h2>任务信息</h2>
      </div>
      <task-info :task="taskItem"></task-info>
    </el-row>

    <el-row>
      <div v-if="tids.length === 1">
        <div class="pa-title pa20">
          <h2>打分结果</h2>
        </div>
        <div style="float:right;margin:-30px 30px 0 0 " v-if="!showTaskInfo">
          <!--<el-button type="primary" size="mini" style="margin-right: 10px;" @click="exportExcel">导出excel</el-button>-->
          <el-button type="primary" size="mini" style="margin-right: 10px;" @click="exportStatDetail">导出excel
          </el-button>
        </div>
        <el-table
          id="scoreResult"
          border
          :data="dataList"
          class="pa20"
          :header-cell-style="{color:'#333',fontWeight:'bold'}">
          <el-table-column
            type="index"
            label="序号"
            width="70">
          </el-table-column>
          <el-table-column
            :label="item.colName"
            :prop="'data.'+item.colEnName"
            v-for="item in headerList"
            :key="item.id"
            :formatter="(item.inputType === 7 || item.inputType === 6) ? dataFormat : undefined">
          </el-table-column>
          <el-table-column
            prop="score"
            header-align="center"
            label="分数">
          </el-table-column>
          <el-table-column
            prop="remark"
            header-align="center"
            label="打分意见">
          </el-table-column>
        </el-table>
      </div>
    </el-row>

  </div>
</template>
<script>
  import FileSaver from 'file-saver'
  import XLSX from 'xlsx'
  import html2canvas from 'html2canvas'
  import Sortable from 'sortablejs'
  import taskInfo from '../task-list-task-info';

  export default {
    components: {
      taskInfo
    },
    data() {
      return {
        chartData: [],
        headerList: [],
        dataList: [],
        taskItem: null,
        task: null,
        scoreStatus: 0,
        loadingData: false,
        showTaskInfo: false,
        tids: [],
        tid: 0,
        sid: 0,
        total: 9.0,
        tableDataLoading: false,
        dialogFormVisible: false,
        deptIds: [],
        baseDeptList: []
      }
    },
    created() {
      this.$destroy();
      console.log('recv tids = ', this.$route.params.tids);
      this.showTaskInfo = this.$route.params.showTaskInfo;
      this.scoreStatus = this.$route.params.scoreStatus;
      console.log('this.$route.params = ', this.$route.params);
      let tids = this.$route.params.tids;
      if (tids && tids.length > 0) {
        this.scoreStatus = 1;
      }
      if (this.$route.params.task) {
        this.taskItem = this.$route.params.task;
        console.log(this.taskItem);
      } else {
        this.taskItem = {};
      }
      if (!tids || tids.length === 0) {
        tids = this.$getParams();
        console.log('tids: ', tids.tids);
        if (tids) {
          tids = tids.tids;
        }
      }
      if (tids && tids.length !== 0) {
        this.tids = tids;
        this.getQueryList();
      } else {
        console.error('模板id丢失');
      }
    },
    activated() {
    },
    mounted() {
      // this.columnDrop();
    },
    filters: {
      colorFilter(rank, total) {
        let rate = rank / total;
        console.log(rank, '/', total, rate);
        let class_ = 'success';
        if (rate <= 0.2) {
          class_ = 'danger'
        } else if (rate <= 0.4) {
          class_ = 'warning'
        }
        console.log('clss', class_);
        return class_;
      }
    },
    methods: {
      //获取数据  一条数据9个字段
      getQueryList() {
        this.loadingData = true;
        this.$http({
          url: this.$http.adornUrl('/operational/questionnaire/answer/stat2'),
          method: 'get',
          params: this.$http.adornParams({tids: this.tids})
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.data && data.data.length !== 0) {
              let total_ = data.data.length * 9;
              data.data.forEach((item, idx) => {
                item.id = idx + 1;
                Object.keys(item).forEach((key) => {
                  if (key !== 'deptName' &&
                    key !== 'sid' &&
                    key !== 'tid' &&
                    typeof item[key] === 'number' &&
                    item[key] === -1) {
                    total_ -= 1
                  }
                })
              });
              console.log('total_', total_);
              if (total_ === 0) {
                this.total = 1.0
              } else {
                this.total = total_;
              }
            }
            if (this.tids && this.tids.length === 1) {
              this.tid = this.tids[0];
              this.getHeaderList(data.sid);
              this.getDataList(data.sid);
              this.sid = data.sid;
            }
            this.chartData = data.data;
            if (this.chartData && this.chartData.length > 1) {
              this.$nextTick(() => {
                this.rowDrop();
              })
            }
            console.log('data.chartData', data.data)
          } else {
            this.chartData = []
          }
          this.loadingData = false;
        })
      },
      getHeaderList(sid) {
        this.tableDataLoading = true;
        this.$http({
          url: this.$http.adornUrl('/operational/questionnaire/select'),
          method: 'get',
          params: this.$http.adornParams({basic: 1, sid: sid})
        }).then(({data}) => {
          if (data.code === 0) {
            this.headerList = data.data;
            this.tableDataLoading = false
          } else {
            this.$message.error(data.msg)
          }
        })
      },
      dataFormat(row, column, cellValue, index) {
        if (cellValue) {
          for (let comp of this.baseDeptList) {
            if (cellValue === comp.deptId) {
              return comp.deptName
            }
          }
        }
        return cellValue;
      },
      getDataList(sid) {
        console.log(' this.taskItem = ', this.taskItem)
        this.tableDataLoading = true;
        this.$http({
          url: this.$http.adornUrl('/operational/questionnaire/answer/statDetail'),
          method: 'get',
          params: this.$http.adornParams({
            tid: this.tid,
            sid: sid,
            receiveUserId: this.taskItem.receiveUserId,
            sendUserId: this.taskItem.sendUserId,
            dkUserId: this.taskItem.dkUserId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.data;

            for (let d of this.dataList) {
              let dd = d.data;
              this.headerList.forEach(item => {
                if (item.inputType === 6 || item.inputType === 7) {
                  this.deptIds.push(dd[item.colEnName])
                }
              })
            }
            this.getBaseDeptList();
          } else {
            this.dataList = []
          }
          this.tableDataLoading = false
        })
      },
      getBaseDeptList() {
        this.$http({
          url: this.$http.adornUrl('/sys/dept/getDeptList'),
          method: 'post',
          data: this.$http.adornParams({deptIds: this.deptIds})
        }).then(({data}) => {
          this.baseDeptList = data.list;
          console.log(this.baseDeptList, '------');
        })
      },
      exportExcel() {
        let wb = XLSX.utils.table_to_book(document.querySelector('#scoreResult'));
        let wbout = XLSX.write(wb, {bookType: 'xlsx', bookSST: true, type: 'array'});
        try {
          FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), '打分结果.xlsx')
          // let file = new File(new Blob([wbout], '打分结果.xlsx', {type: 'application/octet-stream'}));
          // FileSaver.saveAs(file)
        } catch (e) {
          console.log(e, wbout)
        }
      },
      exportStatDetail() {
        let url = this.$http.adornUrl(`/operational/questionnaire/answer/exportStatDetail?tid=${this.tid}&sid=${this.sid}`);
        this.$downloadFile(url, 'get', '', null, null);
      },
      saveImg() {
        html2canvas(document.getElementById('statMap')).then(function (canvas) {
          // document.body.appendChild(canvas);
          let a = document.createElement('a');
          a.href = canvas.toDataURL();
          a.download = '红绿灯图';
          a.click();
        });
      },
      formatter(row, column) {
        return row.address
      },
      getClass(rank) {
        let rate = rank / this.total;
        console.log(rank, '/', this.total, rate);
        let class_ = 'success';
        if (rate <= 0.2) {
          class_ = 'danger'
        } else if (rate <= 0.4) {
          class_ = 'warning'
        }
        console.log('clss', class_);
        return class_;
      },
      filterTag(value, row) {
        return row.tag === value
      },
      //行拖拽
      rowDrop() {
        const tbody = document.querySelector('.el-table__body-wrapper tbody')
        const _this = this
        Sortable.create(tbody, {
          onEnd({newIndex, oldIndex}) {
            const currRow = _this.chartData.splice(oldIndex, 1)[0]
            _this.chartData.splice(newIndex, 0, currRow)
          }
        })
      },
      queryTaskInfo(taskId) {
        // 查询任务信息 evaluatetask
        if (taskId) {
          alert(taskId);
          this.$http({
            url: this.$http.adornUrl('/operational/evaluatetask/getTaskAndTempInfo'),
            method: 'get',
            params: this.$http.adornParams({'taskId': taskId})
          }).then(({data}) => {
            console.log('data = ', data);
            this.task = data && data.code === 0 ? data.task : {};
          });
        }
      }
    }
  }
</script>
<style scoped>
  .last-title {
    padding: 10px 8px;
  }

  .el-button--danger {
    background-color: red !important;
    border-color: red !important;
  }

  .el-button--warning {
    background-color: #efeb2a !important;
    border-color: #efeb2a !important;
  }

  .el-button--success {
    background-color: green !important;
    border-color: green !important;
  }

</style>
