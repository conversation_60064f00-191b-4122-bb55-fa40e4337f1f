package com.youngking.renrenwithactiviti.modules.sys.oauth2;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Method;
import java.util.*;

@Component
@Slf4j
public class PermCollection {
	@Getter
	private Set<String> perms = new HashSet<>();

	private final ApplicationContext applicationContext;

	public PermCollection(ApplicationContext applicationContext) {
		this.applicationContext = applicationContext;
		new Thread(() ->
				this.listPermission(this.applicationContext.getBeansWithAnnotation(Controller.class), new String[]{
						"com.bonc.rrs", "com.bonc.rrs.*"
						, "com.youngking.*", "com.youngking","com.common"
				})
		).start();
	}

	private void listPermission(Map<String, Object> map, String[] basicPackages) {
		for (String basicPackage : basicPackages) {
			for (Map.Entry<String, Object> entry : map.entrySet()) {
				Object bean = entry.getValue();

				if (!StringUtils.contains(ClassUtils.getPackageName(bean.getClass()), basicPackage)) {
					continue;
				}
				Class<?> clz = bean.getClass();
				Class controllerClz = clz.getSuperclass();
				List<Method> methods = MethodUtils.getMethodsListWithAnnotation(controllerClz, RequiresPermissions.class);
				for (Method method : methods) {
					RequiresPermissions requiresPermissions = AnnotationUtils.getAnnotation(method, RequiresPermissions.class);

					if (requiresPermissions == null) {
						continue;
					}

					perms.addAll(Arrays.asList(requiresPermissions.value()));

				}
			}
		}
	}
}
