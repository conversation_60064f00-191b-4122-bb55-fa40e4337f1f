package com.bonc.rrs.warning.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.message.entity.ReceiveUser;
import com.bonc.rrs.warning.entity.WorderWarningEntity;
import com.bonc.rrs.warning.entity.po.ReceiveUserPo;
import com.bonc.rrs.warning.entity.po.UserNamePo;
import com.bonc.rrs.warning.entity.po.WorderTriggerPo;

import java.util.List;

/**
 * Created by zhangyibo on 2020-03-20 17:31
 */

public interface WorderWarningService extends IService<WorderWarningEntity> {

    /**
     * 查询预警设置
     * @param detailName 事件类型
     * @return
     */
    List<WorderTriggerPo> listTriggerByDetailName(String worderNo, String detailName);

    /**
     * 获取发送时间
     * @param worderNo
     * @param detailNumber
     * @return
     */
    String getSendTime(String worderNo, String detailNumber);

    /**
     * 获取预警验证
     * @param worderNo
     * @param triggerValidate
     * @return
     */
    boolean getTriggerValidate(String worderNo, String triggerValidate);


    /**
     * 根据ID获取消息接收者名称
     * @param userId
     * @return
     */
    List<UserNamePo> getUserNameById(String userId);
    /**
     * 工单预警策略列表
     * @return
     */
    List<WorderTriggerPo> listWorderTrigger();

    List<WorderTriggerPo> listWorderTriggerByParams(WorderWarningEntity worderTrigger);

    /**
     * 根据ID查询工单预警策略
     * @param eventType
     * @return
     */
    List<WorderTriggerPo> listWorderTriggerById(String eventType);

    /**
     * 根据工单编号和受理人角色查询客服，服务经理，网点，网点管理员，服务兵
     * @param worderNo
     * @return
     */
    ReceiveUserPo getUserName(String worderNo, String role);

    /**
     * 根据工单编号查询客服，服务经理，网点，网点管理员，服务兵
     * @param worderNo
     * @return
     */
    ReceiveUserPo getUserName(String worderNo);

    void sendMessage(String eventType, String worderNo, String currentTime);

    void test1();

    void test2();

    /**
     * 获取辐射工单的全流程客服、外包客服，项目助理，维修工程师账号
     * @param worderNo
     * @return
     */
    List<ReceiveUser> queryReceiveUserListByWorderNo(String worderNo);
}
