package com.bonc.rrs.balancerule.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.rrs.suite.entity.SuiteEntity;
import com.bonc.rrs.worder.aspect.DicMarker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by liqingchao on 2020/1/17.
 */
@Data
@TableName("balance_rule")
@ApiModel(value = "结算规则表")
public class BalanceRuleEntity implements Serializable {
    @TableId
    @ApiModelProperty(value = "自增主键")
    @NotNull(message = "ID不能为空", groups = SuiteEntity.Update.class)
    private Integer id;

    @ApiModelProperty(value = "规则名称")
    @NotBlank(message = "规则名称不能为空", groups = {SuiteEntity.Insert.class, SuiteEntity.Update.class})
    private String name;

    @ApiModelProperty(value = "结算类型（数据字典balance_type）")
    @NotNull(message = "结算类型不能为空", groups = {SuiteEntity.Insert.class, SuiteEntity.Update.class})
    @DicMarker(key = "balance_type")
    private Integer balanceType;

    @ApiModelProperty(value = "结算对象(数据字典balance_target)")
    @NotNull(message = "结算对象不能为空", groups = {SuiteEntity.Insert.class, SuiteEntity.Update.class})
    @DicMarker(key = "balance_target")
    private Integer balanceTarget;

    @ApiModelProperty(value = "税点(数据字典balance_tax_rate)")
    @NotNull(message = "税点不能为空", groups = {SuiteEntity.Insert.class, SuiteEntity.Update.class})
    @DicMarker(key = "balance_tax_rate")
    private Integer balanceTaxRate;

    @ApiModelProperty(value = "结算对象冗余")
    private String balanceTargetValue;

    @ApiModelProperty(value = "税点冗余")
    private String balanceTaxRateValue;

    @ApiModelProperty(value = "结算类型冗余")
    private String balanceTypeValue;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "结算项列表")
    //@NotEmpty(message = "结算项列表不能为空", groups = {SuiteEntity.Insert.class, SuiteEntity.Update.class})
    private List<BalanceRuleDetailEntity> balanceItemList;

    @ApiModelProperty(value = "价格类型：0：含税价，1：不含税价")
    private Integer priceType;

    private String priceTypeDesc;
    /**
     * 是否删除 0：未删除 1：已删除
     */
    private Integer isDelete;
}
