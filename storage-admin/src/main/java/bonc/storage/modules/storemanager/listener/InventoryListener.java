package bonc.storage.modules.storemanager.listener;

import bonc.storage.modules.storemanager.dao.RRSStoreStockGoodsDao;
import bonc.storage.modules.storemanager.entity.RRSStoreStockGoodsEntity;
import bonc.storage.modules.storemanager.entity.TableHeaderExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.read.metadata.holder.ReadWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youngking.lenmoncore.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @Author: liujunpeng
 * @Date: 2021/6/17 16:17
 * @Version: 1.0
 */
@AllArgsConstructor
@Log4j2
public class InventoryListener extends AnalysisEventListener<TableHeaderExcelProperty> {

    private final RRSStoreStockGoodsDao rrsStoreStockGoodsDao;

    private final List<RRSStoreStockGoodsEntity> stockGoodsUpdateList;

    private final List<RRSStoreStockGoodsEntity> stockGoodsInsertList;

    private final List<String> failureMessage;

    private final List<String> storeName;

    @Override
    public void invoke(TableHeaderExcelProperty data, AnalysisContext context) {
        ReadRowHolder readRowHolder = context.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex();
        //查询记录
        QueryWrapper<RRSStoreStockGoodsEntity> queryWrapper = new QueryWrapper<>();
        //校验仓库
        if (StringUtils.isNotBlank(data.getStoreName())) {
            queryWrapper.exists("select 1 from rrs_store_basic_info where store_name = '" + data.getStoreName().trim() + "' and id = rrs_store_stock_goods.store_id");
            storeName.add(data.getStoreName());
        } else {
            failureMessage.add("表格第" + (rowIndex + 1) + "行 缺少仓库！");
            return;
        }
        //校验仓位
        if (StringUtils.isNotBlank(data.getPositionName())) {
            queryWrapper.exists("select 1 from rrs_store_position_info where position_name = '" + data.getPositionName().trim() + "' and id = rrs_store_stock_goods.store_position_id");
        } else {
            failureMessage.add("表格第" + (rowIndex + 1) + "行 缺少仓位！");
            return;
        }
        //校验物料
        if (StringUtils.isNotBlank(data.getGoodsName()) && StringUtils.isNotBlank(data.getGoodsCode())) {
            queryWrapper.exists("select * from materiel_information where materiel_name = '" + data.getGoodsName() + "' and materiel_no = '" + data.getGoodsCode() + "' and id = rrs_store_stock_goods.goods_id");
        } else {
            failureMessage.add("表格第" + (rowIndex + 1) + "行 缺少物料信息！");
            return;
        }
        //校验区分
        if (StringUtils.isNotBlank(data.getGoodsType()) && data.getGoodsType().trim().contains("不良品")) {
            queryWrapper.eq("goods_type", 2);
        } else if (StringUtils.isNotBlank(data.getGoodsType()) && data.getGoodsType().trim().contains("良品")) {
            queryWrapper.eq("goods_type", 1);
        } else if (StringUtils.isNotBlank(data.getGoodsType()) && data.getGoodsType().trim().contains("全新")) {
            queryWrapper.eq("goods_type", 4);
        } else {
            failureMessage.add("表格第" + (rowIndex + 1) + "行 缺少区分或者值不正确！");
            return;
        }
        //查询
        List<RRSStoreStockGoodsEntity> rrsStoreStockGoods = rrsStoreStockGoodsDao.selectList(queryWrapper);
        if (Objects.nonNull(rrsStoreStockGoods) && rrsStoreStockGoods.size() > 0) {
            RRSStoreStockGoodsEntity rrsStoreStockGoodsEntity = rrsStoreStockGoods.get(0);
            if (Objects.nonNull(data.getRealGoodsTotal())) {
                rrsStoreStockGoodsEntity.setRealGoodsTotal(data.getRealGoodsTotal());
            }
            stockGoodsUpdateList.add(rrsStoreStockGoodsEntity);
        } else {
            RRSStoreStockGoodsEntity rrsStoreStockGoodsEntity = new RRSStoreStockGoodsEntity();
            //查询仓库id
            Integer storeId = rrsStoreStockGoodsDao.getStoreId(data.getStoreName());
            if (Objects.isNull(storeId)) {
                failureMessage.add("表格第" + (rowIndex + 1) + "行 根据仓库名称：" + data.getStoreName() + "未查询到仓库信息仓库！");
                return;
            }
            rrsStoreStockGoodsEntity.setStoreId(storeId);
            //根据仓库和仓位名称查询仓位信息
            Integer positionId = rrsStoreStockGoodsDao.getPositionId(data.getPositionName(), storeId);
            if (Objects.isNull(positionId)) {
                failureMessage.add("表格第" + (rowIndex + 1) + "行 根据仓位名称：" + data.getPositionName() + "未查询到仓位信息！");
                return;
            }
            rrsStoreStockGoodsEntity.setStorePositionId(positionId);
            //根据编码查询物料信息
            Integer materielId = rrsStoreStockGoodsDao.getMaterielId(data.getGoodsCode());
            if (Objects.isNull(materielId)) {
                failureMessage.add("表格第" + (rowIndex + 1) + "行 根据物料编码：" + data.getGoodsCode() + " 未查询到物料信息！");
                return;
            }
            if (StringUtils.isNotBlank(data.getGoodsType()) && data.getGoodsType().trim().contains("不良品")) {
                rrsStoreStockGoodsEntity.setGoodsType(2);
            } else if (StringUtils.isNotBlank(data.getGoodsType()) && data.getGoodsType().trim().contains("良品")) {
                rrsStoreStockGoodsEntity.setGoodsType(1);
            } else if (StringUtils.isNotBlank(data.getGoodsType()) && data.getGoodsType().trim().contains("全新")) {
                rrsStoreStockGoodsEntity.setGoodsType(4);
            }
            rrsStoreStockGoodsEntity.setGoodsId(materielId);
            rrsStoreStockGoodsEntity.setGoodsTotal(0);
            rrsStoreStockGoodsEntity.setRealGoodsTotal(data.getRealGoodsTotal());
            rrsStoreStockGoodsEntity.setLatestInventoryTime(new Date());
            rrsStoreStockGoodsEntity.setLatestInTime(new Date());
            stockGoodsInsertList.add(rrsStoreStockGoodsEntity);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ReadWorkbookHolder readWorkbookHolder = context.readWorkbookHolder();
        InputStream inputStream = readWorkbookHolder.getInputStream();
        if (Objects.nonNull(inputStream)) {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
