package com.bonc.rrs.invoice.enterprises.finance.business.callback.settlementAuditNotify;

import com.alibaba.fastjson.annotation.JSONField;
import com.bonc.rrs.invoice.enterprises.finance.business.callback.BaseRequestMsg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description 合并单号审核结果通知
 * @Date 2023/2/8 14:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class SettlementAuditNotify extends BaseRequestMsg {

    /**
     * 结算申请单号
     */
    @JSONField(name = "bill_no")
    private String billNo;
    /**
     * 1.通过  2.不通过
     */
    @JSONField(name = "check_code")
    private String checkCode;
    /**
     * 不通过原因
     */
    @JSONField(name = "check_reason")
    private String checkReason;
    /**
     * 审核人
     */
    @JSONField(name = "check_user")
    private String checkUser;
    /**
     * 审核时间（yyyy-mm-dd HH:mi:ss）
     */
    @JSONField(name = "check_time")
    private String checkTime;

}
