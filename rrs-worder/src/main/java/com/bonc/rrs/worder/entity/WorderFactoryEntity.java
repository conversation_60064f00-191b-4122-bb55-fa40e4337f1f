package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 厂商订单信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:57
 */
@Data
@TableName("worder_factory")
@ApiModel(value = "厂商订单信息表")
public class WorderFactoryEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId
    @ApiModelProperty(value = "主键，自增", required = false)
    private Integer id;
    /**
     * 厂商订单号
     */
    @ApiModelProperty(value = "厂商订单号", required = false)
    private String factoryOrderNo;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", required = false)
    private String worderNo;
    /**
     * 对应的厂商编号
     */
    @ApiModelProperty(value = "对应的厂商编号", required = false)
    private String companyNo;
    /**
     * 订单的4S店名称
     */
    @ApiModelProperty(value = "订单的4S店名称", required = false)
    private String sName;
    /**
     * 4S店联系方式
     */
    @ApiModelProperty(value = "4S店联系方式", required = false)
    private String sPhone;
    /**
     * 厂商订单派单时间
     */
    @ApiModelProperty(value = "厂商订单派单时间", required = false)
    private Date factoryDate;
    /**
     * 订单对应的厂商大区
     */
    @ApiModelProperty(value = "订单对应的厂商大区", required = false)
    private String factoryArea;
    /**
     * 订单对应的日日顺工贸
     */
    @ApiModelProperty(value = "订单对应的日日顺工贸", required = false)
    private String rrsGm;
    /**
     * 充电桩CD号
     */
    @ApiModelProperty(value = "充电桩CD号", required = false)
    private String pileCd;
    /**
     * 充电桩规格
     */
    @ApiModelProperty(value = "充电桩规格", required = false)
    private String pileSpecs;
    /**
     * 充电桩型号
     */
    @ApiModelProperty(value = "充电桩型号", required = false)
    private String pileModel;
    /**
     * 充电桩编号
     */
    @ApiModelProperty(value = "充电桩编号", required = false)
    private String pileNo;
    /**
     * 充电桩是否寄出
     */
    @ApiModelProperty(value = "充电桩是否寄出", required = false)
    private String isSend;
    /**
     * 安装数量
     */
    @ApiModelProperty(value = "安装数量", required = false)
    private Integer pileNumber;
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号", required = false)
    private String vin;
    /**
     * 车型
     */
    @ApiModelProperty(value = "车型", required = false)
    private String carModel;
    /**
     * 购车日期
     */
    @ApiModelProperty(value = "购车日期", required = false)
    private Date buyDate;
    /**
     * 汽车品牌
     */
    @ApiModelProperty(value = "汽车品牌", required = false)
    private String brand;

}
