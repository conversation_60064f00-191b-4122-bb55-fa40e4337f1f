package bonc.storage.modules.storemanager.controller;

import bonc.storage.core.utils.PageUtils;
import bonc.storage.core.utils.Query;
import bonc.storage.core.utils.Result;
import bonc.storage.modules.storemanager.entity.RRSAlertManagerEntity;
import bonc.storage.modules.storemanager.service.IRRSAlertManagerService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 入库
 */
@RestController
@RequestMapping("/alertManager")
public class RRSAlertManagerController {

    @Autowired
    private IRRSAlertManagerService irrsAlertManagerService;

    /**
     *  预警管理列表
     */
    @PostMapping("/alertManagerList")
    @ResponseBody
    public Result RRSAlertManagerList(@RequestBody Map<String, Object> params){
        PageUtils page = irrsAlertManagerService.alertManagerList(new Query(params));
        return Result.ok().put("page", page);
    }

    /**
     *  物料预警列表
     */
    @PostMapping("/goodsAlertList")
    @ResponseBody
    public Result goodsAlertList(@RequestBody Map<String, Object> params){
        PageUtils page = irrsAlertManagerService.goodsAlertList(new Query(params));
        return Result.ok().put("page", page);
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @ResponseBody
    public Result updateMode(@RequestBody JSONObject param){
        return irrsAlertManagerService.updateGoodsAlertDown(param);
    }
    /**
     * 删除
     */
    @RequestMapping("/delete")
    @ResponseBody
    public Result delete(@RequestBody RRSAlertManagerEntity managerEntity){
        return irrsAlertManagerService.deleteAlertManager(managerEntity);
    }

    /**
     * 修改
     */
    @RequestMapping("/batchOperation")
    @ResponseBody
    public Result batchOperation(@RequestBody JSONObject param){
        return irrsAlertManagerService.batchOperation(param);
    }

    /**
     * 新增/修改预警
     * @param param
     * @return
     */
    @PostMapping("/insertMateriel")
    @ResponseBody
    public Result insertMaterielByStore(@RequestBody JSONObject param){
        return irrsAlertManagerService.insertMateriel(param);
    }
}