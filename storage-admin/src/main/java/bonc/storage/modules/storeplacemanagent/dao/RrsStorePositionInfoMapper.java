package bonc.storage.modules.storeplacemanagent.dao;

import bonc.storage.modules.storemanager.entity.RRSStoreStockGoodsEntity;
import bonc.storage.modules.storeplacemanagent.entity.RrsStorePositionInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓位信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2020-01-08
 */
@Mapper
public interface RrsStorePositionInfoMapper extends BaseMapper<RrsStorePositionInfo> {
    /**
     * 查询数据
     * @param
     * @return list
     */

    List<RrsStorePositionInfo> selectRrsStorePositionInfoList(Map<String,Object> map);
    /**
     * 新增数据
     * @param  rrsStorePositionInfo
     * @return int
     */
    int insertRrsStorePositionInfo(RrsStorePositionInfo rrsStorePositionInfo);
    /**
     * 查询单条数据
     * @param  map
     * @return RrsStorePositionInfo
     */
    RrsStorePositionInfo selectRrsStorePositionInfoOne(Map<String,Object> map);
    /**
     * 更新数据
     * @param  rrsStorePositionInfo
     * @return int
     */
    int updateRrsStorePositionInfo(RrsStorePositionInfo rrsStorePositionInfo);
    /**
     * 删除数据
     * @param  id
     * @return int
     */
    int deleteRrsStorePositionInfoById(String id);
    /**
     * 批量删除数据
     * @param  hashMap
     * @return int
     */
    int deleteRrsStorePositionInfoByIds(HashMap<String,Object> hashMap);


    List<Map<String, Object>> positionList(@Param("storeId") String storeId);

    List<RRSStoreStockGoodsEntity> getPositionStock(String id);

    List<String> getPositionName(@Param("storeId") Integer storeId);

    List<Integer> queryDotAddedPositionByStoreId(@Param("storeId") Integer storeId, @Param("positionId") Integer positionId);

    Integer queryDotCountByStoreId(@Param("storeId") Integer storeId);

    List<Map<String, Object>> brandList(@Param("storeId") String storeId,@Param("positionId") String positionId);

    /**
     * 校验仓位是否存在预录入库单
     * @param positionId
     * @return
     */
    List<String> checkEnterOrderByPositionId(@Param("positionId") String positionId);

    /**
     * 校验仓位是否存在预录出库库单
     * @param positionId
     * @return
     */
    List<String> checkLeaveOrderByPositionId(@Param("positionId") String positionId);

    /**
     * 校验仓位是否存在调拨入库单
     * @param positionId
     * @return
     */
    List<String> checkShopApplyEnterOrderByPositionId(@Param("positionId") String positionId);

    /**
     * 校验仓位是否存在调拨单
     * @param positionId
     * @return
     */
    List<String> checkShopApplyByPositionId(@Param("positionId") String positionId);

    /**
     * 校验仓位是否还有库存
     * @param positionId
     * @return
     */
    Integer checkStockGoodsByPositionId(@Param("positionId") String positionId);
}