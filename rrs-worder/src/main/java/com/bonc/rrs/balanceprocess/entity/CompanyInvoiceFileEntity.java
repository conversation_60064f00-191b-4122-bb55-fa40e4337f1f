package com.bonc.rrs.balanceprocess.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 车企开票文件表
 * 
 * <AUTHOR>
 * @date 2020-07-03 17:47:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("company_invoice_file")
public class CompanyInvoiceFileEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
		private Integer id;
	/**
	 * 开票ID
	 */
	private Integer invoiceId;
	/**
	 * 文件ID
	 */
	private Integer fileId;


}
