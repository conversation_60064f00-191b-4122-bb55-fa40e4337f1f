package com.bonc.rrs.spider.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.bonc.rrs.spider.dto.CaRepairOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class CaRepairOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Collections.singletonList(564));

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void preProcess(OrderContext context) {

        context.getWorderInfoEntity().setCreateBy(2252L);

        CaRepairOrderDto apiDto = BeanUtil.mapToBean(context.getRequestDto().getExtFields(), CaRepairOrderDto.class, true);
        List<WorderExtFieldEntity> extFieldList = context.getWorderInfoEntity().getWorderExtFieldList();
        extFieldList.add(WorderExtFieldEntity.create(1143, "充电桩是否在保", apiDto.getIsPileSafety()));
        extFieldList.add(WorderExtFieldEntity.create(1629, "长安-品牌", apiDto.getChanganBrand()));
        extFieldList.add(WorderExtFieldEntity.create(1275, "故障现象", apiDto.getFaultDesc()));
    }

}
