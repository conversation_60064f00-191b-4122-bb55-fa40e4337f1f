package com.bonc.rrs.gace.enums;

/**
 * <AUTHOR>
 */
public enum GaceOrderTypeEnum {
    /**
     * 工单类型
     * RM:故障/缺陷响应, PM:维护保养, INS:点巡检, AM:自主维护, OH:大修/改造, IT:安装调试, OT:其它事项, 01:换货类工单
     */
    RM("RM", "故障/缺陷响应"),
    IT("IT", "安装调试"),
    PM("PM", "维护保养"),
    INS("INS", "点巡检"),
    AM("AM", "自主维护"),
    OH("OH", "大修/改造"),
    OT("OT", "其它事项"),
    OTHER("01", "换货类工单");


    private final String code;
    private final String description;

    GaceOrderTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据状态码获取对应枚举
    public static GaceOrderTypeEnum fromCode(String code) {
        for (GaceOrderTypeEnum status : GaceOrderTypeEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid work order status code: " + code);
    }

    @Override
    public String toString() {
        return this.code + " (" + this.description + ")";
    }
}