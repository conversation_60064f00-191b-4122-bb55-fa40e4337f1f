package com.bonc.rrs.spider.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.bonc.rrs.spider.dto.XPRepairOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 小鹏车企接口
 * <AUTHOR>
 */
@Component
public class XPRepairOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Arrays.asList(219,241));

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void preProcess(OrderContext context) {
        super.preProcess(context);
        context.getWorderInfoEntity().setCreateBy(5617L);
        XPRepairOrderDto xpOrderDto = BeanUtil.mapToBean(context.getRequestDto().getExtFields(), XPRepairOrderDto.class, true);
        List<WorderExtFieldEntity> worderExtFieldEntityList = context.getWorderInfoEntity().getWorderExtFieldList();
        Integer templateId = context.getRequestDto().getTemplateId();

        //维修
        if (templateId == 219) {
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(988, "车企大区", xpOrderDto.getCompanyBigArea()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1057, "安装完成时间", xpOrderDto.getInstallFinishTime()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1058, "待维修原充电桩编号", xpOrderDto.getPrivatePileNo()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1143, "充电桩是否在保", xpOrderDto.getIsPileWarranty()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1142, "线路是否在保", xpOrderDto.getIsLineWarranty()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1131, "保修范围确认", xpOrderDto.getIsWarrantyRange()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1129, "客户反应故障描述", xpOrderDto.getFaultDesc()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1138, "小鹏充电桩故障代码", xpOrderDto.getFaultCode()));

        //移桩
        }else {
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(911, "联系信息备注", xpOrderDto.getContactRemark()));
            worderExtFieldEntityList.add(WorderExtFieldEntity.create(1206, "是否官方移桩服务", xpOrderDto.getIsOfficialPileMove()));
        }
    }
}
