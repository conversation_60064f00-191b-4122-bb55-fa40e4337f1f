/**
 * Copyright (C), 2024,
 */
package bonc.storage.modules.storemanager.controller;

import bonc.storage.core.utils.Result;
import bonc.storage.modules.storemanager.dao.DotInformationDao;
import bonc.storage.modules.storemanager.dao.MaterialInformationDao;
import bonc.storage.modules.storemanager.dao.SparePartApplyDao;
import bonc.storage.modules.storemanager.dto.SparePartApplyParamDTO;
import bonc.storage.modules.storemanager.entity.DotInformationEntity;
import bonc.storage.modules.storemanager.entity.MaterielInformationEntity;
import bonc.storage.modules.storemanager.entity.SparePartApplyEntity;
import bonc.storage.modules.storemanager.listener.SparePartApplyAuditListener;
import bonc.storage.modules.storemanager.service.ISparePartApplyService;
import bonc.storage.modules.storemanager.util.CustomSheetWriteHandler;
import bonc.storage.modules.storemanager.vo.SparePartApplyAuditExcelVO;
import bonc.storage.modules.storemanager.vo.SparePartApplyExcelVO;
import bonc.storage.modules.storemanager.vo.SparePartApplyStatus;
import com.alibaba.excel.EasyExcelFactory;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.dao.SysUserDao;
import com.youngking.renrenwithactiviti.modules.sys.dto.WorderCountDTO;
import com.youngking.renrenwithactiviti.modules.sys.entity.MagrAreaBrand;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/13 10:13
 * @Version 1.0.0
 */

@Slf4j
@RestController
@RequestMapping("/sparePartApply")
public class SparePartApplyController {

    @Resource
    private ISparePartApplyService sparePartApplyService;

    @Resource
    private SysUserDao sysUserDao;

    @Resource
    private MaterialInformationDao materialInformationDao;

    @Resource
    private DotInformationDao dotInformationDao;

    @Resource
    private SparePartApplyDao sparePartApplyDao;

    @GetMapping("/getDotInfo")
    @ApiOperation(value = "获取当前用户的网点信息", notes = "获取当前用户的网点信息")
    public Result getDotInfo() {
        List<Map<String, String>> dotInfo = sparePartApplyService.getDotInfos();
        return Result.ok().put("dotInfo", dotInfo);
    }

    @GetMapping("/getStoreInfo")
    @ApiOperation(value = "获取仓库信息", notes = "获取仓库信息")
    public Result getStoreInfo(Integer storeId) {
        List<Map<String, String>> dotInfo = sparePartApplyService.getStoreInfos(storeId);
        return Result.ok().put("storeList", dotInfo);
    }


    @GetMapping("/queryUserBrand")
    @ApiOperation(value = "获取当前用户有的品牌", notes = "获取当前用户有的品牌")
    public Result queryUserBrand() {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        List<Long> roleIdList = user.getRoleIdList();
        List<MagrAreaBrand> brands;
        if (roleIdList.size() == 1 && roleIdList.contains(2L)) {
            brands = sysUserDao.listUserBrand(user.getUserId());
        } else {
            brands = sysUserDao.listBrand();
        }
        return Result.ok().put("brands", brands);

    }


    @GetMapping("/queryBrandMateriel")
    @ApiOperation(value = "获取品牌的所有物料", notes = "获取当前用户有的品牌")
    public Result queryBrandMateriel(Integer brandId) {
        if (brandId == null) {
            return Result.error("请选择品牌");
        }
        List<MaterielInformationEntity> list = materialInformationDao.selectByBrandId(brandId);
        return Result.ok().put("materielList", list);
    }

    @GetMapping("/getById")
    @ApiOperation(value = "获取备件申请详情", notes = "获取备件申请详情")
    public Result getByNo(Integer id) {
        if (id == null) {
            return Result.error("id不能为空");
        }
        return Result.ok().put("sparePartApply", sparePartApplyService.getById(id));
    }

    /**
     * 列表
     */
    @PostMapping("/pageList")
    public Result partApplyList(@RequestBody SparePartApplyParamDTO params) {
        PageUtils page = sparePartApplyService.partApplyList(params);
        return Result.ok().put("page", page);
    }

    /**
     * 新增
     * @param entity
     * @return
     */
    @PostMapping("/save")
    public Result save(@RequestBody SparePartApplyEntity entity) {
        boolean save = sparePartApplyService.add(entity);
        return save ? Result.ok(): Result.error();
    }


    /**
     * 修改
     */
    @PostMapping("/update")
    public Result update(@RequestBody SparePartApplyEntity entity) {
        boolean update = sparePartApplyService.updateById(entity);
        return update ? Result.ok(): Result.error();
    }

    /**
     * 审批
     */
    @PostMapping("/audit")
    public Result audit(@RequestBody SparePartApplyEntity entity) {
        boolean audit = sparePartApplyService.audit(entity);
        return audit ? Result.ok(): Result.error();
    }

    /**
     * 批量审批
     */
    @PostMapping("/batchAudit")
    public Result batchAudit(@RequestBody List<Long> ids) {
        boolean audit = sparePartApplyService.batchAudit(ids);
        return audit ? Result.ok(): Result.error();
    }

    /**
     * 逻辑删除接口
     */
    @PostMapping("/delete")
    public Result delete(@RequestBody SparePartApplyEntity entity) {
        boolean deleted = sparePartApplyService.delete(entity.getId());
        return deleted ? Result.ok(): Result.error();
    }

    /**
     * 获取查询该网点对应品牌的订单未到安装中的订单数量(近两年)
     *
     * @return
     */
    @GetMapping("/queryDotBrandWorderNum")
    @ApiOperation(value = "获取查询该网点对应品牌的订单未到安装中的订单数量", notes = "获取查询该网点对应品牌的订单未到安装中的订单数量")
    public Result queryDotBrandWorderNum(Integer brandId,Integer dotId) {
        if (brandId == null) {
            return Result.error("请选择品牌");
        }
        List<Integer> dotIdList = new ArrayList<>();
        dotIdList.add(dotId);
        List<Integer> brandIdList = new ArrayList<>();
        brandIdList.add(brandId);
        //  获取查询该网点对应品牌的订单未到安装中的订单数量(近两年)
        List<WorderCountDTO> list = sysUserDao.queryDotBrandWorderNum(dotIdList, brandIdList);
        WorderCountDTO worderCountDTO = list.stream().filter(w -> w.getBrandId().equals(brandId) && w.getDotId().equals(dotId)).findFirst().orElse(null);
        return Result.ok().put("num", worderCountDTO != null ? worderCountDTO.getNum() : 0);
    }



    /**
     * 查询该物料的在该网点所有仓库的在库数量
     *
     * @return
     */
    @GetMapping("/queryDotMaterialNum")
    @ApiOperation(value = "查询该物料的在该网点所有仓库的在库数量", notes = "查询该物料的在该网点所有仓库的在库数量")
    public Result queryDotMaterialNum(@RequestParam Integer materielId,@RequestParam Integer dotId) {
        if (materielId == null || materielId == 0) {
            return Result.error("请选择物料");
        }

        if (dotId == null || dotId == 0) {
            SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            DotInformationEntity dot = sparePartApplyDao.getDotByUserId(user.getUserId());
            if (dot == null) {
                return Result.error("当前用户无网点信息,请选择网点");
            } else {
                dotId = dot.getDotId();
            }
        }

        List<WorderCountDTO> list = materialInformationDao.queryDotMaterialNum(Collections.singletonList(dotId), Collections.singletonList(materielId));
        if (CollectionUtils.isEmpty(list)){
            return Result.ok().put("num", 0);
        }
        return Result.ok().put("num", list.get(0).getNum());
    }


    /**
     * 网点导出
     */
    @PostMapping("/dotExport")
    public void dotExport(@RequestBody SparePartApplyParamDTO params, HttpServletResponse response) {
        try {
            List<SparePartApplyEntity> list = sparePartApplyService.selectSparePartApplyList(params);
            List<SparePartApplyExcelVO>  excelVOS
                    = list.stream().map(w -> {
                SparePartApplyExcelVO vo = new SparePartApplyExcelVO();
                BeanUtils.copyProperties(w, vo);
                return vo;
            }).collect(Collectors.toList());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;");
            EasyExcelFactory.write(response.getOutputStream(), SparePartApplyExcelVO.class).autoCloseStream(Boolean.TRUE).sheet("备件网点导出列表").doWrite(excelVOS);
        } catch (Exception e) {
            log.error("",e);
        }
    }

    /**
     * 发货导出
     */
    @PostMapping("/outstoreExport")
    public void outstoreExport(@RequestBody SparePartApplyParamDTO params, HttpServletResponse response) {
        try {
            params.setApplyStatus(SparePartApplyStatus.AUDITED.getCode());
            List<SparePartApplyEntity> list = sparePartApplyService.selectSparePartApplyList(params);
            List<SparePartApplyExcelVO>  excelVOS = new ArrayList<>();
            list.forEach(w -> {
                Integer auditNum = w.getAuditNum();
                for (int i = 0; i < auditNum; i++) {
                    SparePartApplyExcelVO vo = new SparePartApplyExcelVO();
                    BeanUtils.copyProperties(w, vo);
                    vo.setAuditNum(1);
                    excelVOS.add(vo);
                }
            });
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;");
            EasyExcelFactory.write(response.getOutputStream(), SparePartApplyExcelVO.class).autoCloseStream(Boolean.TRUE).sheet("备件发货列表").doWrite(excelVOS);
        } catch (Exception e) {
            log.error("",e);
        }
    }

    /**
     * 审批导出
     */
    @PostMapping("/auditExport")
    public void auditExport(@RequestBody SparePartApplyParamDTO params, HttpServletResponse response) {
        try {
            params.setApplyStatus(SparePartApplyStatus.SUBMITTED.getCode());
            List<SparePartApplyEntity> list = sparePartApplyService.selectSparePartApplyList(params);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }

            //找到所有网点dot_id和物料
            List<Integer> dotIdList = list.stream().map(SparePartApplyEntity::getDotId).distinct().collect(Collectors.toList());
            List<Integer> materielIdList = list.stream().map(SparePartApplyEntity::getMaterialId).distinct().collect(Collectors.toList());
            List<Integer> brandIdList = list.stream().map(SparePartApplyEntity::getBrandId).distinct().collect(Collectors.toList());
            //  查询该物料的在该网点所有仓库的在库数量 dotId->materielId
            List<WorderCountDTO> l1 = materialInformationDao.queryDotMaterialNum(dotIdList, materielIdList);
            // 获取查询该网点对应品牌的订单未到安装中的订单数量 dotId->brandId
            List<WorderCountDTO> l2 = sysUserDao.queryDotBrandWorderNum(dotIdList, brandIdList);


            List<SparePartApplyAuditExcelVO> excelVOS = list.stream().map(w -> {
                SparePartApplyAuditExcelVO vo = new SparePartApplyAuditExcelVO();
                BeanUtils.copyProperties(w, vo);
                vo.setAuditNum(vo.getApplyNum());
                int noFinishNum = l2.stream()
                        .filter(w1 ->
                                    (int) w1.getBrandId() == w.getBrandId() && (int) w1.getDotId() == w.getDotId())
                        .mapToInt(WorderCountDTO::getNum).sum();
                vo.setNoFinishNum(noFinishNum);
                Integer storeNum = l1.stream()
                        .filter(w1 ->
                                (int) w1.getDotId() == w.getDotId() && (int) w1.getMaterielId() == w.getMaterialId())
                        .mapToInt(WorderCountDTO::getNum).sum();
                vo.setStoreNum(storeNum);
                int needNum = vo.getNoFinishNum() - vo.getStoreNum();
                // needNum = Math.max(needNum, 0);
                vo.setNeedNum(needNum);
                return vo;
            }).collect(Collectors.toList());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;");
            // 定义下拉列表的值
            EasyExcelFactory.write(response.getOutputStream(), SparePartApplyAuditExcelVO.class).registerWriteHandler(new CustomSheetWriteHandler(excelVOS.size())).autoCloseStream(Boolean.TRUE).sheet("备件审核导出列表").doWrite(excelVOS);
        } catch (Exception e) {
            log.error("导出异常", e);
        }
    }

    /**
     * 审核导入
     */
    @PostMapping("/auditImport")
    public R importFile(@RequestParam("file") MultipartFile file) throws IOException {
        //获取文件名
        String fileName = file.getOriginalFilename();
        //获取文件的后缀名为xlsx
        assert fileName != null;
        String fileXlsx = fileName.substring(fileName.length() - 5);
        String fileXls = fileName.substring(fileName.length() - 4);
        //校验文件扩展名
        //如果不是excel文件
        if (!(".xlsx".equals(fileXlsx) || ".xls".equals(fileXls))) {
            return R.error().put("msg", "文件类型不正确！");
        }
        List<SparePartApplyAuditExcelVO> list = new ArrayList<>();
        //读取文件并且更新表
        EasyExcelFactory.read(file.getInputStream(), SparePartApplyAuditExcelVO.class, new SparePartApplyAuditListener(list)).sheet().doRead();
        return sparePartApplyService.batchImportAudit(list);
    }


}