package com.bonc.rrs.spider.strategy;

import com.bonc.rrs.spider.dto.JhOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class JhOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Arrays.asList(557, 558,559,560,561));

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void preProcess(OrderContext context) {
        JhOrderDto apiDto = (JhOrderDto) context.getRequestDto().getExtFields();
        List<WorderExtFieldEntity> extFieldList = context.getWorderInfoEntity().getWorderExtFieldList();
        //list转set
        Set<WorderExtFieldEntity> extFieldSet = new HashSet<>(extFieldList);
        extFieldSet.add(WorderExtFieldEntity.create(306, "工单来源", apiDto.getWorderSource()));
        context.getWorderInfoEntity().setWorderExtFieldList(new ArrayList<>(extFieldSet));
    }
}
