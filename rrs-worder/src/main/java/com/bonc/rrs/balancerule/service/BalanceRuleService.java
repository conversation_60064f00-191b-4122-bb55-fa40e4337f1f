package com.bonc.rrs.balancerule.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.balancerule.entity.BalanceRuleEntity;
import com.bonc.rrs.balancerule.vo.BalanceRuleQueryVO;
import com.bonc.rrs.balancerule.vo.DictionaryVO;
import com.bonc.rrs.balancerule.vo.MaterielQueryVO;
import com.bonc.rrs.balancerule.vo.MaterielTypeVO;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;

import java.util.List;

/**
 * Created by liqingchao on 2020/1/14.
 */
public interface BalanceRuleService extends IService<BalanceRuleEntity>{
    PageUtils pageList(BalanceRuleQueryVO balanceRuleQueryVO);

    BalanceRuleEntity queryOne(Integer balanceRuleId);

    String taxRatePercent(String taxRate);

    R addBalanceRule(BalanceRuleEntity balanceRuleEntity);

    R updateBalanceRule(BalanceRuleEntity balanceRuleEntity);

    R copyBalanceRule(BalanceRuleEntity balanceRuleEntity);

    void batchDeleteBalanceRule(String balanceRuleIds);

    List<MaterielTypeVO> listMaterielType(boolean containSuite);

    PageUtils pageListMateriel(MaterielQueryVO materielQueryVO);

    List<DictionaryVO> listDictionaryByType(String type);

    Object selectSpecifyMateriel();

    PageUtils roleAuthPageList(BalanceRuleQueryVO balanceRuleQueryVO);

    R switchProject(String detailNumber);
}
