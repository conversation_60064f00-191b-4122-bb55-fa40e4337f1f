package com.youngking.renrenwithactiviti.modules.sys.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.UploadFile;
import com.youngking.renrenwithactiviti.modules.oss.cloud.OSSFactory;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUploadRecordEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUploadRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;


/**
 * 文件上传记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-05 09:29:43
 */
@RestController
@RequestMapping("/sys/sysuploadrecord")
@Api(tags = {"文件上传相关接口"})
public class SysUploadRecordController extends AbstractController {
    @Autowired
    private SysUploadRecordService sysUploadRecordService;
    @Value("${baseUrl}")
    private String baseUrl;

    /**
     * 列表
     */
	@GetMapping("/list")
    @RequiresPermissions("sys:sysuploadrecord:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = sysUploadRecordService.queryPage(params);

        return R.ok().putPage(page);
    }


    /**
     * 信息
	 */
	@GetMapping("/info/{id}")
    @RequiresPermissions("sys:sysuploadrecord:info")
    public R info(@PathVariable("id") Integer id) {
		SysUploadRecordEntity sysUploadRecord = sysUploadRecordService.getById(id);

        return R.ok().put("sysUploadRecord", sysUploadRecord);
    }

    /**
     * 保存
	 */
	@PostMapping("/save")
    @RequiresPermissions("sys:sysuploadrecord:save")
    public R save(@RequestBody SysUploadRecordEntity sysUploadRecord) {
		sysUploadRecordService.save(sysUploadRecord);

        return R.ok();
    }

    /**
     * 保存
     */
    @PostMapping(value = "/uploadfile")
    @ApiOperation(value = "文件上传", notes = "单个文件上传")
    @ApiImplicitParams({
//            @ApiImplicitParam(paramType = "form", dataType = "String", name = "businessDir", value = "文件储存位置", required = false),
            @ApiImplicitParam(paramType = "form", dataType = "String", name = "uuid", value = "uuid", required = false),
            @ApiImplicitParam(paramType = "form", dataType = "__file", name = "file", value = "文件", required = false)

    })
    /* @RequiresPermissions("sys:sysuploadrecord:uploadfile")*/
    public R uploadfile(@RequestParam String uuid, @RequestParam MultipartFile file) {
        if (file.isEmpty()) {
            return R.error(444, "文件不能为空");
        } else {
            SysUploadRecordEntity sysUploadRecord = new SysUploadRecordEntity();
            sysUploadRecord.setUuid(uuid);
            sysUploadRecord.setStatus(0);
            sysUploadRecord.setFileSize(file.getSize());
            sysUploadRecord.setFileName(file.getOriginalFilename());
            sysUploadRecord.setFileType(file.getContentType());
            sysUploadRecord.setCreateUserId(Integer.parseInt(getUserId().toString()));
            sysUploadRecord.setUpdateTime(new Date());
            sysUploadRecord.setCreateTime(new Date());
            sysUploadRecord.setFileFirsturl(baseUrl);
            try {
//                sysUploadRecord.setFileUrl(UploadFile.uploadFile(file, businessDir));
                String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
                String url = OSSFactory.build().uploadSuffix(file.getBytes(), suffix);
                sysUploadRecord.setFileUrl(url);
            } catch (IOException e) {
                e.printStackTrace();
            }
            sysUploadRecord.setBusinessDir("basick");
            sysUploadRecordService.save(sysUploadRecord);
            return R.ok().put("file", sysUploadRecord);
        }
    }

    /**
     * 更新文件
     */
    @PostMapping("/updatefile")
    @ApiOperation("更新文件")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "form", dataType = "String", name = "url", value = "文件url", required = false),
            @ApiImplicitParam(paramType = "form", dataType = "__file", name = "file", value = "文件", required = false)

    })
    public R updateFile(@RequestParam String url, @RequestParam MultipartFile file) {
        String uuid = deleteFileByUrl(url);
        return this.uploadfile(uuid, file);
    }
    /* @RequiresPermissions("sys:sysuploadrecord:uploadfile")*/
//	public R updatefile(@RequestParam Integer id, @RequestParam MultipartFile file) {
//		if (file.isEmpty()) {
//			return R.error(444, "文件不能为空");
//		} else {
//			SysUploadRecordEntity sysUploadRecord = sysUploadRecordService.getById(id);
//			sysUploadRecord.setFileSize(file.getSize());
//			sysUploadRecord.setFileName(file.getOriginalFilename());
//			sysUploadRecord.setFileType(file.getContentType());
//			sysUploadRecord.setCreateUserId(Integer.parseInt(getUserId().toString()));
//			sysUploadRecord.setUpdateTime(new Date());
//			sysUploadRecord.setFileFirsturl(baseUrl);
//			sysUploadRecord.setStatus(0);
//			UploadFile.delFile(sysUploadRecord.getFileUrl());
//			try {
//				sysUploadRecord.setFileUrl(UploadFile.uploadFile(file, sysUploadRecord.getBusinessDir()));
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//			sysUploadRecordService.updateById(sysUploadRecord);
//			return R.ok().put("file", sysUploadRecord);
//		}
//	}

    public String deleteFileByUrl(String url) {
        SysUploadRecordEntity entity = sysUploadRecordService.getOne(new QueryWrapper<>(SysUploadRecordEntity.builder().fileUrl(url).build()));
        sysUploadRecordService.removeById(entity.getId());
        return entity.getUuid();
    }

    /**
     * 更新文件
	 */
	@PostMapping(value = "/delfile")
    /* @RequiresPermissions("sys:sysuploadrecord:uploadfile")*/
	@ApiOperation(value = "删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "String", name = "id", value = "文件id", required = true)
    })
    public R delfile(@RequestBody SysUploadRecordEntity tss) {
        SysUploadRecordEntity sysUploadRecord = sysUploadRecordService.getById(tss.getId());
        if (sysUploadRecord == null) {
            return R.error(444, "查无文件");
        } else {
            try {
                UploadFile.delFile(sysUploadRecord.getFileUrl());
            } catch (Exception e) {
                e.printStackTrace();
            }
            sysUploadRecordService.removeById(sysUploadRecord);
            return R.ok();
        }
    }

	/**
	 * 修改
	 */
	@PostMapping("/update")
    @RequiresPermissions("sys:sysuploadrecord:update")
    public R update(@RequestBody SysUploadRecordEntity sysUploadRecord) {
		sysUploadRecordService.updateById(sysUploadRecord);

        return R.ok();
    }

	/**
	 * 删除
	 */
	@PostMapping("/delete")
    @RequiresPermissions("sys:sysuploadrecord:delete")
    public R delete(@RequestBody Integer[] ids) {
		sysUploadRecordService.removeByIds(Arrays.asList(ids));

        return R.ok();
	}

}
