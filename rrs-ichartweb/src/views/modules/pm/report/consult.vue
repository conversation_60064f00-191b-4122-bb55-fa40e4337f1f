<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :visible.sync="visible">
    <div class="pa-title">
      <h2>征询</h2>
    </div>
    <el-form :model="dataForm" ref="dataForm" label-width="120px" class="pa20"  :rules="rules">
      <el-form-item label="征询人:" prop="userId">
        <multi-dept-user-select-pro
          v-if="visible"
          @sendUserInfo="sendUserInfo($event)"
          ref="deptUserSelect2"
          v-model="dataForm.userId"
          :uname="dataForm.userName">
        </multi-dept-user-select-pro>
      </el-form-item>
      <el-form-item label="征询意见:" prop="remark">
        <el-input type="textarea" placeholder="请输入备注" v-model="dataForm.remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="cancel()">取消</el-button>
        <!--<el-button type="primary" @click="saveConsult(0)">保存</el-button>-->
        <el-button type="primary" @click="saveConsult(1)">征询</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
  import MultiDeptUserSelectPro from '@/components/user/multi-dept-user-selector-pro'

  export default {
    data () {
      return {
        visible: false,
        dataForm: {
          opType: 2,
          taskId: '',
          remark: '',
          userId: undefined,
          userName: ''
        },
        rules: {
          userId: [
            { required: true, message: '征询人不能为空', trigger: ['blur', 'change'] }
          ],
          remark: [
            { required: true, message: '征询意见不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    components: {
      MultiDeptUserSelectPro
    },
    methods: {
      init(taskId, consultId) {
        // this.clearData()
        this.dataForm.opType = 2;
        this.dataForm.remark = '';
        this.dataForm.userName = '';
        this.dataForm.userId = undefined;
        this.dataForm.taskId = taskId
        this.dataForm.writeable = 0;
        if (consultId) {
          this.dataForm.pid = consultId;
          // this.dataForm.writeable = 1;
        }
        this.visible = true
        this.$nextTick(() => {
          this.$refs.dataForm.resetFields();
          this.queryNcommitableRecord(taskId)
        })
      },
      queryNcommitableRecord(taskId) {
        this.$http({
          url: this.$http.adornUrl(`/pm/consultassign/queryNcommitableRecord/${taskId}`),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.record && data.record.length > 0) {
              //保存没有提交的，回显原来的值
              this.dataForm.id = data.record[0].id;
              this.dataForm.userId = data.record[0].persons;
              this.dataForm.userName = data.record[0].personNames;
              this.dataForm.remark = data.record[0].remark
              this.dataForm.personList = data.record[0].personList
            }
          }
        })
      },
      sendUserInfo(userInfo) {
        this.dataForm.personList = userInfo
      },
      saveConsult(status) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.dataForm.commitable = status
            this.$http({
              url: this.$http.adornUrl('/pm/consultassign/saveConsultAssign'),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message.success({
                  message: '操作成功',
                  duration: 600
                })
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
            });
          }
        })
      },
      cancel() {
        this.visible = false
        this.clearData()
      },
      clearData() {
        this.$nextTick(() => {
        })
        if (this.$refs.dataForm) {
          this.$refs.dataForm.resetFields();
        }
        this.dataForm = {
          opType: 2,
          taskId: '',
          remark: '',
          userId: undefined,
          userName: ''
        };
        this.$set(this.dataForm, 'userName', '');
        // this.dataForm.userName = '';
      }
    }
  }
</script>

<style scoped>
</style>
