package com.bonc.rrs.worder.dto.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * 客户满意度
 * @date 2020/5/811:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientSatisfactionVo {

    private Integer id;
    private String worderNo; //工单编号
    private Integer serviceSatisfaction; //服务整体是否满意,0：否，1：是
    private String troubleSpot;  //问题点
    private String troubleSpotContent;  //问题点内容
//    private List<TroubleVo> troubleVos;  //问题点集合
    private String  satisfactionScore;  //满意度分值
    private Integer isWorkCloth;       //是否穿工服，0：否，1：是
    private Integer isShowDocument;    //是否有出示品牌独有文件,0：否，1：是
    private Integer isShowAgreement;   //是否出示安装服务协议或增项报价标准,0：否，1：是
    private Integer isAssistBind;      //是否协助绑定APP,0：否，1：是
    private String  customerAdvice;     //客户宝贵建议
}
