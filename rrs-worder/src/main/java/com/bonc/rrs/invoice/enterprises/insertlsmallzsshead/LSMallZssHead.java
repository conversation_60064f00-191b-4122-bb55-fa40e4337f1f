
package com.bonc.rrs.invoice.enterprises.insertlsmallzsshead;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="xsddm" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="dmgs" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="khdm" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="khswdjh" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="khmc" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="khsj" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="khdz" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="khyh" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="bz" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="skr" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="fhr" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="kprq" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="fpzl" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="zx" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="fphm" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="xrrq" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "xsddm",
    "dmgs",
    "khdm",
    "khswdjh",
    "khmc",
    "khsj",
    "khdz",
    "khyh",
    "bz",
    "skr",
    "fhr",
    "kprq",
    "fpzl",
    "zx",
    "fphm",
    "xrrq"
})
@XmlRootElement(name = "LSMallZssHead")
public class LSMallZssHead {

    @XmlElement(required = true)
    protected String xsddm;
    @XmlElement(required = true)
    protected String dmgs;
    @XmlElement(required = true)
    protected String khdm;
    @XmlElement(required = true)
    protected String khswdjh;
    @XmlElement(required = true)
    protected String khmc;
    @XmlElement(required = true)
    protected String khsj;
    @XmlElement(required = true)
    protected String khdz;
    @XmlElement(required = true)
    protected String khyh;
    @XmlElement(required = true)
    protected String bz;
    @XmlElement(required = true)
    protected String skr;
    @XmlElement(required = true)
    protected String fhr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar kprq;
    @XmlElement(required = true)
    protected String fpzl;
    @XmlElement(required = true)
    protected String zx;
    @XmlElement(required = true)
    protected String fphm;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar xrrq;

    /**
     * 获取xsddm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getXsddm() {
        return xsddm;
    }

    /**
     * 设置xsddm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setXsddm(String value) {
        this.xsddm = value;
    }

    /**
     * 获取dmgs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDmgs() {
        return dmgs;
    }

    /**
     * 设置dmgs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDmgs(String value) {
        this.dmgs = value;
    }

    /**
     * 获取khdm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhdm() {
        return khdm;
    }

    /**
     * 设置khdm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhdm(String value) {
        this.khdm = value;
    }

    /**
     * 获取khswdjh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhswdjh() {
        return khswdjh;
    }

    /**
     * 设置khswdjh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhswdjh(String value) {
        this.khswdjh = value;
    }

    /**
     * 获取khmc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhmc() {
        return khmc;
    }

    /**
     * 设置khmc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhmc(String value) {
        this.khmc = value;
    }

    /**
     * 获取khsj属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhsj() {
        return khsj;
    }

    /**
     * 设置khsj属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhsj(String value) {
        this.khsj = value;
    }

    /**
     * 获取khdz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhdz() {
        return khdz;
    }

    /**
     * 设置khdz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhdz(String value) {
        this.khdz = value;
    }

    /**
     * 获取khyh属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKhyh() {
        return khyh;
    }

    /**
     * 设置khyh属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKhyh(String value) {
        this.khyh = value;
    }

    /**
     * 获取bz属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBz() {
        return bz;
    }

    /**
     * 设置bz属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBz(String value) {
        this.bz = value;
    }

    /**
     * 获取skr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkr() {
        return skr;
    }

    /**
     * 设置skr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkr(String value) {
        this.skr = value;
    }

    /**
     * 获取fhr属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFhr() {
        return fhr;
    }

    /**
     * 设置fhr属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFhr(String value) {
        this.fhr = value;
    }

    /**
     * 获取kprq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getKprq() {
        return kprq;
    }

    /**
     * 设置kprq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setKprq(XMLGregorianCalendar value) {
        this.kprq = value;
    }

    /**
     * 获取fpzl属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFpzl() {
        return fpzl;
    }

    /**
     * 设置fpzl属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFpzl(String value) {
        this.fpzl = value;
    }

    /**
     * 获取zx属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZx() {
        return zx;
    }

    /**
     * 设置zx属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZx(String value) {
        this.zx = value;
    }

    /**
     * 获取fphm属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFphm() {
        return fphm;
    }

    /**
     * 设置fphm属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFphm(String value) {
        this.fphm = value;
    }

    /**
     * 获取xrrq属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getXrrq() {
        return xrrq;
    }

    /**
     * 设置xrrq属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setXrrq(XMLGregorianCalendar value) {
        this.xrrq = value;
    }

}
