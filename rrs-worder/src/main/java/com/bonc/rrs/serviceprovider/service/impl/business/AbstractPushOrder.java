package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.service.BizRegionService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.R;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/2/29 10:20
 */
public abstract class AbstractPushOrder extends AbstractBusinessProcess {

    @Autowired
    protected BizRegionService bizRegionService;
    @Autowired
    protected WorderInformationAttributeDao worderInformationAttributeDao;

    protected PushApiResponse error(String orderCode, String reason) {
        List<Object> dataList = new ArrayList<>();
        Map map = new HashMap();
        map.put("orderCode", orderCode);
        map.put("reason", reason);
        dataList.add(map);
        PushApiResponse pushApiResponse = new PushApiResponse("推送工单信息失败");
        pushApiResponse.setData(dataList);
        return pushApiResponse;
    }

    protected void addError(String orderCode, String reason, List<Object> dataList) {
        Map map = new HashMap();
        map.put("orderCode", orderCode);
        map.put("reason", reason);
        dataList.add(map);
    }

    protected WorderExtFieldEntity setWorderExtFieldEntity(Integer fieldId, String fieldName, Object fieldValue){
        String value = "";
        if (fieldValue != null) {
            value = String.valueOf(fieldValue);
        }
        return WorderExtFieldEntity.builder()
                .fieldId(fieldId)
                .fieldName(fieldName)
                .fieldValue(value).build();
    }

    protected Boolean validateCompanyOrderNumberAndBrandExsit(String compayOrderNumber, Integer templateId) {
        return worderInformationService.validateCompanyOrderNumberAndBrandExsit(compayOrderNumber, templateId);
    }

    /**
     * 转换比亚迪区域编码
     * @param provinceCode
     * @param cityCode
     * @param areaCode
     * @return
     */
    protected RegionCode convertRegion(String provinceCode, String cityCode, String areaCode) {
        RegionCode regionCode = new RegionCode();
        if (StringUtils.isNotBlank(provinceCode)) {
            Long bydProvinceCode = Long.valueOf(provinceCode);
            BizRegionEntity provinceRegion = bizRegionService.getRegionByBydCode(bydProvinceCode);
            if (IntegerEnum.ONE.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getId());
            } else if (IntegerEnum.TWO.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getPid());
                regionCode.setCityCode(provinceRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(provinceRegion.getType())) {
                regionCode.setCityCode(provinceRegion.getPid());
                regionCode.setAreaCode(provinceRegion.getId());
                BizRegionEntity cityRegion = bizRegionService.getById(provinceRegion.getPid());
                regionCode.setProvinceCode(cityRegion.getPid());
            }
        }

        if (StringUtils.isNotBlank(cityCode)) {
            Long bydCityCode = Long.valueOf(cityCode);
            BizRegionEntity cityRegion = bizRegionService.getRegionByBydCode(bydCityCode);
            if (IntegerEnum.TWO.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getPid());
                regionCode.setAreaCode(cityRegion.getId());
            }
        }

        // 区编码 未获取到数据 填写市编码
        if (StringUtils.isNotBlank(areaCode)) {
            Long bydAreaCode = Long.valueOf(areaCode);
            BizRegionEntity areaRegion = bizRegionService.getRegionByBydCode(bydAreaCode);
            if (areaRegion != null) {
                regionCode.setAreaCode(areaRegion.getId());
            } else {
                regionCode.setAreaCode(regionCode.getCityCode());
            }
        } else {
            regionCode.setAreaCode(regionCode.getCityCode());
        }
        return regionCode;
    }

    protected R saveWorderInformation(WorderInfoEntity worderInfoEntity) {
        return worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
    }

    protected void saveWorderAttribute(Integer worderId,
                                           String attributeCode,
                                           String attributeName,
                                           String attributeValue,
                                           String attribute) {
        WorderInformationAttributeEntity entity = new WorderInformationAttributeEntity();
        entity.setWorderId(worderId);
        entity.setAttributeCode(attributeCode);
        entity.setAttributeName(attributeName);
        entity.setAttributeValue(attributeValue);
        entity.setAttribute(attribute);
        worderInformationAttributeDao.insert(entity);
    }

    /**
     * 自动派单
     * @param worderNo
     * @param username
     */
    protected Integer goAutoSendWorder(String worderNo, String username) {
        Results results = worderInformationService.goAutoSendWorder(worderNo, username, null);
        // 修改工单状态 0
        worderInformationService.updateWorderStatus(worderNo);
        return results.getDotId();
    }
}
