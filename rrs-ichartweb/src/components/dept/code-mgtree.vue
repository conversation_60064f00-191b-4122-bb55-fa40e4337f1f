<template>
  <div>
    <el-col :span="4">
      <el-select v-model="form.province" placeholder="请选择" @visible-change="getProvince($event, 1)"
                 :disabled="form.type" @change="cleancity">
        <el-option
          v-for="item in provinceList"
          :key="item.areaCode"
          :label="item.areaName"
          :value="item.areaName">
        </el-option>
      </el-select>
    </el-col>
    <el-col :span="4" style="margin-left: 5px">
      <el-select v-model="form.city" placeholder="请选择" @visible-change="getProvince($event,form.province)"
                 :disabled="form.type" @change="cleanarea">
        <el-option
          v-for="item in provinceList"
          :key="item.areaCode"
          :label="item.areaName"
          :value="item.areaCode">
        </el-option>
      </el-select>
    </el-col>
    <el-col :span="4" style="margin-left: 5px">
      <el-select v-model="form.area" placeholder="请选择"
                 @visible-change="getCodeAreaInfoManger($event,form.city)" :disabled="form.type"
                 @change="sendMsg(form)">
        <el-option
          v-for="item in CodeAreaInfoMangerList"
          :key="item.areaCode"
          :label="item.areaName"
          :value="item.areaCode">
        </el-option>
      </el-select>
    </el-col>
    <el-col :span="11" style="margin-left: 5px">
      <el-input v-model="form.desc" placeholder="详细地址" @blur="sendMsg(form)" :disabled="form.type"></el-input>
    </el-col>

  </div>
</template>

<script>
  export default {
    props: ['form'],
    name: 'mgtree',
    data() {
      return {
        provinceList: [],
        CodeAreaInfoMangerList: [],
        forms: {
          province: '',
          city: '',
          area: '',
          desc: '',
          type: false
        }
      }
    },
    methods: {
      sendMsg(val) {
        this.$emit('sendMsg', val)
      },
      getProvince(callback, val) {
        console.log("-------------------->val", val)
        // if (val === '') {
        //   val = this.form.province
        // }
        if (callback) {
          this.$http({
            url: this.$http.adornUrl('/sys/CodeAreaInfo/getProvince'),
            method: 'get',
            params: this.$http.adornParams({name: val})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.provinceList = data.data;
              console.log("----------------->provinceList", )
            }
          })
        }
      },
      getCodeAreaInfoManger(callback, val) {
        if (val === '') {
          val = this.form.area;
        }
        if (callback) {
          this.$http({
            url: this.$http.adornUrl('/sys/CodeAreaInfo/getProvince'),
            method: 'get',
            params: this.$http.adornParams({name: val})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.CodeAreaInfoMangerList = data.data;
            }
          })
        }
      },
      cleancity() {
        this.form.city = "";
        this.form.area = "";
      },
      cleanarea() {
        this.form.area = "";
      }
    }
  }
</script>

<style scoped>
</style>
