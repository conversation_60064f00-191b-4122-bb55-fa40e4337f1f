package org.ichart.demo.module.ichartform.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.ichart.demo.module.ichartform.model.entity.RptReport;


import java.util.List;

/**
 * 报表所有的配置项，都存储在CONFIG字段中
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-08 17:10:19
 */
@Mapper
public interface RptReportDao extends BaseMapper<RptReport> {
    @Select("SELECT * from rpt_report where delete_flag =0 and status=1 order by create_time desc")
    List<RptReport> getChart();
}
