package com.bonc.rrs.worder.dao;

import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 网点主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:58
 */
@Mapper
public interface DotInformationDao extends BaseMapper<DotInformationEntity> {

    List<Map<Integer, String>> selectByDotIds(@Param("dotIdList")List<Integer> dotIdList);

    /**
     * 根据网点和品牌查询关联表的数量
     * @param dotId
     * @param brandId
     * @return
     */
    List<Integer> selectCountByDotAndBrand(@Param("dotId") Integer dotId, @Param("brandId") Integer brandId);

    List<Map<String, Object>> selectByDotValue(@Param("dotValue") String dotValue);

    /**
     * 根据网点id获取网点税点
     * @param dotId
     * @return
     */
    String getDotTaxPointByDotId(@Param("dotId") Integer dotId);

    /**
     * 查询所有网点名称与id
     * @return
     */
    List<Map<Integer, Object>> getListDotName();

    /**
     * 查询网点评分
     * @param dotId
     * @return
     */
    Integer getDotScore(@Param("dotId") Integer dotId);

    /**
     * 更新网点评分
     * @param dotId
     * @param dotScore
     */
    void updateDotScore(@Param("dotId") Integer dotId,
                        @Param("dotScore") Integer dotScore,
                        @Param("userId") Long userId);

    String selectDotByUserName(@Param("username") String username);
}
