package com.bonc.rrs.worder.controller;

import java.util.Arrays;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.bonc.rrs.worder.entity.FinanceTicketEntity;
import com.bonc.rrs.worder.service.FinanceTicketService;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.*;



/**
 * 财务发票表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-12 14:54:09
 */
@RestController
@RequestMapping("worder/financeticket")
@Api(tags = {"财务发票表相关接口"})
public class FinanceTicketController {
    @Autowired
    private FinanceTicketService financeTicketService;

    /**
     * 列表
     */
    @GetMapping("/list")
    @RequiresPermissions("worder:financeticket:list")
    @ApiOperation(value="获取列表", notes="获取列表" )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = financeTicketService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @GetMapping("/info/{ticketId}")
    @RequiresPermissions("worder:financeticket:info")
    @ApiOperation(value="获取详情", notes="获取详情" )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "Integer", name = "id", value = "id", required = true)
    })
    public R info(@PathVariable("ticketId") Integer ticketId){
		FinanceTicketEntity financeTicket = financeTicketService.getById(ticketId);

        return R.ok().put("financeTicket", financeTicket);
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    @RequiresPermissions("worder:financeticket:save")
    @ApiOperation(value="新增记录", notes="新增记录" )
    public R save(@ApiParam @RequestBody FinanceTicketEntity financeTicket){
		financeTicketService.save(financeTicket);

        return R.ok();
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @RequiresPermissions("worder:financeticket:update")
    @ApiOperation(value="修改记录", notes="修改记录" )
    public R update(@ApiParam @RequestBody FinanceTicketEntity financeTicket){
		financeTicketService.updateById(financeTicket);

        return R.ok();
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @RequiresPermissions("worder:financeticket:delete")
    @ApiOperation(value="删除记录", notes="删除记录" )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "String", name = "ids", value = "ids", required = true)
    })
    public R delete(@RequestBody Integer[] ticketIds){
		financeTicketService.removeByIds(Arrays.asList(ticketIds));

        return R.ok();
    }

}
