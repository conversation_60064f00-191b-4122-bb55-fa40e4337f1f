<template>
  <div style="position: relative">
    <el-upload
      class="upload-demo"
      :disabled="disabled || !visible"
      ref="upload_"
      name="file"
      :action="importFileUrl"
      :data="uploadData"
      :on-preview="handlePreview"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      :onError="uploadError"
      :onSuccess="uploadSuccess"
      :file-list="fileList"
      :headers="token"
      :auto-upload="true"
      :limit="1"
      :accept="acceptFileType"
      :beforeUpload="beforeFileUpload">
      <el-button slot="trigger" type="primary" v-if="visible" style="position: absolute; right: -1%;bottom: 65%;padding:7px 20px">选取文件</el-button>
      <p></p>
      <span slot="tip" class="el-upload__tip" v-if="visible" style="text-align: right;margin-top: 10%;display: inline-block;width: 100%;">（只能上传xlsm文件等，且不超过30兆）</span>
    </el-upload>
  </div>
</template>

<script>
  /* eslint-disable prefer-promise-reject-errors */
  //      :class="{'upload-demo':1===1, 'hiddenUploadArea': !visible }"

  import SparkMD5 from 'spark-md5'

  export default {
    name: 'common-upload',
    props: {
      size: {
        type: String,
        default: 'small'
      },
      value: {
        type: String
      },
      disabled: {
        type: Boolean,
        default: false
      },
      moduleId: {
        require: true,
        type: Number,
        default: 0
      },
      visible: {
        require: false,
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        importFileUrl: this.$http.adornUrl('/system/sys/attach/upload'),
        token: {token: this.$cookie.get('token')},
        acceptFileType: '.xlsm',
        fileList: [],
        uploadData: {
          bussiId: '',
          moduleId: this.moduleId,
          hashCode: '' // MD5
        }
      }
    },
    created() {
      console.log('add init', new Date());
      console.log('this.value upload ', this.value);
      this.init();
    },
    methods: {
      init() {
        if (this.value) {
          if (this.$refs.upload_) {
            this.$refs.upload_.clearFiles();
          }
          this.$http({
            url: this.$http.adornUrl('/sys/attach/select'),
            method: 'get',
            params: {bussi_id: this.value}
          }).then(({data}) => {
            this.fileList = data && data.code === 0 ? data.list : [];
            this.uploadData.bussiId = this.value;
            if (!this.visible) {
              this.$nextTick(() => {
                this.$refs.upload_.$el.children[0].style.display = 'none';
                let els = this.$refs.upload_.$el.children[1].children;
                if (els && els.length) {
                  for (let i = 0; i < this.$refs.upload_.$el.children[1].children.length; i++) {
                    // this.$refs.upload_.$el.children[1].children[i].classList.remove('is-success');
                    this.$refs.upload_.$el.children[1].children[i].children[1].remove()
                    // console.log('classList', this.$refs.upload_.$el.children[1].children[i].classList);
                  }
                }
                console.log(this.$refs.upload_);
              })
            }
          })
        } else {
          this.getBussiId();
        }
      },
      getBussiId() {
        this.$http({
          url: this.$http.adornUrl('/sys/attach/genBussid'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$emit('input', data.bussiId);
            this.uploadData.bussiId = data.bussiId;
          } else {
            this.$message.error({
              message: '获取数据失败！请稍后再试！',
              duration: 1500
            });
          }
        })
      },
      // 上传成功后的回调
      uploadSuccess(response, file, fileList) {
        if (response.code === 0) {
          console.log('上传文件', fileList);
          this.fileList = fileList;
          this.$emit('validateUpload');
        } else {
          this.fileList.pop();
          this.$emit('validateUpload');
          this.$message.error({
            message: '上传失败，请重试！',
            duration: 1500
          });
        }
      },
      // 上传错误
      uploadError(response, file, fileList) {
        this.$message.error({
          message: '上传失败，请重试！',
          duration: 1500
        });
      },
      // 上传前对文件的大小的判断
      beforeFileUpload(file) {
        const fileExt = file.name.substr(file.name.lastIndexOf('.'));
        const isLt2M = file.size / 1024 / 1024 < 1000;
        const isValidExt = (this.acceptFileType.concat(',')).includes(fileExt.toLowerCase().concat(','));
        if (!isValidExt) {
          this.$message.warning({
            message: '只能上传xlsm等文件!',
            duration: 1500
          });
          return false;
        }
        if (!isLt2M) {
          this.$message.warning({
            message: '上传文件大小不能超过 10MB!',
            duration: 1500
          });
          return false;
        }
        return this.fileMd5Calculate(file);
      },
      // 获取已上传文件数量
      getFileCount() {
        return this.fileList.length;
      },
      submitUpload() {
        this.$refs.upload.submit();
      },
      beforeRemove(file) {
        if (file && file.status === 'success') {
          return new Promise((resolve, reject) => {
            this.$confirm('确认删除附件?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              resolve();
            }).catch(() => {
              reject();
            });
          })
        }
      },
      // id + bussiId 防接口恶意操作
      handleRemove(file, fileList) {
        let id = file.response ? file.response.id : file.id;
        let bussiId = file.response ? file.response.bussiId : file.bussiId;
        this.$http({
          url: this.$http.adornUrl(`/sys/attach/${id}/${bussiId}`),
          method: 'delete',
          data: this.$http.adornData()
        }).then(({data}) => {
          if (data && data.code === 0) {
            console.log(this.fileList);
            this.fileList = this.fileList.filter((item) => (item.response && item.response.id !== id) || (!item.response && item.id !== id));
            console.log('删除成功');
            this.$emit('validateUpload');
          }
        })
      },
      handlePreview(file) {
        let id = file.response ? file.response.id : file.id;
        this.$downloadFileById(id);
      },
      // see https://github.com/satazor/js-spark-md5#hash-a-file-incrementally
      fileMd5Calculate(file) {
        let that = this;
        return new Promise(function (resolve, reject) {
          let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
          let chunkSize = 2097152;                          // Read in chunks of 2MB
          let chunks = Math.ceil(file.size / chunkSize);
          let currentChunk = 0;
          let spark = new SparkMD5.ArrayBuffer();
          let fileReader = new FileReader();
          fileReader.onload = function (e) {
            console.log('read chunk nr', currentChunk + 1, 'of', chunks);
            spark.append(e.target.result);                   // Append array buffer
            currentChunk++;

            if (currentChunk < chunks) {
              loadNext();
            } else {
              console.log('finished loading');
              let md5 = spark.end();                        // Compute hash
              console.info('computed hash', md5);
              // callBack(md5);
              that.uploadData.hashCode = md5;
              resolve();
            }
          };

          fileReader.onerror = function () {
            console.warn('oops, something went wrong.');
            reject();
          };

          function loadNext() {
            let start = currentChunk * chunkSize;
            let end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
            fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
          }

          loadNext();
        });
      },
      clearFileList() {
        if (this.$refs.upload_) {
          this.$refs.upload_.clearFiles();
          if (this.uploadData.bussiId) {
            this.getBussiId();
          }
        }
      }
    }
  }
</script>

<style scoped>
  .hiddenUploadArea {
    display: none
  }
  .el-upload--text{width: 100%}
</style>

