<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ichart.demo.module.ichartform.dao.RptDataSourceDao">
    <select id="selectPoolPageVo" parameterType="java.util.Map"
            resultType="org.ichart.demo.module.ichartform.model.entity.RptDataSource">
        SELECT * from rpt_data_source
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="params.tableName != null and params.tableName != ''">
                <!--                AND table_name like "%"#{params.tableName}"%"-->
                AND table_name like CONCAT('%', #{params.tableName}, '%')
            </if>
            <if test="params.name != null and params.name != ''">
                <!--                AND (table_name like "%"#{params.name}"%" or  table_code like "%"#{params.name}"%" )-->
                AND (table_name like CONCAT('%', #{params.name}, '%') or table_code like CONCAT('%', #{params.name},
                '%') )
            </if>
            <if test="params.dataType != null and params.dataType != ''">
                AND data_type = #{params.dataType}
            </if>
            <if test="params.type != null and params.type != ''">
                AND type = #{params.type}
            </if>
            <if test="params.userId != null and params.userId != ''">
                AND user_id = #{params.userId}
            </if>
            <if test="params.createUser != null and params.createUser != ''">
                AND create_user = #{params.createUser}
            </if>
            and delete_flag !=0
        </trim>
        ORDER BY create_time desc
    </select>

    <select id="getDataSourceItem" parameterType="java.util.Map"
            resultType="org.ichart.demo.module.ichartform.model.entity.RptDataSource">
        select * from rpt_data_source
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="id != null and id != ''">
                AND id =#{id}
            </if>
            <if test="tableCode != null and tableCode != ''">
                AND table_code = #{tableCode}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            AND delete_flag = 1 AND type=1
        </trim>
    </select>

    <select id="selectBydataSource"
            resultType="org.ichart.demo.module.ichartform.model.entity.RptDataSource">
        select * from rpt_data_source
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            AND (
            <trim prefix=" " prefixOverrides="AND|OR">
                <if test="tableCode != null and tableCode != ''">
                    AND table_code = #{tableCode}
                </if>
                <if test="id != null and id != ''">
                    OR id = #{id}
                </if>
            </trim>
            )
            AND delete_flag = 1
        </trim>
    </select>
</mapper>
