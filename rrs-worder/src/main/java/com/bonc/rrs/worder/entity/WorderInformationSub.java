package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * 工单信息子表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@TableName(value = "worder_information_sub")
public class WorderInformationSub implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 工单表主键
     */
    @TableField(value = "worder_id")
    private Integer worderId;

    /**
     * 网点派单计数标识：0：标识该工单的网点派单不计数
     */
    @TableField(value = "dot_count_state")
    private Integer dotCountState;

    /**
     * 是否需要电力报桩：0：否 ，1：是
     */
    @TableField(value = "electricity_state")
    private Long electricityState;

    /**
     * 不结算原因
     */
    @TableField(value = "no_settlement_reason")
    private String noSettlementReason;

    /**
     * 未结算备注
     */
    @TableField(value = "no_settlement_remark")
    private String noSettlementRemark;

    /**
     * 创建时间
     */
    @TableField(value = "insert_time")
    private LocalDateTime insertTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}