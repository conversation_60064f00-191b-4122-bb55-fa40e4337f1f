package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.BydFaultCodes;
import com.bonc.rrs.byd.domain.BydFaultCodesMapping;
import com.bonc.rrs.byd.domain.PushFile;
import com.bonc.rrs.byd.domain.RepairAfterSalesInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.BydFaultCodesMappingService;
import com.bonc.rrs.byd.service.BydFaultCodesService;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.gexin.fastjson.JSON;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 报修附件信息(重构)回传
 * @Description /jumpto/openapi/sp/ao/pushRefactorProcessing
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessRepairPushAccessoriesInfoNew extends AbstractBusinessProcess {

    final WorderExtFieldService worderExtFieldService;

    final SysFilesService sysFilesService;

    final IBydApiService iBydApiService;

    final BydFaultCodesService bydFaultCodesService;

    final BydFaultCodesMappingService bydFaultCodesMappingService;

    @Override
    public String getProcessCode() {
        return "pushRepairAccessoriesInfoNew";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }
            // 工单扩展字段
            List<WorderExtFieldEntity> extFieldEntities = worderExtFieldService.getSpecificFields(businessProcessPo.getWorderNo(), null);
            Map<Integer, String> fieldMap = new HashMap<>();
            for (WorderExtFieldEntity extFieldEntity : extFieldEntities) {
                fieldMap.put(extFieldEntity.getFieldId(), extFieldEntity.getFieldValue());
            }
            //故障码表
            Map<String, BydFaultCodes> faultCodesMap = bydFaultCodesService.getAllFaultCodes().stream().collect(Collectors.toMap(BydFaultCodes::getCode, Function.identity()));

            //比亚迪故障码
            String faultCode = fieldMap.get(2107);
            //比亚迪故障码对象
            BydFaultCodes bydFaultCodes = faultCodesMap.get(faultCode);
            if (bydFaultCodes == null || bydFaultCodes.getIsLeaf() != 1) {
                return Result.error("比亚迪故障码不正确，请重新选择");
            }
            List<BydFaultCodesMapping> faultCodesMappings = bydFaultCodesMappingService.getBydFaultCodesMapping(faultCode);
            BydFaultCodesMapping manPileImage = new BydFaultCodesMapping();
            manPileImage.setCode("manPileImage");
            manPileImage.setFieldName("人桩合照");
            manPileImage.setFieldId(2089);
            manPileImage.setType(1);
            faultCodesMappings.add(manPileImage);

            BydFaultCodesMapping repairConfirmDocument = new BydFaultCodesMapping();
            repairConfirmDocument.setCode("repairConfirmDocument");
            repairConfirmDocument.setFieldName("维修确认单");
            repairConfirmDocument.setFieldId(1730);
            repairConfirmDocument.setType(1);
            faultCodesMappings.add(repairConfirmDocument);

            BydFaultCodesMapping increaseChargeImage = new BydFaultCodesMapping();
            increaseChargeImage.setCode("increaseChargeImage");
            increaseChargeImage.setFieldName("增项收费单");
            increaseChargeImage.setFieldId(1729);
            increaseChargeImage.setType(1);
            faultCodesMappings.add(increaseChargeImage);


            List<String> errorCodes = new ArrayList<>();
            for (BydFaultCodesMapping codesMapping : faultCodesMappings) {
                String fieldValue = fieldMap.get(codesMapping.getFieldId());
                if ("1".equals(codesMapping.getIsNotnull()) && StringUtils.isBlank(fieldValue)) {
                    errorCodes.add(codesMapping.getFieldName());
                    continue;
                }
                codesMapping.setFieldValue(fieldValue);
            }
            if (!errorCodes.isEmpty()) {
                return Result.error("缺少必填资料：" + String.join(",", errorCodes));
            }

            //文件ID
            List<String> fileIdList = faultCodesMappings.stream()
                    .map(BydFaultCodesMapping::getFieldValue)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            //添加人桩合照、维修确认单、增项收费单
            String fileIds = StringUtils.join(fileIdList,  ",");

            List<SysFileEntity> fileList = sysFilesService.getSysFileByIds(fileIds);
            Map<Integer, String> fileMap = fileList.stream().collect(Collectors.toMap(SysFileEntity::getFileId, SysFileEntity::getNewName, (e1, e2) -> e2));

            for (BydFaultCodesMapping faultCodesMapping : faultCodesMappings) {
                String fieldValue = faultCodesMapping.getFieldValue();
                if (StringUtils.isNotBlank(fieldValue)) {
                    // 字符串逗号分割
                    String[] split = fieldValue.split(",");
                    // 遍历
                    for (String value : split) {
                        if (fileMap.containsKey(Integer.valueOf(value))) {
                            faultCodesMapping.setFileUrl(FileUtils.copyImage(fileMap.get(Integer.valueOf(value))));
                        }
                    }
                }
            }

            Map<String, String> picAttrs = new HashMap<>();
            //添加桩合照--必填
            //picAttrs.put("manPileImage", FileUtils.copyImage(fileMap.get(2089)));
            //添加维修确认单--必填
            //picAttrs.put("repairConfirmDocument", FileUtils.copyImage(fileMap.get(1730)));
            //添加增项收费单--非必填
            //picAttrs.put("increaseChargeImage", FileUtils.copyImage(fileMap.get(1729)));
            for (BydFaultCodesMapping faultCodesMapping : faultCodesMappings) {
                if (faultCodesMapping.getType() == 1) {
                    picAttrs.put(faultCodesMapping.getCode(), faultCodesMapping.getFileUrl());
                }
            }

            //删除picAttrs中值为空的key
            picAttrs.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
            //遍历picAttrs，修改value
            for (Map.Entry<String, String> entry : picAttrs.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (StringUtils.isNotBlank(value)) {
                    OtherApiResponse apiResponse = iBydApiService.fileUpload(PushFile.builder().url(value).build());
                    if (apiResponse.getErrno() != 0) {
                        return Result.error(apiResponse.getErrmsg());
                    }
                    value = apiResponse.getData().toString();
                    picAttrs.put(key, value);
                }
            }

            //视频附件
            Map<String, String> videoAttrs = null;
            for (BydFaultCodesMapping bydFaultCodesMapping : faultCodesMappings) {
                if (bydFaultCodesMapping.getType() == 2) {
                    videoAttrs = (videoAttrs == null? new HashMap<>() : videoAttrs);
                    videoAttrs.put(bydFaultCodesMapping.getCode(), bydFaultCodesMapping.getFileUrl());
                }
            }

            //遍历picAttrs，修改value
            if (videoAttrs != null) {
                //删除picAttrs中值为空的key
                videoAttrs.entrySet().removeIf(entry -> entry.getValue() == null || entry.getValue().isEmpty());
                for (Map.Entry<String, String> entry : videoAttrs.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    if (StringUtils.isNotBlank(value)) {
                        OtherApiResponse apiResponse = iBydApiService.fileUpload(PushFile.builder().url(value).build());
                        if (apiResponse.getErrno() != 0) {
                            return Result.error(apiResponse.getErrmsg());
                        }
                        value = apiResponse.getData().toString();
                        videoAttrs.put(key, value);
                    }
                }
            }


            String doorSolutionFieldValue = fieldMap.get(2015);
            //是否上门解决值是：是、否，转换成1、0
            if (StringUtils.isBlank(doorSolutionFieldValue)) {
                errorCodes.add("是否上门解决");
            }
            String finishTimeFieldValue = fieldMap.get(1711);
            if (StringUtils.isBlank(finishTimeFieldValue)) {
                errorCodes.add("售后完成时间");
            }
            if (!errorCodes.isEmpty()) {
                return Result.error("缺少必填信息：" + String.join(",", errorCodes));
            }
            //更换的主材
            String exchangeMainMaterialFieldValue = fieldMap.get(2110);
            //更换的辅材
            String exchangeAuxiliaryMaterialFieldValue = fieldMap.get(2111);
            //充电桩编码
            String newPileCodeFieldValue = fieldMap.get(1395);
            //故障是否解决
            String faultSolvedFieldValue = fieldMap.get(2095);
            //故障是否解决描述
            String faultSolvedDescFieldValue = fieldMap.get(2113);
            //是否涉及原安装工程整改
            String rectificationFieldValue = fieldMap.get(2097);
            //整改米数
            String rectificationMeterFieldValue = fieldMap.get(2128);
            //其他辅材描述
            String otherMaterialDescFieldValue = fieldMap.get(2129);
            //返厂物流单号
            String expressNumberFieldValue = fieldMap.get(1065);
            RepairAfterSalesInfo repairAfterSalesInfo = RepairAfterSalesInfo.builder()
                    .orderCode(worderInformationEntity.getCompanyOrderNumber())
                    .operatePerson(OPERATE_PERSON)
                    .finishTime(finishTimeFieldValue)
                    .doorSolution(YesOrNo.getValueByName(doorSolutionFieldValue))
                    .faultCode(bydFaultCodes.getParentCode())
                    .faultReasonCode(bydFaultCodes.getCode())
                    .picAttrs(JSON.toJSONString(picAttrs))
                    .videoAttrs(videoAttrs==null? null : JSON.toJSONString(videoAttrs))
                    .exchangeMainMaterial(ExchangeMainMaterial.getValueByName(exchangeMainMaterialFieldValue))
                    .exchangeAuxiliaryMaterial(ExchangeAuxiliaryMaterial.getValueByName(exchangeAuxiliaryMaterialFieldValue))
                    .newPileCode(newPileCodeFieldValue)
                    .expressNumber(expressNumberFieldValue)
                    .faultSolved(YesOrNo.getValueByName(faultSolvedFieldValue))
                    .faultSolvedDesc(faultSolvedDescFieldValue)
                    .rectification(YesOrNo.getValueByName(rectificationFieldValue))
                    .rectificationMeter(rectificationMeterFieldValue)
                    .otherMaterialDesc(otherMaterialDescFieldValue).build();


            OtherApiResponse otherApiResponse = iBydApiService.pushRepairAccessoriesInfoNew(repairAfterSalesInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.error(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }

        } catch (Exception e) {
            log.error("服务商报修信息回传出现异常", e);
            return Result.error("服务商报修信息回传失败");
        }
        return Result.success();
    }

    //创建枚举类：是--1，否--0
    @Getter
    private enum YesOrNo {
        YES("是", "1"),
        NO("否", "0");
        private final String name;
        private final String value;
        YesOrNo(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (YesOrNo yesOrNo : YesOrNo.values()) {
                if (yesOrNo.getName().equals(name)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }

    //创建枚举类：更换主材 充电桩--11,接地极--12,漏保--13,线缆且6平方--14,线缆且10平方--15,线缆且16平方--16,无--99
    @Getter
    private enum ExchangeMainMaterial {
        EXCHANGE_MAIN_MATERIAL_CHARGING_PILE("充电桩", "11"),
        EXCHANGE_MAIN_MATERIAL_GROUNDING_POST("接地极", "12"),
        EXCHANGE_MAIN_MATERIAL_LEAKAGE_GUARD("漏保", "13"),
        EXCHANGE_MAIN_MATERIAL_LINE_CABLE_6_SQUARE("线缆且6平方", "14"),
        EXCHANGE_MAIN_MATERIAL_LINE_CABLE_10_SQUARE("线缆且10平方", "15"),
        EXCHANGE_MAIN_MATERIAL_LINE_CABLE_16_SQUARE("线缆且16平方", "16"),
        EXCHANGE_MAIN_MATERIAL_NONE("无", "99");
        private final String name;
        private final String value;
        ExchangeMainMaterial(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (ExchangeMainMaterial exchangeMainMaterial : ExchangeMainMaterial.values()) {
                if (exchangeMainMaterial.getName().equals(name)) {
                    return exchangeMainMaterial.getValue();
                }
            }
            return null;
        }
    }

    //创建枚举类：更换辅材 101-空开,102-保护箱,103-套管,104-立柱,105-吊筋,106-吊丝,107-其他,127-无
    @Getter
    private enum ExchangeAuxiliaryMaterial {
        EXCHANGE_AUXILIARY_MATERIAL_EMPTY_OPEN("空开", "101"),
        EXCHANGE_AUXILIARY_MATERIAL_PROTECTION_BOX("保护箱", "102"),
        EXCHANGE_AUXILIARY_MATERIAL_TUBE("套管", "103"),
        EXCHANGE_AUXILIARY_MATERIAL_STAND("立柱", "104"),
        EXCHANGE_AUXILIARY_MATERIAL_ROPES("吊筋", "105"),
        EXCHANGE_AUXILIARY_MATERIAL_WIRES("吊丝", "106"),
        EXCHANGE_AUXILIARY_MATERIAL_OTHER("其他", "107"),
        EXCHANGE_AUXILIARY_MATERIAL_NONE("无", "127");
        private final String name;
        private final String value;
        ExchangeAuxiliaryMaterial(String name, String value) {
            this.name = name;
            this.value = value;
        }

        public static String getValueByName(String name) {
            for (ExchangeAuxiliaryMaterial exchangeAuxiliaryMaterial : ExchangeAuxiliaryMaterial.values()) {
                if (exchangeAuxiliaryMaterial.getName().equals(name)) {
                    return exchangeAuxiliaryMaterial.getValue();
                }
            }
            return null;
        }
    }

}
