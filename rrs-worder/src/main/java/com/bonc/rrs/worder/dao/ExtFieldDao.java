package com.bonc.rrs.worder.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bonc.rrs.worder.dto.dto.ExtFieldDictionaryDto;
import com.bonc.rrs.worder.dto.vo.DicKeyResultVo;
import com.bonc.rrs.worder.entity.ExtFieldEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 扩展字段表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:58
 */
@Mapper
public interface ExtFieldDao extends BaseMapper<ExtFieldEntity> {


    IPage<ExtFieldEntity> getPageList(IPage<ExtFieldEntity> page, @Param("p") Map<String,Object> params);

    void updateExtField(ExtFieldEntity extField);


    /**
     * 字典数据
     * @param dicNumber
     * @return
     */
    List<DicKeyResultVo> getDicKeyList(String dicNumber);

    List<ExtFieldDictionaryDto> findByDicNumber(String dicNumber);
}
