package com.bonc.rrs.serviceprovider.po;

import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import lombok.Builder;
import lombok.Data;
import org.apache.http.HttpStatus;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/2/26 17:39
 */
@Data
@Builder
public class Result {

    private Integer code;

    private String msg;

    private PushApiResponse pushApiResponse;

    private OtherApiResponse otherApiResponse;

    public static Result success() {
        return Result.builder().code(0).msg("成功").build();
    }

    public static Result error() {
        return Result.builder().code(HttpStatus.SC_INTERNAL_SERVER_ERROR).msg("未知异常，请联系管理员").build();
    }

    public static Result error(String msg) {
        return Result.builder().code(HttpStatus.SC_INTERNAL_SERVER_ERROR).msg(msg).build();
    }

    public static Result error(Integer code, String msg) {
        return Result.builder().code(code).msg(msg).build();
    }

    public static Result pushApiResponse(PushApiResponse pushApiResponse) {
        return Result.builder().code(0).msg("成功").pushApiResponse(pushApiResponse).build();
    }

    public static Result pushApiResponseMessage(String message) {
        return Result.builder().code(0).msg("成功").pushApiResponse(new PushApiResponse(message)).build();
    }

    public static Result pushApiResponseSuccess() {
        return Result.builder().code(0).msg("成功").pushApiResponse(new PushApiResponse("success")).build();
    }

    public static Result otherApiResponseSuccess() {
        return Result.builder().code(0).msg("成功").otherApiResponse(OtherApiResponse.ok()).build();
    }

    public static Result otherApiResponseError(int errno, String errmsg) {
        return Result.builder().code(0).msg("成功").otherApiResponse(OtherApiResponse.error(errno, errmsg)).build();
    }

    public static Result otherApiResponseError(String errmsg) {
        return Result.builder().code(0).msg("成功").otherApiResponse(OtherApiResponse.error(500, errmsg)).build();
    }

}
