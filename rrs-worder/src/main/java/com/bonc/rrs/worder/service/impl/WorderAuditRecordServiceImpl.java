package com.bonc.rrs.worder.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.dao.*;
import com.bonc.rrs.balanceprocess.entity.*;
import com.bonc.rrs.balanceprocess.service.AdvanceMoneyInfoService;
import com.bonc.rrs.balanceprocess.service.CompanyInvoiceService;
import com.bonc.rrs.balanceprocess.service.CompanyReceivableService;
import com.bonc.rrs.balanceprocess.service.WorderChildInformationService;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.Merger;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.ReqNewAdvanceSettlement;
import com.bonc.rrs.invoice.enterprises.finance.business.newsDataAccess.req.Supplier;
import com.bonc.rrs.invoice.enterprises.finance.business.settleAuditAdd.req.FncSettleAuditDetails;
import com.bonc.rrs.invoice.enterprises.finance.business.settleAuditAdd.req.ReqSettleAuditAdd;
import com.bonc.rrs.invoice.enterprises.finance.business.settleAuditAdd.resp.RespSettleAuditAdd;
import com.bonc.rrs.invoice.enterprises.finance.business.transferApprovalForm.req.FncSubOrderList;
import com.bonc.rrs.invoice.enterprises.finance.business.transferApprovalForm.req.FncWriteOffDetailsInfo;
import com.bonc.rrs.invoice.enterprises.finance.business.transferApprovalForm.req.ReqTransferApprovalForm;
import com.bonc.rrs.invoice.enterprises.finance.business.transferApprovalForm.resp.RespTransferApprovalForm;
import com.bonc.rrs.invoice.enterprises.util.FinanceBusiness;
import com.bonc.rrs.worder.dao.WorderAuditRecordDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.dto.vo.WorderPmStimulateVo;
import com.bonc.rrs.worder.entity.CompanyInformationEntity;
import com.bonc.rrs.worder.entity.DotInformationEntity;
import com.bonc.rrs.worder.entity.WorderAuditRecordEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.service.CompanyInformationService;
import com.bonc.rrs.worder.service.WorderAuditRecordService;
import com.bonc.rrs.worderinformationaccount.dao.WorderInformationAccountDao;
import com.bonc.rrs.worderinformationaccount.dao.WorderPmStimulateDao;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.gexin.fastjson.JSON;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * created by zhangyibo at 2020:07:04 19:12
 */
@Service
@Log4j2
public class WorderAuditRecordServiceImpl extends ServiceImpl<WorderAuditRecordDao, WorderAuditRecordEntity> implements WorderAuditRecordService {
    @Autowired
    private CompanyReceivableDao companyReceivableDao;

    @Autowired(required = false)
    private WorderAuditRecordDao worderAuditRecordDao;

    @Autowired
    private WorderPmStimulateDao worderPmStimulateDao;

    @Autowired(required = false)
    private WorderInformationAccountDao worderInformationAccountDao;

    @Autowired(required = false)
    private WorderWaitAccountService worderWaitAccountService;

    @Autowired
    private WorderInformationAccountService worderInformationAccountService;

    @Autowired(required = false)
    private BalancePublishDao balancePublishDao;

    @Autowired
    private CompanyReceivableRecordDao companyReceivableRecordDao;

    @Autowired
    private CompanyReceivableCapitalpoolDao companyReceivableCapitalpoolDao;

    @Autowired
    private CompanyInvoiceService companyInvoiceService;

    @Autowired
    private WorderChildInformationService worderChildInformationService;

    @Autowired
    private CompanyInformationService companyInformationService;

    @Autowired(required = false)
    CompanyInvoiceDao companyInvoiceDao;

    @Autowired
    private FinanceBusiness financeBusiness;

    @Autowired
    private AdvanceMoneyInfoService advanceMoneyInfoService;

    @Autowired
    private WorderInformationDao worderInformationDao;

    @Autowired
    private CompanyReceivableNoticeDao companyReceivableNoticeDao;

    @Autowired
    private CompanyReceivableService companyReceivableService;

    @Override
    public List<WorderAuditRecordEntity> list(String applyNo) {
        if (StringUtils.isNotBlank(applyNo)) {
            return baseMapper.selectList(new QueryWrapper<WorderAuditRecordEntity>()
                    .eq("apply_no", applyNo).orderByDesc("create_time"));
        } else {
            return new ArrayList<>();
        }

    }

    @Override
    public boolean receivableSucessUpdateWorderInformation(String[] balanceIds) {
        //查询所有结算子工单
        List<WorderChildInformationEntity> childInformationEntityList = worderChildInformationService.getBaseMapper().selectBatchIds(Arrays.stream(balanceIds).map(Integer::parseInt).collect(Collectors.toList()));
        String[] worderIdArr = new String[childInformationEntityList.size()];
        for (int i = 0; i < worderIdArr.length; i++) {
            worderIdArr[i] = childInformationEntityList.get(i).getWorderId().toString();
        }
        worderInformationAccountDao.update(null,
                new UpdateWrapper<WorderInformationAccountEntity>()
                        .in("worder_id", worderIdArr)
                        .set("worder_set_status", 4)
                        .set("worder_set_status_value", "车企已回款"));
        return worderChildInformationService.updateWorderChildStatus(childInformationEntityList,4,"车企已回款",4,"激励已结算");
    }

    @Override
    public boolean publishSucessUpdateWorderInformation(String[] worderIdStrs) {
        //更改工单状态

        worderInformationAccountDao.update(null,
                new UpdateWrapper<WorderInformationAccountEntity>()
                        .in("worder_no", worderIdStrs)
                        .set("worder_set_status", 5)
                        .set("worder_set_status_value", "网点结算已发布"));

        return true;
    }

    @Override
    public Integer updateCompanyInvoice(Map params) {
        return baseMapper.updateCompanyInvoice(params);
    }

    @Override
    public Integer updateCompanyReceive(Map params) {
        return baseMapper.updateCompanyReceive(params);
    }

    @Override
    public R transferApprovalFormByCompanyReceivableNo(String companyReceivableNo) {
        CompanyReceivableEntity companyReceivableEntity = companyReceivableDao.selectOne(new QueryWrapper<CompanyReceivableEntity>().eq("company_receivable_no", companyReceivableNo));
        // 判断核销金额为0的为回款审核单 不用传给财务中心
        if (companyReceivableEntity.getCavFee().compareTo(BigDecimal.ZERO) == 0) {
            return R.ok();
        }
        if (companyReceivableEntity.getType()==0){
            RespTransferApprovalForm respTransferApprovalForm = transferApprovalForm(companyReceivableEntity);
            // 审核不通过 返回原因
            if (!FinanceBusiness.getSUCCESS().equals(respTransferApprovalForm.getCode())) {
                return R.error(respTransferApprovalForm.getMsg());
            }
        }
        return R.ok();
    }

    private List<Integer> getListBySplit(String ids, String separetor) {
        List<Integer> idList = new ArrayList<>();
        if (StringUtils.isBlank(ids)) {
            return idList;
        }
        String[] idArr = ids.split(separetor);

        for (String str : idArr) {
            idList.add(Integer.valueOf(str));
        }
        return idList;
    }

    @Override
    public RespTransferApprovalForm transferApprovalForm(CompanyReceivableEntity companyReceivableEntity) {
        String voucherNos = companyReceivableEntity.getVoucherNo();
        String[] voucherNoArr = voucherNos.split(",");
        RespTransferApprovalForm respTransferApprovalForm = new RespTransferApprovalForm();
        String balanceIds = companyReceivableEntity.getBalanceIds();
        List<Integer> childIds = getListBySplit(balanceIds, ",");
        CompanyInformationEntity companyInformationEntity = companyInformationService.getByCompanyId(companyReceivableEntity.getCompanyId());
        List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationService.selectChildWorderCompanyFeeByWorderChildIds(childIds);
        ReqTransferApprovalForm reqTransferApprovalForm = new ReqTransferApprovalForm();
        //回款\核销单号
        reqTransferApprovalForm.setWriteOffNo(companyReceivableEntity.getCompanyReceivableNo());
        // 回款说明  （核销）
        reqTransferApprovalForm.setWriteOffRemark("核销");
        // 查询开票单
        List<CompanyInvoiceEntity> companyInvoiceEntityList = companyReceivableDao.queryCompanyInvoiceByCompanyReceivableNo(companyReceivableEntity.getCompanyReceivableNo());
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceEntityList.get(0);
        // 合并单号
        reqTransferApprovalForm.setOrderNo(companyInvoiceEntity.getCompanyInvoiceNo());

        BigDecimal writeOffAmount = companyReceivableDao.queryCompanyInvoiceSurplusCavFeeHis(companyInvoiceEntity.getId(),companyReceivableEntity.getCompanyReceivableNo());

        BigDecimal writeOffAmountAlready = companyReceivableDao.queryCompanyInvoiceSurplusCavFeeHis(companyInvoiceEntity.getId(),companyReceivableEntity.getCompanyReceivableNo());
        //已核销金额
        reqTransferApprovalForm.setWriteOffAmountAlready(writeOffAmount!=null ? writeOffAmount.toString() : "0.0");
        //本次核销金额
        reqTransferApprovalForm.setWriteOffAmount(writeOffAmountAlready.toString());
        // 合并单本次回款金额（核销金额）
        reqTransferApprovalForm.setOrderAmount(companyInvoiceEntity.getInvoiceFee().toString());
        // 校验是否是最后一笔核销
        Boolean isLastCav = isLastCav(companyReceivableEntity.getCompanyReceivableNo());
        // 合并单回款完成标记 0-未完成，1-回款完成
        String orderCollectionFlag = isLastCav ? "1" : "0";
        // 合并单回款完成标记
        reqTransferApprovalForm.setOrderCollectionFlag(orderCollectionFlag);
        CompanyInformationEntity company = companyInformationService.getByCompanyId(companyInvoiceEntity.getCompanyId());
        // 公司代码
        reqTransferApprovalForm.setCompanyCode("0RK0");
        // 车企名称
        reqTransferApprovalForm.setCustomerName(company.getCompanyName());
        // 车企88码
        reqTransferApprovalForm.setCustomer88code(company.getCompanyCode());
        BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(companyInvoiceEntity.getId());
        // 品牌编码
        reqTransferApprovalForm.setBrandCode(brand.getId().toString());
        // 品牌名称
        reqTransferApprovalForm.setBrandName(brand.getBrandName());
        BigDecimal thisTimeWriteOffCosts = new BigDecimal(0);
        BigDecimal advanceAlreadyRelease = new BigDecimal(0);
        for (WorderChildInformationEntity worderChildInformationEntity : worderChildInformationEntityList) {
            thisTimeWriteOffCosts = thisTimeWriteOffCosts.add(worderChildInformationEntity.getDotBalanceFeeSum());
            if (worderChildInformationEntity.getType() == 1 && worderChildInformationEntity.getPublishId() != null){
                advanceAlreadyRelease = advanceAlreadyRelease.add(worderChildInformationEntity.getDotBalanceFeeSum());
            }
        }
        BigDecimal thisTimeAmountAvailable = thisTimeWriteOffCosts.subtract(advanceAlreadyRelease);
        //核销成本， 取核销工单dot_balance_fee
        reqTransferApprovalForm.setThisTimeWriteOffCosts(thisTimeWriteOffCosts.toString());
        //本次非新资金可发布金额
        reqTransferApprovalForm.setThisTimeAmountAvailable(thisTimeAmountAvailable.toString());
        //新资金已发布金额
        reqTransferApprovalForm.setAdvanceAlreadyRelease(advanceAlreadyRelease.toString());
        List<FncWriteOffDetailsInfo> fncWriteOffDetailsInfoList = new ArrayList<>();
        if (voucherNoArr != null && voucherNoArr.length > 0) {
            for (String voucherNo : voucherNoArr) {
                List<CompanyReceivableRecordEtity> recordEtityList = companyReceivableRecordDao.selectList(
                        new QueryWrapper<CompanyReceivableRecordEtity>().eq("delete_state","0").eq("type","0")
                                .eq("voucher_no",voucherNo));
                BigDecimal receivableFee = new BigDecimal(0);
                BigDecimal receivableRealFee = new BigDecimal(0);
                for (CompanyReceivableRecordEtity companyReceivableRecordEtity : recordEtityList) {
                    receivableFee = receivableFee.add(companyReceivableRecordEtity.getReceivableFee());
                    receivableRealFee = receivableRealFee.add(companyReceivableRecordEtity.getReceivableRealFee());
                }
                CompanyReceivableRecordEtity receivableRecordEtity = companyReceivableRecordDao.selectOne(
                        new QueryWrapper<CompanyReceivableRecordEtity>().eq("voucher_no",voucherNo).eq("company_receivable_no",companyReceivableEntity.getCompanyReceivableNo()).eq("type","1"));
                BigDecimal cavFee = companyReceivableDao.querySumCavFee(voucherNo);
                cavFee = cavFee.subtract(receivableRecordEtity.getCavFee());
                FncWriteOffDetailsInfo fncWriteOffDetailsInfo = new FncWriteOffDetailsInfo();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                CompanyReceivableNoticeEntity companyReceivableNoticeEntity = companyReceivableNoticeDao.queryByVoucherNoEightCode(voucherNo,companyInformationEntity.getCompanyCode());
                fncWriteOffDetailsInfo.setAccountCode(voucherNo);
                fncWriteOffDetailsInfo.setAccountDate(sdf.format(companyReceivableNoticeEntity.getReceivableTime()));
                fncWriteOffDetailsInfo.setAccountAmount(companyReceivableNoticeEntity.getReceivablePrice());
                BigDecimal changeAmount = receivableFee.subtract(receivableRealFee);
                fncWriteOffDetailsInfo.setChangeAmount(changeAmount.toString());
                fncWriteOffDetailsInfo.setWriteOffAmountAlready(cavFee.toString());
                fncWriteOffDetailsInfo.setWriteOffAmount(receivableRecordEtity.getCavFee().toString());
                fncWriteOffDetailsInfoList.add(fncWriteOffDetailsInfo);
            }
        }
        List<FncSubOrderList> fncSubOrderList = new ArrayList<>();
        if (worderChildInformationEntityList != null && worderChildInformationEntityList.size() > 0){
            worderChildInformationEntityList.forEach(worderChildInformationEntity -> {
                WorderInfoEntity worderInfoEntity = worderInformationDao.getByWorderId(worderChildInformationEntity.getWorderId());
                DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByWorderId(worderChildInformationEntity.getWorderId());
                FncSubOrderList fncSubOrder = new FncSubOrderList();
                fncSubOrder.setSubOrderNo(worderChildInformationEntity.getBalanceNo());
                fncSubOrder.setWorkOrderNo(worderInfoEntity.getWorderNo());
                fncSubOrder.setSupplierName(dotInformationEntity.getDotName());
                fncSubOrder.setSupplierVcode(dotInformationEntity.getVCode());
                fncSubOrder.setCostAmountTaxExcluded(worderChildInformationEntity.getDotBalanceFee() == null ? "" : worderChildInformationEntity.getDotBalanceFee().toString());
                fncSubOrder.setCostAmountTaxIncluded(worderChildInformationEntity.getDotBalanceFeeSum() == null ? "" : worderChildInformationEntity.getDotBalanceFeeSum().toString());
                fncSubOrder.setOrderAmountTaxExcluded(worderChildInformationEntity.getCompanyBalanceFee()== null ? "" : worderChildInformationEntity.getCompanyBalanceFee().toString());
                fncSubOrder.setOrderAmountTaxIncluded(worderChildInformationEntity.getCompanyBalanceFeeSum()== null ? "" : worderChildInformationEntity.getCompanyBalanceFeeSum().toString());
                fncSubOrderList.add(fncSubOrder);
            });
        }
        reqTransferApprovalForm.setFncWriteOffAccountDetails(fncWriteOffDetailsInfoList);
        reqTransferApprovalForm.setFncWriteOffSubOrderDetails(fncSubOrderList);
        JSONObject respJson = financeBusiness.transferApprovalForm(reqTransferApprovalForm);
        if (respJson != null) {
            respTransferApprovalForm = respJson.toJavaObject(RespTransferApprovalForm.class);
            // 有调用失败就返回
            if (!FinanceBusiness.getSUCCESS().equals(respTransferApprovalForm.getCode())) {
                respTransferApprovalForm.setCode("500");
                respTransferApprovalForm.setMsg("调用财务中台传输回款审核单1216接口失败");
            }
        } else {
            respTransferApprovalForm.setCode("500");
            respTransferApprovalForm.setMsg("调用财务中台传输回款审核单1216接口失败");
        }
        return respTransferApprovalForm;
    }

    /**
     * 校验是否最后一次核销
     * @param companyReceivableNo
     * @return
     */
    public Boolean isLastCav(String companyReceivableNo) {
        //核销工单和激励单总金额
        BigDecimal cavTotalFee = new BigDecimal(0);
        //查询该回款单号的核销记录
        List<CompanyReceivableRecordEtity> companyReceivableRecordEtities = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no", companyReceivableNo).eq("type", 1).eq("delete_state", 0));
        //获取开票单ID
        Integer invoiceId = companyReceivableRecordEtities.get(0).getInvoiceId();

        List<CompanyReceivableRecordEtity> companyReceivableRecordList = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("invoice_id", invoiceId).eq("type", 1).eq("delete_state", 0));
        Map<String, Object> params = new HashMap<>(16);
        params.put("invoiceId", invoiceId);
        //获取开票单下所有工单总金额
        BigDecimal worderCompanyTotalFee = companyReceivableDao.queryWorderSumFeeByInvoiceId(params);
        //获取开票单下所有激励单总金额
//        BigDecimal stimulateCompanyTotalFee = companyReceivableDao.queryStimulateSumFeeByInvoiceId(params);
        BigDecimal totalFee = (worderCompanyTotalFee != null ? worderCompanyTotalFee : BigDecimal.ZERO);
        //获取已核销的总金额
        for (CompanyReceivableRecordEtity companyReceivableRecordEtity : companyReceivableRecordList) {
            if(companyReceivableRecordEtity.getCavFee() != null){
                cavTotalFee = cavTotalFee.add(companyReceivableRecordEtity.getCavFee());
            }
        }

        return cavTotalFee.compareTo(totalFee) >= 0;
    }

    @Override
    public Integer updateBalancePublish(Map params) {
        return baseMapper.updateBalancePublish(params);
    }

    @Override
    public R updateAdvanceBalancePublish(Map params) {
        String batchNo = (String) params.get("batchNo");
        List<BalancePublishEntity> balancePublishEntityList = balancePublishDao.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no", batchNo));

        if (balancePublishEntityList == null || balancePublishEntityList.size() == 0) {
            return R.error("无效发布单批次号");
        }
        RespSettleAuditAdd respSettleAuditAdd = pushSettleAuditAdd(balancePublishEntityList);
        if (respSettleAuditAdd != null && FinanceBusiness.getSUCCESS().equals(respSettleAuditAdd.getCode())) {
            baseMapper.updateBatchBalancePublish(params);
        } else if (respSettleAuditAdd != null && !FinanceBusiness.getSUCCESS().equals(respSettleAuditAdd.getCode())) {
            return R.error("结算审核单传输接口失败:" + respSettleAuditAdd.getMsg());
        } else {
            return R.error("结算审核单传输接口失败");
        }

        return R.ok();
    }

    private BigDecimal getDotBalanceFeeByWorderChildInfoList(List<WorderChildInformationEntity> worderChildInformationList) {
        BigDecimal dotAdvanceAmount = BigDecimal.ZERO;

        if (worderChildInformationList != null) {
            for (WorderChildInformationEntity worderChildInformation : worderChildInformationList) {
                dotAdvanceAmount = dotAdvanceAmount.add(worderChildInformation.getDotBalanceFeeSum());
            }
        }
        return dotAdvanceAmount;
    }

    private RespSettleAuditAdd pushSettleAuditAdd(List<BalancePublishEntity> balancePublishEntityList) {

        RespSettleAuditAdd resp = new RespSettleAuditAdd();
        String settleNo = balancePublishEntityList.get(0).getBatchNo();
        BigDecimal advanceAmount = BigDecimal.ZERO;
        BigDecimal settleAmountTaxIncluded = BigDecimal.ZERO;

        List<CompanyInvoiceEntity> companyInvoiceEntityList = balancePublishDao.selectCompanyInvoiceByBatchNo(settleNo);

        if (companyInvoiceEntityList == null || companyInvoiceEntityList.size() <= 0) {
            resp.setCode("500");
            resp.setMsg("未找到有效开票单");
            return resp;
        }

        for (BalancePublishEntity balancePublishEntity : balancePublishEntityList) {
            settleAmountTaxIncluded = settleAmountTaxIncluded.add(balancePublishEntity.getPublishFee());
        }

        BigDecimal settleAmountTaxExcluded = balancePublishDao.selectSettleAmountTaxExcludedByBatchNo(settleNo);

        ReqNewAdvanceSettlement reqNewAdvanceSettlement = new ReqNewAdvanceSettlement();

        reqNewAdvanceSettlement.setSettleNo(settleNo);
        reqNewAdvanceSettlement.setCompanyCode("0KR0");

        AdvanceMoneyInfoEntity advanceMoneyInfoEntity = advanceMoneyInfoService.getAdvanceMoneyInfo();
        reqNewAdvanceSettlement.setAdvanceNum(companyInvoiceEntityList.size());
        reqNewAdvanceSettlement.setSettleAmountTaxIncluded(settleAmountTaxIncluded.toString());
        reqNewAdvanceSettlement.setSettleAmountTaxExcluded(settleAmountTaxExcluded.toString());

        BigDecimal totalAmount = settleAmountTaxIncluded.add(advanceMoneyInfoEntity.getPublishLimit());
        reqNewAdvanceSettlement.setTotalAmount(totalAmount.toString());//表内已使用垫资池金额+本次提交金额

        reqNewAdvanceSettlement.setApplyRemark("news发布审核传输");

        List<Merger> mergerList = new ArrayList<>();

        for (CompanyInvoiceEntity invoiceEntity : companyInvoiceEntityList) {
            CompanyInformationEntity company = companyInformationService.getByCompanyId(invoiceEntity.getCompanyId());

            CompanyReceivableEntity companyReceivableEntity = balancePublishDao.selectCompanyReceivableByInvoiceId(invoiceEntity.getId());

            BigDecimal orderCostAmount = balancePublishDao.selectOrderCostAmountByInvoiceId(invoiceEntity.getId());

            BigDecimal orderPayAmount = balancePublishDao.selectOrderPayAmountByInvoiceId(invoiceEntity.getId());

            BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(invoiceEntity.getId());

            Merger merger = new Merger();

            merger.setSettleNo(settleNo);
            merger.setOrderNo(invoiceEntity.getCompanyInvoiceNo());

            merger.setOrderNum(invoiceEntity.getCount()+1);

            CompanyInformationEntity companyInformationEntity = companyInformationService.getByCompanyId(invoiceEntity.getCompanyId());
            merger.setCustomerName(companyInformationEntity.getCompanyName());
            merger.setCustomer88Code(companyInformationEntity.getCompanyCode());

            merger.setBrandName(brand.getBrandName());
            merger.setBrandCode(brand.getId().toString());

            merger.setOrderAmount(orderPayAmount);
            merger.setOrderCostAmount(orderCostAmount);

            BigDecimal paidAmount = worderChildInformationService.getPaidAmount(invoiceEntity.getId());

            List<WorderChildInformationEntity> worderChildInformationList = balancePublishDao.selectCompanyWorderChildInfoByPublishId(invoiceEntity.getId(),settleNo);
            advanceAmount = getDotBalanceFeeByWorderChildInfoList(worderChildInformationList);
            paidAmount = paidAmount.subtract(advanceAmount);
            merger.setPaidAmount(paidAmount);
            merger.setAdvanceAmount(advanceAmount);
            merger.setToPaidAmount(orderCostAmount.subtract(paidAmount).subtract(advanceAmount));

            mergerList.add(merger);
        }
        reqNewAdvanceSettlement.setMergerList(mergerList);

        List<Supplier> supplierList = new ArrayList<>();

        for (BalancePublishEntity balancePublishEntity : balancePublishEntityList) {
            Supplier supplier = new Supplier();
            supplier.setSettleNo(settleNo);

            DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByDotId(balancePublishEntity.getDotId());
            supplier.setSupplierVcode(dotInformationEntity.getVCode());
            supplier.setSupplierName(dotInformationEntity.getDotName());

            BigDecimal subSettleAmountTaxExcluded = balancePublishDao.selectSettleAmountTaxExcludedByPublishId(balancePublishEntity.getId());
            supplier.setSettleAmountTaxExcluded(subSettleAmountTaxExcluded);
            supplier.setSettleAmountTaxIncluded(balancePublishEntity.getPublishFee());

            BigDecimal taxRate = companyInvoiceDao.queryDotTaxPointByDotId(balancePublishEntity.getDotId());
            supplier.setTaxRate(taxRate);
            supplierList.add(supplier);
        }
        reqNewAdvanceSettlement.setSupplierList(supplierList);
        JSONObject respJson = financeBusiness.newAdvanceSettlement(reqNewAdvanceSettlement);
        if (respJson != null) {
            resp = respJson.toJavaObject(RespSettleAuditAdd.class);
            if (FinanceBusiness.getSUCCESS().equals(resp.getCode())){
                for (CompanyInvoiceEntity invoiceEntity : companyInvoiceEntityList) {
                    //审核次数+1
                    Integer count = invoiceEntity.getCount()+1;
                    companyInvoiceDao.update(null,new UpdateWrapper<CompanyInvoiceEntity>().set("count",count).eq("id",invoiceEntity.getId()));
                }
            }
        } else {
            resp.setCode("500");
            resp.setMsg("调用财务中台新垫资结算审核单传输接口失败");
            for (CompanyInvoiceEntity invoiceEntity : companyInvoiceEntityList) {
                //审核次数+1
                Integer count = invoiceEntity.getCount()+1;
                companyInvoiceDao.update(null,new UpdateWrapper<CompanyInvoiceEntity>().set("count",count).eq("id",invoiceEntity.getId()));
            }
        }
        return resp;
//        RespSettleAuditAdd resp = new RespSettleAuditAdd();
//        String settleNo = balancePublishEntityList.get(0).getBatchNo();
//        BigDecimal advanceAmount = BigDecimal.ZERO;
//        BigDecimal settleAmountTaxIncluded = BigDecimal.ZERO;
//
//        List<CompanyInvoiceEntity> companyInvoiceEntityList = balancePublishDao.selectCompanyInvoiceByBatchNo(settleNo);
//
//        if (companyInvoiceEntityList == null || companyInvoiceEntityList.size() <= 0) {
//            resp.setCode("500");
//            resp.setMsg("未找到有效开票单");
//            return resp;
//        }
//        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceEntityList.get(0);
//
//        BigDecimal dotAdvanceAmount = BigDecimal.ZERO;
//        for (BalancePublishEntity balancePublishEntity : balancePublishEntityList) {
//            settleAmountTaxIncluded = settleAmountTaxIncluded.add(balancePublishEntity.getPublishFee());
//        }
//
//        List<WorderChildInformationEntity> worderChildInformationList = balancePublishDao.selectCompanyWorderChildInfoByPublishId(companyInvoiceEntity.getId());
//        advanceAmount = getDotBalanceFeeByWorderChildInfoList(worderChildInformationList);
//
//        BrandEntity brand = companyInvoiceDao.queryBrandByInvoiceId(companyInvoiceEntity.getId());
//        CompanyInformationEntity company = companyInformationService.getByCompanyId(companyInvoiceEntity.getCompanyId());
//
//        CompanyReceivableEntity companyReceivableEntity = balancePublishDao.selectCompanyReceivableByInvoiceId(companyInvoiceEntity.getId());
//
//        BigDecimal orderCostAmount = balancePublishDao.selectOrderCostAmountByInvoiceId(companyInvoiceEntity.getId());
//
//        BigDecimal settleAmountTaxExcluded = balancePublishDao.selectSettleAmountTaxExcludedByBatchNo(settleNo);
//
//        ReqSettleAuditAdd reqSettleAuditAdd = new ReqSettleAuditAdd();
//
//        reqSettleAuditAdd.setSettleNo(settleNo);
//        reqSettleAuditAdd.setOrderNo(companyInvoiceEntity.getCompanyInvoiceNo());
//        reqSettleAuditAdd.setAdvanceNo(companyReceivableEntity.getCompanyReceivableNo());
//
//        reqSettleAuditAdd.setCompanyCode("0KR0");
//
//        reqSettleAuditAdd.setBrandCode(brand.getId().toString());
//        reqSettleAuditAdd.setBrandName(brand.getBrandName());
//
//        reqSettleAuditAdd.setCustomer88code(company.getCompanyCode());
//        reqSettleAuditAdd.setCustomerName(company.getCompanyName());
//
//        reqSettleAuditAdd.setOrderAmount(companyInvoiceEntity.getInvoiceFee().toString());
//
//        reqSettleAuditAdd.setAdvanceAmount(advanceAmount.toString());
////        reqSettleAuditAdd.setAdvanceAmount(companyInvoiceEntity.getInvoiceFee().toString());
//
//        reqSettleAuditAdd.setOrderCostAmount(orderCostAmount.toString());
//        reqSettleAuditAdd.setSettleAmountTaxIncluded(settleAmountTaxIncluded.toString());
//        reqSettleAuditAdd.setSettleAmountTaxExcluded(settleAmountTaxExcluded.toString());
//
//        reqSettleAuditAdd.setApplyRemark("news发布审核传输");
//
//        List<FncSettleAuditDetails> fncSettleAuditDetails = new ArrayList<>();
//
//        for (BalancePublishEntity balancePublishEntity : balancePublishEntityList) {
//
//            FncSettleAuditDetails fnc = new FncSettleAuditDetails();
//
//            fnc.setSettleNo(settleNo);
//            fnc.setBillNo(balancePublishEntity.getBalancePublishNo());
//
//            DotInformationEntity dotInformationEntity = companyInvoiceDao.queryDotByDotId(balancePublishEntity.getDotId());
//
//            fnc.setSupplierVcode(dotInformationEntity.getVCode());
//            fnc.setSupplierName(dotInformationEntity.getDotName());
//
//            BigDecimal subSettleAmountTaxExcluded = balancePublishDao.selectSettleAmountTaxExcludedByPublishId(balancePublishEntity.getId());
//
//            fnc.setSettleAmountTaxExcluded(subSettleAmountTaxExcluded.toString());
//            fnc.setSettleAmountTaxIncluded(balancePublishEntity.getPublishFee().toString());
//
//            BigDecimal taxRate = companyInvoiceDao.queryDotTaxPointByDotId(balancePublishEntity.getDotId());
//
//            fnc.setTaxRate(taxRate.toString());
//
//            fncSettleAuditDetails.add(fnc);
//        }
//        reqSettleAuditAdd.setFncSettleAuditDetails(fncSettleAuditDetails);
//
//        JSONObject respJson = financeBusiness.settleAuditAdd(reqSettleAuditAdd);
//        if (respJson != null) {
//            resp = respJson.toJavaObject(RespSettleAuditAdd.class);
//        } else {
//            resp.setCode("500");
//            resp.setMsg("调用财务中台结算审核单传输接口失败");
//        }
//        return resp;
    }

    @Override
    public LinkedList<Map<String, Object>> getStatus(JSONObject applyNo) {
        LinkedList<Map<String, Object>> status = new LinkedList<>();
        Map<String, Object> statusReason = worderAuditRecordDao.getForStatus((String) applyNo.get("applyNo"), (Integer) applyNo.get("auditType"), null);
        if (statusReason == null) {
            return status;
        } else if ((Integer) statusReason.get("auditStatus") == 1 || (Integer) statusReason.get("auditStatus") == -1) {
            status.add(statusReason);
            return status;
        } else if ((Integer) statusReason.get("auditStatus") == 2 || (Integer) statusReason.get("auditStatus") == -2) {
            Map<String, Object> statusReason1 = worderAuditRecordDao.getForStatus((String) applyNo.get("applyNo"), (Integer) applyNo.get("auditType"), 1);
            status.add(statusReason1);
            status.add(statusReason);
            return status;
        }
        return null;
    }

    @Override
    public R batchCheck(JSONObject params) {
        //获取登录用户
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        //是否审核通过
        int auditStatus = params.getIntValue("auditStatus");
        int auditType = params.getIntValue("auditType");
        int status = params.getIntValue("status");
        String noPassReason = params.getString("noPassReason");
        //获取激励列表id
        JSONArray applyNos = params.getJSONArray("applyNos");
        for (int i = 0, s = applyNos.size(); i < s; i++) {
            Object object = applyNos.get(i);

            WorderAuditRecordEntity record = new WorderAuditRecordEntity();
            //查询
            WorderPmStimulateEntity worderPmStimulateEntity = worderPmStimulateDao.selectByStimulateId((Integer) object);
            Integer oldStatus = worderPmStimulateEntity.getStatus();

            WorderPmStimulateVo stimulateInfo = baseMapper.getStimulateInfo(object.toString());

            //保存激励原因
            Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>() {
                {
                    put("100", baseMapper.getStimulateDotReason());
                    put("110", baseMapper.getStimulateReason());
                    put("101", baseMapper.getStimulateDotNegativeReason());
                    put("111", baseMapper.getStimulateNegativeReason());
                }
            };
            Optional<Map<String, Object>> reason = map.get(stimulateInfo.getStimulateType() + "" + stimulateInfo.getIncentiveType()) == null || map.get(stimulateInfo.getStimulateType() + "" + stimulateInfo.getIncentiveType()).size() == 0 ? null : map.get(stimulateInfo.getStimulateType() + "" + stimulateInfo.getIncentiveType()).stream()
                    .filter(item -> Integer.valueOf(item.get("stimulateReason").toString()).equals(stimulateInfo.getStimulateReason()))
                    .findFirst();
            worderPmStimulateEntity.setStimulateReasonValue(String.valueOf(reason.isPresent() ? reason.get().get("stimulateReasonName") : ""));

            WorderInformationAccountEntity worderInformation = worderInformationAccountDao.selectById(worderPmStimulateEntity.getWorderId());

            if (worderInformation == null) {
                return R.error("激励单:" + worderPmStimulateEntity.getId() + "的工单已删除,无法审核通过！");
            }
            if (worderInformation != null) {
                if (worderInformation.getWorderExecStatus() == 21) {
                    return R.error("激励单:" + worderPmStimulateEntity.getId() + "的工单已取消,无法审核通过！");
                }
            }

            record.setApplyNo(object.toString());
            record.setAuditUserId(user.getUserId().intValue());
            record.setCreateTime(new Date());
            record.setAuditStatus(auditStatus);
            record.setAuditType(auditType);
            record.setNoPassReason(noPassReason);
            record.setWorderNos(worderInformation.getWorderNo());
            worderAuditRecordDao.insert(record);

            worderPmStimulateEntity.setStatus(status);
            worderPmStimulateDao.updateById(worderPmStimulateEntity);

            // 激励二次审核通过
            if (status == 18) {
                // 拆分结算子订单
                WorderChildInformationEntity worderChildInformationEntity = worderChildInformationService.splitBalanceStimulate(worderPmStimulateEntity);

                if (IntegerEnum.TWO.getValue() == auditStatus
                        && IntegerEnum.FOUR.getValue() == auditType
                        && 11 == worderPmStimulateEntity.getStimulateType()) {
                    // 加入待工单结算-车企待结算
                    worderWaitAccountService.addStimulateWaitAccount(worderPmStimulateEntity.getWorderId(), worderPmStimulateEntity.getId());
                } else if (IntegerEnum.TWO.getValue() == auditStatus
                        && IntegerEnum.FOUR.getValue() == auditType
                        && IntegerEnum.TEN.getValue().equals(worderPmStimulateEntity.getStimulateType())) {
                    // 网点激励 推送acs记账
                    //List<WorderPmStimulateEntity> stimulateList = new ArrayList<>();
                    //stimulateList.add(worderPmStimulateEntity);
                    //List<WorderChildInformationEntity> worderChildInformationList = new ArrayList<>();
                    //worderChildInformationList.add(worderChildInformationEntity);
                    //worderInformationAccountService.pushAcsAccountStimulate(worderChildInformationList, stimulateList);

                    // 网点激励 推送acs记账
                    List<WorderPmStimulateEntity> stimulateList = new ArrayList<>();
                    stimulateList.add(worderPmStimulateEntity);
                    List<WorderChildInformationEntity> worderChildInformationList = new ArrayList<>();
                    worderChildInformationList.add(worderChildInformationEntity);
                    // TODO 添加校验逻辑,防止重复推送
                    if (oldStatus != null && oldStatus == 18) {
                        log.error("激励二次审核已推送ACS记账,请勿重复推送！");
                    } else {
                        worderInformationAccountService.pushFinanceAccountStimulate(worderChildInformationList, stimulateList);
                    }
                }
            }
        }
//        applyNos.forEach(object -> {
//            WorderAuditRecordEntity record = new WorderAuditRecordEntity();
//            //查询
//            WorderPmStimulateEntity worderPmStimulateEntity = worderPmStimulateDao.selectById((Integer) object);
//
//            WorderInformationAccountEntity worderInformation = worderInformationAccountDao.selectById(worderPmStimulateEntity.getWorderId());
//
//            if (worderInformation == null) {
//                return R.error("工单已删除,无法审核通过！");
//            }
//            if (worderInformation != null) {
//                if (worderInformation.getWorderExecStatus() == 21) {
//                    return R.error("工单已取消,无法审核通过！");
//                }
//            }
//
//            record.setApplyNo(object.toString());
//            record.setAuditUserId(user.getUserId().intValue());
//            record.setCreateTime(DateUtils.getCurrentTime());
//            record.setAuditStatus(auditStatus);
//            record.setAuditType(auditType);
//            record.setNoPassReason(noPassReason);
//            record.setWorderNos(worderInformation.getWorderNo());
//            worderAuditRecordDao.insert(record);
//
//            worderPmStimulateEntity.setStatus(status);
//            worderPmStimulateDao.updateById(worderPmStimulateEntity);
//
//            // 激励二次审核通过
//            if (status == 18) {
//                // 拆分结算子订单
//                WorderChildInformationEntity worderChildInformationEntity = worderChildInformationService.splitBalanceStimulate(worderPmStimulateEntity);
//
//                if (IntegerEnum.TWO.getValue() == auditStatus
//                        && IntegerEnum.FOUR.getValue() == auditType
//                        && 11 == worderPmStimulateEntity.getStimulateType()) {
//                    // 加入待工单结算-车企待结算
//                    worderWaitAccountService.addStimulateWaitAccount(worderPmStimulateEntity.getWorderId(), worderPmStimulateEntity.getId());
//                } else if (IntegerEnum.TWO.getValue() == auditStatus
//                        && IntegerEnum.FOUR.getValue() == auditType
//                        && IntegerEnum.TEN.getValue().equals(worderPmStimulateEntity.getStimulateType())) {
//                    // 网点激励 推送acs记账
//                    List<WorderPmStimulateEntity> stimulateList = new ArrayList<>();
//                    stimulateList.add(worderPmStimulateEntity);
//                    List<WorderChildInformationEntity> worderChildInformationList = new ArrayList<>();
//                    worderChildInformationList.add(worderChildInformationEntity);
//                    worderInformationAccountService.pushAcsAccountStimulate(worderChildInformationList, stimulateList);
//                }
//            }
//        });
        return R.ok();
    }


    @Override
    public LinkedList<Map<String, Object>> getBatchStatus(JSONObject batchNo) {
        LinkedList<Map<String, Object>> status = new LinkedList<>();
        List<BalancePublishEntity> balancePublishEntities = balancePublishDao.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no", batchNo.get("batchNo")));
        Map<String, Object> statusReason = worderAuditRecordDao.getForStatus(balancePublishEntities.get(0).getBalancePublishNo(), (Integer) batchNo.get("auditType"), null);
        if (statusReason == null) {
            return status;
        } else if ((Integer) statusReason.get("auditStatus") == 1 || (Integer) statusReason.get("auditStatus") == -1) {
            status.add(statusReason);
            return status;
        } else if ((Integer) statusReason.get("auditStatus") == 2 || (Integer) statusReason.get("auditStatus") == -2) {
            Map<String, Object> statusReason1 = worderAuditRecordDao.getForStatus((String) batchNo.get("applyNo"), (Integer) batchNo.get("auditType"), 1);
            status.add(statusReason1);
            status.add(statusReason);
            return status;
        }
        return null;
    }

    @Override
    public void saveBatchAuditRecord(WorderAuditRecordEntity record) {
        //获取批次号
        String batchNo = record.getBatchNo();
        if (StringUtils.isNotBlank(batchNo)) {
            //根据批次号查询发布单号
            List<BalancePublishEntity> balancePublishs = balancePublishDao.selectList(new QueryWrapper<BalancePublishEntity>().eq("batch_no", batchNo));
            balancePublishs.forEach(balancePublishEntity -> {
                record.setApplyNo(balancePublishEntity.getBalancePublishNo());
                this.baseMapper.insert(record);
            });
        } else {
            this.baseMapper.insert(record);
        }
    }

    @Override
    public void updateBatchBalancePublish(Map params) {
        baseMapper.updateBatchBalancePublish(params);
        Integer status = (Integer) params.get("status");
        // 33审核中状态
        if(StringUtils.isNotBlank(params.get("batchNo").toString()) && StringUtils.isNotBlank(params.get("status").toString()) && status != 13){
            //根据批次号查询是否有激励Id
            QueryWrapper<BalancePublishEntity> objectQueryWrapper = new QueryWrapper<>();
            objectQueryWrapper.in("batch_no",params.get("batchNo"));
            List<BalancePublishEntity> balancePublishEntities = balancePublishDao.selectList(objectQueryWrapper);
            //通过激励Id修改激励发布状态
            if(balancePublishEntities.size()>0){
                for (BalancePublishEntity balancePublishEntity:balancePublishEntities){
                    if(StringUtils.isNotBlank(balancePublishEntity.getStimulateIds())){
                        String[] as = balancePublishEntity.getStimulateIds().split(",");
                        for (String a:as){
                            worderPmStimulateDao.updatePmStatus(Integer.valueOf(a),(Integer) params.get("status"));
                        }
                    }
                }
            }

        }
    }

    /**
     * 平账审核完成更新核销状态
     * @param companyInvoiceEntity
     * @param balanceIds
     */
    @Override
    public void updateBalanceAdvanceMoneyCompanyInvoiceCavState(CompanyInvoiceEntity companyInvoiceEntity, String[] balanceIds,CompanyReceivableEntity companyReceivable) {
        //如果字只做回款不更新
        if (balanceIds == null || balanceIds.length <= 0) {
            return;
        }
        //核销工单和激励单总金额
//        BigDecimal cavTotalFee = new BigDecimal(0);
//        //查询该回款单号的核销记录
//        List<CompanyReceivableRecordEtity> companyReceivableRecordEtities = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no", record.getApplyNo()).eq("type", 1).eq("delete_state", 0));
//        //获取开票单ID
//        Integer invoiceId = companyReceivableRecordEtities.get(0).getInvoiceId();
//
//        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceService.getById(invoiceId);
        // 平账审核通过修改新资金池 将开票单核销状态改为4已平账

            //查询所有结算子工单
            List<WorderChildInformationEntity> childInformationEntityList = worderChildInformationService.getBaseMapper().selectBatchIds(Arrays.stream(balanceIds).map(Integer::parseInt).collect(Collectors.toList()));

            Set<Integer> publishIds = new HashSet();
            for (WorderChildInformationEntity worderChildInformationEntity : childInformationEntityList) {
                if (worderChildInformationEntity.getPublishId() != null) {
                    publishIds.add(worderChildInformationEntity.getPublishId());
                }
                // 已回款状态将类型改为0普通
                if (worderChildInformationEntity.getBalanceSetStatus() == 4) {
                    worderChildInformationEntity.setType(0);
                    // 已发布状态改为已平账
                } else if (worderChildInformationEntity.getBalanceSetStatus() == 5) {
                    worderChildInformationEntity.setBalanceSetStatus(16);
                    worderChildInformationEntity.setBalanceSetStatusValue("已平账");
                }
            }
            log.info("开票单平账 companyInvoiceNo:{},发布单id:{}", companyInvoiceEntity.getCompanyInvoiceNo(), JSON.toJSONString(publishIds));
            if (!publishIds.isEmpty()){
                List<BalancePublishEntity> balancePublishEntitys = this.balancePublishDao.selectBatchIds(new ArrayList<>(publishIds));

                BigDecimal balanceMoney = BigDecimal.ZERO;
                for (BalancePublishEntity balancePublishEntity : balancePublishEntitys) {
                    //判断是否是最后一笔
                    String worderIdStr = balancePublishEntity.getWorderIds();
                    String [] worderId = worderIdStr.split(",");
                    List<Integer> worderIdList = new ArrayList<>();
                    for (String s : worderId) {
                        worderIdList.add(Integer.parseInt(s));
                    }
                    List<CompanyReceivableEntity> receivableList = companyReceivableDao.getAllReceivableIdByBalanceId(worderIdList);
                    boolean last = true;
                    for (CompanyReceivableEntity companyReceivableEntity : receivableList) {
                        if (companyReceivableEntity == null || companyReceivableEntity.getType() == null || companyReceivableEntity.getType() == 1) {
                            last = false;
                        }
                    }
                    if (last){
                        //已经平账的总金额
                        BigDecimal money = companyReceivableDao.querySumMoney(companyReceivable.getId(),balancePublishEntity.getId());
                        if (money==null){
                            money = new BigDecimal(0);
                        }
                        //发布单金额-已经平账的总金额 = 发布单本次平账金额
                        balanceMoney = balanceMoney.add(balancePublishEntity.getPublishFee().subtract(money));
                    }else{
                        //本次平账子订单金额
                        BigDecimal money = companyReceivableDao.queryChildMoney(companyReceivable.getId(),balancePublishEntity.getId());
                        //子订单金额 = 发布单本次平账金额
                        balanceMoney = balanceMoney.add(money);
                    }
                }
                log.info("发布金额合计:{}", balanceMoney.doubleValue());

                // 新资金池更新 - 平账
                advanceMoneyInfoService.balanceMoney(balanceMoney);
            }
            if (companyInvoiceEntity.getCavState() == 3) {
                // 更新开票单核销状态为4已平账
                companyInvoiceService.update(new UpdateWrapper<CompanyInvoiceEntity>().set("cav_state", 4).eq("id", companyInvoiceEntity.getId()));
            }
            // 更新子订单状态
            worderChildInformationService.updateBatchById(childInformationEntityList);

    }

    @Override
    public void updateCompanyInvoiceCavState(WorderAuditRecordEntity record) {
        //如果字只做回款不更新
        if (StringUtils.isBlank(record.getBalanceIds())) {
            return;
        }
        //核销工单和激励单总金额
        BigDecimal cavTotalFee = new BigDecimal(0);
        //查询该回款单号的核销记录
        List<CompanyReceivableRecordEtity> companyReceivableRecordEtities = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no", record.getApplyNo()).eq("type", 1).eq("delete_state", 0));

        CompanyReceivableRecordEtity companyReceivableRecord = companyReceivableRecordEtities.get(0);

        //获取开票单ID
        Integer invoiceId = companyReceivableRecord.getInvoiceId();

        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceService.getBaseMapper().selectById(invoiceId);

        // 平账中
        if ((companyInvoiceEntity.getCavState() == 3||companyInvoiceEntity.getCavState() == 5) && companyReceivableRecord != null && StringUtils.isNotBlank(companyReceivableRecord.getCompanyReceivableNo())) {

            CompanyReceivableEntity companyReceivableEntity = companyReceivableService.getBaseMapper().selectOne(new QueryWrapper<CompanyReceivableEntity>().eq("company_receivable_no", companyReceivableRecord.getCompanyReceivableNo()));

            String[] balanceIdArr = null;
            String balanceIds = companyReceivableEntity.getBalanceIds();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(balanceIds)) {
                balanceIdArr = balanceIds.split(",");
            }
            updateBalanceAdvanceMoneyCompanyInvoiceCavState(companyInvoiceEntity, balanceIdArr,companyReceivableEntity);
            if (companyInvoiceEntity.getCavState()==3){
                return;
            }
        }

        List<CompanyReceivableRecordEtity> companyReceivableRecordList = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("invoice_id", invoiceId).eq("type", 1).eq("delete_state", 0));
        Map<String, Object> params = new HashMap<>(16);
        params.put("invoiceId", invoiceId);
        //获取开票单下所有工单总金额
        BigDecimal worderCompanyTotalFee = companyReceivableDao.queryWorderSumFeeByInvoiceId(params);
        //获取开票单下所有激励单总金额
//        BigDecimal stimulateCompanyTotalFee = companyReceivableDao.queryStimulateSumFeeByInvoiceId(params);
        BigDecimal totalFee = (worderCompanyTotalFee != null ? worderCompanyTotalFee : BigDecimal.ZERO);
        //获取已核销的总金额
        for (CompanyReceivableRecordEtity companyReceivableRecordEtity : companyReceivableRecordList) {
            CompanyReceivableEntity companyReceivableEntity  = companyReceivableDao.selectOne(new QueryWrapper<CompanyReceivableEntity>().eq("company_receivable_no", companyReceivableRecordEtity.getCompanyReceivableNo()).eq("type", 0));
            if (companyReceivableEntity!=null){
                if(companyReceivableRecordEtity.getCavFee() != null){
                    cavTotalFee = cavTotalFee.add(companyReceivableRecordEtity.getCavFee());
                }
            }
        }
        if (companyInvoiceEntity.getCavState()==5){
            //更新开票单状态
            companyInvoiceService.update(new UpdateWrapper<CompanyInvoiceEntity>().set("cav_state", cavTotalFee.compareTo(totalFee) < 0 ? 6 : 4).eq("id", invoiceId));
        }else{
            //更新开票单状态
            companyInvoiceService.update(new UpdateWrapper<CompanyInvoiceEntity>().set("cav_state", cavTotalFee.compareTo(totalFee) < 0 ? 1 : 2).eq("id", invoiceId));
        }

    }

    @Override
    public void updateCompanyReceivableCapitalpool(WorderAuditRecordEntity record, SysUserEntity user) {
        //查询回款单号涉及的所有核销或者回款记录
        List<CompanyReceivableRecordEtity> companyReceivableRecordEtities = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no", record.getApplyNo()).eq("delete_state", 0));
        //获取开票单ID
        Integer invoiceId = companyReceivableRecordEtities.get(0).getInvoiceId();
        //查询开票单
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceService.getBaseMapper().selectById(invoiceId);
        //车企ID
        Integer companyId = companyInvoiceEntity.getCompanyId();
        //根据车企查询资金池
        List<CompanyReceivableCapitalpoolEtity> companyReceivableCapitalpools = companyReceivableCapitalpoolDao.selectList(new QueryWrapper<CompanyReceivableCapitalpoolEtity>().eq("company_id", companyId).eq("delete_state", 0));
        //比那里资金池
        companyReceivableCapitalpools.forEach(companyReceivableCapitalpoolEtity -> {
            //遍历核销和回款记录
            companyReceivableRecordEtities.forEach(companyReceivableRecordEtity -> {
                //回款逻辑把当前余额加上回款金额
                if (companyReceivableRecordEtity.getType() == 0 && companyReceivableCapitalpoolEtity.getVoucherNo().equals(companyReceivableRecordEtity.getVoucherNo())) {
                    companyReceivableCapitalpoolEtity.setBalance(companyReceivableCapitalpoolEtity.getBalance().add(companyReceivableRecordEtity.getReceivableFee()));
                    //核销逻辑把当前余额减去核销金额
                } else if (companyReceivableRecordEtity.getType() == 1 && companyReceivableCapitalpoolEtity.getVoucherNo().equals(companyReceivableRecordEtity.getVoucherNo())) {
                    companyReceivableCapitalpoolEtity.setBalance(companyReceivableCapitalpoolEtity.getBalance().subtract(companyReceivableRecordEtity.getCavFee()));
                }
            });
            //更新当期资金池信息
            companyReceivableCapitalpoolEtity.setUpdateTime(LocalDateTime.now())
                    .setUpdateOper(user.getEmployeeName())
                    .setRemark(user.getEmployeeName() + "[" + LocalDateTime.now() + "] 回款单：" + record.getApplyNo() + "二审通过更新余额");
            companyReceivableCapitalpoolDao.updateById(companyReceivableCapitalpoolEtity);
        });
    }

    @Override
    public void updateCompanyReceivableCapitalpoolByCompanyReceivableRecord(String companyReceivableNo, String userName) {
        //查询回款单号涉及的所有核销或者回款记录
        List<CompanyReceivableRecordEtity> companyReceivableRecordEtities = companyReceivableRecordDao.selectList(new QueryWrapper<CompanyReceivableRecordEtity>().eq("company_receivable_no", companyReceivableNo).eq("delete_state", 0));
        //获取开票单ID
        Integer invoiceId = companyReceivableRecordEtities.get(0).getInvoiceId();
        //查询开票单
        CompanyInvoiceEntity companyInvoiceEntity = companyInvoiceService.getBaseMapper().selectById(invoiceId);
        //车企ID
        Integer companyId = companyInvoiceEntity.getCompanyId();
        //根据车企查询资金池
        List<CompanyReceivableCapitalpoolEtity> companyReceivableCapitalpools = companyReceivableCapitalpoolDao.selectList(new QueryWrapper<CompanyReceivableCapitalpoolEtity>().eq("company_id", companyId).eq("delete_state", 0));
        //比那里资金池
        companyReceivableCapitalpools.forEach(companyReceivableCapitalpoolEtity -> {
            //遍历核销和回款记录
            companyReceivableRecordEtities.forEach(companyReceivableRecordEtity -> {
                //回款逻辑把当前余额加上回款金额
                if (companyReceivableRecordEtity.getType() == 0 && companyReceivableCapitalpoolEtity.getVoucherNo().equals(companyReceivableRecordEtity.getVoucherNo())) {
                    companyReceivableCapitalpoolEtity.setBalance(companyReceivableCapitalpoolEtity.getBalance().add(companyReceivableRecordEtity.getReceivableFee()));
                    //核销逻辑把当前余额减去核销金额
                } else if (companyReceivableRecordEtity.getType() == 1 && companyReceivableCapitalpoolEtity.getVoucherNo().equals(companyReceivableRecordEtity.getVoucherNo())) {
                    companyReceivableCapitalpoolEtity.setBalance(companyReceivableCapitalpoolEtity.getBalance().subtract(companyReceivableRecordEtity.getCavFee()));
                }
            });
            //更新当期资金池信息
            companyReceivableCapitalpoolEtity.setUpdateTime(LocalDateTime.now())
                    .setUpdateOper(userName)
                    .setRemark(userName + "[" + LocalDateTime.now() + "] 回款单：" + companyReceivableNo + "二审通过更新余额");
            companyReceivableCapitalpoolDao.updateById(companyReceivableCapitalpoolEtity);
        });
    }

    @Override
    public void updatePublishWorderStatus(WorderAuditRecordEntity record, String[] worderChildIds, String[] worderNoArr) {
        if(record.getAuditStatus() == 1 || record.getAuditStatus() == 2){

            List<WorderChildInformationEntity> worderChildInformationEntityList = new ArrayList<>();
            List<Integer> worderChildIdList = new ArrayList<>();
            List<Integer> stimulateWorderChildIdList = new ArrayList<>();
            Set<Integer> worderIds = new HashSet<>();
            Set<Integer> stimulateIds = new HashSet<>();
            if (worderChildIds != null && worderChildIds.length>0) {
                worderChildInformationEntityList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>()
                        .in("id", worderChildIds)
                        .eq("is_delete", 0));

                worderChildIdList = worderChildInformationEntityList.stream().filter(item -> IntegerEnum.ZERO.getValue().equals(item.getBalanceSource()))
                        .map(WorderChildInformationEntity::getId)
                        .collect(Collectors.toList());

                stimulateWorderChildIdList = worderChildInformationEntityList.stream().filter(item -> IntegerEnum.ONE.getValue().equals(item.getBalanceSource()))
                        .map(WorderChildInformationEntity::getId)
                        .collect(Collectors.toList());

                worderIds = worderChildInformationEntityList.stream().filter(item -> IntegerEnum.ZERO.getValue().equals(item.getBalanceSource()))
                        .map(WorderChildInformationEntity::getWorderId)
                        .collect(Collectors.toSet());

                stimulateIds = worderChildInformationEntityList.stream().filter(item -> IntegerEnum.ONE.getValue().equals(item.getBalanceSource()) )
                        .map(WorderChildInformationEntity::getStimulateId)
                        .collect(Collectors.toSet());
            }

            Integer stimulateWorderSetStatus = record.getAuditStatus() == 1 ? 33 : 13;
            String stimulateWorderSetStatusValue = record.getAuditStatus() == 1 ? "发布首次审核通过" : "已发布";
            Integer publishStatus = record.getAuditStatus() == 1 ? 3 : 4;
            if(record.getPublishType() == 33 || record.getPublishType() == 36){
                Integer worder_set_status = record.getAuditStatus() == 1 ? 11 : 12;

                if (worderChildIdList != null && worderChildIdList.size() > 0) {
                    // 更新结算子订单状态
                    worderChildInformationService.update(null,
                            new UpdateWrapper<WorderChildInformationEntity>()
                                    .in("id", worderChildIdList)
                                    .eq("is_delete", 0)
                                    .set("balance_set_status", worder_set_status));
                }

                // 校验工单结算子订单全部审核通过 更新工单结算状态
                for (Integer worderId : worderIds) {
                    if (valiAllWorderChildInfomationAudit(worderId, worder_set_status)) {
                        worderInformationAccountDao.update(null,
                                new UpdateWrapper<WorderInformationAccountEntity>()
                                        .eq("worder_id", worderId)
                                        .set("worder_set_status", worder_set_status)
                                        .set("worder_set_status_value", record.getAuditStatus() == 1 ? "网点工单首次审核通过" : "网点工单二次审核通过"));
                    }
                }

            }else if(record.getPublishType() == 34 && worderNoArr != null && worderNoArr.length > 0){
                worderInformationAccountDao.update(null,
                        new UpdateWrapper<WorderInformationAccountEntity>()
                                .in("worder_no", worderNoArr)
                                .set("worder_Incre_status", record.getAuditStatus() == 1 ? 5 : 6)
                                .set("worder_Incre_status_value", record.getAuditStatus() == 1 ? "网点增项首次审核通过" : "网点增项二次审核通过"));
            }

            // 更新激励单状态
            if (stimulateWorderChildIdList != null && stimulateWorderChildIdList.size() > 0) {
                worderChildInformationService.update(null,
                        new UpdateWrapper<WorderChildInformationEntity>()
                                .in("id", stimulateWorderChildIdList)
                                .eq("is_delete", 0)
                                .set("balance_set_status", stimulateWorderSetStatus)
                                .set("balance_set_status_value", stimulateWorderSetStatusValue));
            }

            // 校验激励结算子订单全部审核通过 更新激励单状态
            for (Integer stimulateId : stimulateIds) {
                worderPmStimulateDao.update(null,
                        new UpdateWrapper<WorderPmStimulateEntity>()
                                .eq("id", stimulateId)
                                .set("status", stimulateWorderSetStatus));
            }
        }
        Map params = new HashMap();
        params.put("batchNo", record.getBatchNo());
        params.put("status", 3);
        updateBatchBalancePublish(params);
    }

    /**
     * 校验所有工单结算子订单都已审核
     * @param worderId
     * @param balanceSetStatus
     * @return
     */
    private Boolean valiAllWorderChildInfomationAudit (Integer worderId, Integer balanceSetStatus) {
        List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>()
                .eq("worder_id", worderId)
                .eq("is_delete", 0));
        // 获取状态未审核结算子订单
        List<Integer> worderChildIds = worderChildInformationEntityList.stream().filter(item -> !balanceSetStatus.equals(item.getBalanceSetStatus()))
                .map(WorderChildInformationEntity::getId)
                .collect(Collectors.toList());
        // 没有未审核结算子订单 验证通过 返回true
        return worderChildIds.size() == 0;
    }

    /**
     * 校验所有激励结算子订单都已审核
     * @param stimulateId
     * @param balanceSetStatus
     * @return
     */
    private Boolean valiAllStimulateWorderChildInfomationAudit (Integer stimulateId, Integer balanceSetStatus) {
        List<WorderChildInformationEntity> worderChildInformationEntityList = worderChildInformationService.getBaseMapper().selectList(new QueryWrapper<WorderChildInformationEntity>()
                .eq("stimulate_id", stimulateId)
                .eq("is_delete", 0));
        // 获取状态未审核结算子订单
        List<Integer> worderChildIds = worderChildInformationEntityList.stream().filter(item -> !balanceSetStatus.equals(item.getBalanceSetStatus()))
                .map(WorderChildInformationEntity::getId)
                .collect(Collectors.toList());
        // 没有未审核结算子订单 验证通过 返回true
        return worderChildIds.size() == 0;
    }
}