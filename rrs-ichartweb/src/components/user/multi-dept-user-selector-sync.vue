<template>
  <div>
    <el-input v-model="userNames" placeholder="请选择" class="input-with-select" clearable
              @change="valueChange()">
      <!--<i slot="suffix" class="el-input__icon el-icon-date" @click="select"></i>-->
      <el-button slot="append" icon="el-icon-search1" @click="select">选择</el-button>
    </el-input>

    <el-dialog
      width="80%"
      title="选择人员"
      :visible.sync="innerVisible"
      :close-on-click-modal="false"
      append-to-body>
      <el-row type="flex">
        <el-col :span="7">
          <el-tree :data="treeData" :props="defaultProps"
                   lazy
                   :load="loadNode"
                   ref="tree"
                   node-key="deptId"
                   :default-expanded-keys="idArr"
                   highlight-current
                   :expand-on-click-node="false"
                   @node-click="orgTreeNodeClick"></el-tree>
        </el-col>
        <el-col :span="16">
          <el-form :inline="true" class="demo-form-inline" :model="dataForm" @submit.native.prevent>
            <el-form-item label="用户名:" style="margin-left:21px">
              <el-input v-model="dataForm.userName"
                        placeholder="用户名" clearable
                        @keyup.enter.native="searchEnterFun"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary"
                         @click="queryBtn()">查询
              </el-button>
            </el-form-item>
          </el-form>

          <!--          <div>-->
          <!--            <el-checkbox-group v-model="checkboxGroup1" style="margin-left: 1100px;margin-bottom: 15px">-->
          <!--              <el-checkbox-button @change="seleAll(checkboxGroup1)">全选</el-checkbox-button>-->
          <!--            </el-checkbox-group>-->
          <!--          </div>-->

          <el-row type="flex">
            <el-col :span="22">
              <span>已选人员:</span>
              <el-tag
                style="margin-right: 10px;"
                v-for="tag in personList"
                :key="tag.username"
                closable
                @close="handleClose(tag)">
                {{tag.username}}
              </el-tag>
            </el-col>
            <el-col :span="2">
              <div class="text-right" style="padding-left: 10px;">
                <el-checkbox v-model="checkboxGroup1" @change="seleAll(checkboxGroup1)">全选</el-checkbox>
              </div>
            </el-col>
          </el-row>


          <el-table
            class="pa20"
            :data="dataList"
            border
            @row-click="selectMajorPerson"
            size="small"
            :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
            style="width: 100%">
            <el-table-column
              label="序号"
              type="index"
              align="center"
              width="50">
            </el-table-column>
            <el-table-column
              prop="username"
              header-align="center"
              align="center"
              label="用户名">
            </el-table-column>
            <el-table-column
              prop="email"
              header-align="center"
              align="center"
              label="邮箱">
            </el-table-column>
            <el-table-column
              prop="mobile"
              header-align="center"
              align="center"
              label="手机号">
            </el-table-column>
          </el-table>
          <el-row type="flex">
            <el-col :span="3">
              <!--              <div class="pa20" style="padding-left: 10px;">-->
              <!--                <el-checkbox v-model="checkboxGroup1" @change="seleAll(checkboxGroup1)">全选</el-checkbox>-->
              <!--              </div>-->
            </el-col>
            <el-col :span="21">
              <el-pagination
                class="pa20 text-right"
                :current-page="pageIndex"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pageSize"
                :total="totalPage"
                @size-change="sizeChange"
                @current-change="currentChange"
                layout="total, sizes, prev, pager, next, jumper">
              </el-pagination>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div class="pa20 text-center">
        <el-button type="primary" @click="save()">确认</el-button>
        <el-button @click="innerVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
    import {treeDataTranslate} from '@/utils'

    export default {
        name: 'employe-selector',
        props: {
            // clearable: {
            //   type: Boolean,
            //   required: true,
            //   default: true
            // },
            value: {
                //type: Number
            },
            uname: {
                type: String
            }
        },
        data() {
            return {
                checkboxGroup1: '',
                checked: false,
                idArr: [],
                personList: [],
                userIds: '',
                userNames: this.uname || '',
                deptId: undefined,
                deptCode: '',
                dataForm: {
                    userName: ''
                },
                newdataList: [],
                dataList: [],
                treeData: [],
                pageIndex: 1,
                pageSize: 10,
                totalPage: 0,
                defaultProps: {children: 'children', label: 'deptName'},
                innerVisible: false
            }
        },
        methods: {
            // 异步树叶子节点懒加载逻辑
            loadNode(node, resolve) {
                //console.log("--------------------->node", node)
                // 一级节点处理
                if (node.level === 0) {
                    this.getOneTree();
                }
                // 其余节点处理
                if (node.level >= 1) {
                    // 注意！把resolve传到你自己的异步中去
                    this.getOtherTree(node, resolve)
                }
            },
            getOneTree() {
                this.$http({
                    url: this.$http.adornUrl('/sys/dept/getOneLevelDeptList?deptLevel=1'),
                    method: 'get',
                    params: ''
                }).then(({data}) => {
                    this.treeData = treeDataTranslate(data.list, 'deptId', 'parentDeptId');
                    // console.log("-----------this.dataList--------->", this.treeData)
                })
            },
            //获取其他树节点
            getOtherTree(val, resolve) {
                this.$http({
                    url: this.$http.adornUrl('/sys/dept/getOtherTree'),
                    method: 'get',
                    params: this.$http.adornParams({deptid: val.key})
                }).then(({data}) => {
                    if (data.list && data.list.length > 0) {
                        resolve(data.list);
                        // this.$nextTick(() => {
                        //   this.mgdeptListTreeSetCurrentNode();
                        // })
                    } else {
                        resolve([]);
                    }
                })
            },
            select() {
                this.personList.length = 0;
                this.innerVisible = true;
                // this.getOrgTreeList()
                this.loadNode()
            },
            getOrgTreeList() {
                this.$http({
                    url: this.$http.adornUrl('/sys/dept/getTsDeptManger'),
                    method: 'get',
                    params: this.$http.adornParams({status: '1'})
                }).then(({data}) => {
                    this.deptList = data && data.code === 0 ? data.list : [];
                    this.treeData = treeDataTranslate(data.list, 'deptId', 'parentDeptId');
                    if (this.treeData) {
                        this.treeData.forEach((item) => {
                            this.idArr.push(item.deptId);
                        });
                        // this.deptId = this.treeData[0].deptId;
                        // this.rootLevel = this.treeData[0]._level;
                        this.getInfo('');
                    }
                })
            },
            orgTreeNodeClick(data, node, target) {
                this.deptId = data.deptId;
                this.deptCode = data.deptCode;
                this.rootLevel = data._level;
                this.pageIndex = 1;
                this.getInfo(data.deptCode);
            },
            searchEnterFun(e) {
                let keyCode = window.event ? e.keyCode : e.which;
                if (keyCode === 13) {
                    this.queryBtn()
                }
            },
            queryBtnDelay() {
                let self = this;
                if (this.timeoutObj) {
                    clearTimeout(this.timeoutObj);
                }
                this.timeoutObj = setTimeout(function () {
                    self.queryBtn();
                }, 200);
            },
            queryBtn() {
                this.pageIndex = 1;
                this.getInfo(this.deptCode)
            },
            handleClose(tag) {
                this.personList.splice(this.personList.indexOf(tag), 1);
                this.calcCheckStatus(this.dataList);
            },
            selectMajorPerson: function (row, event, column) {
                // this.personList.length = 0;
                let matchPerson = this.personList.filter((item) => {
                    return item.userId === row.userId
                });
                if (matchPerson.length === 0) {
                    this.personList.push(row);
                }
            },
            sizeChange(val) {
                this.pageSize = val;
                this.pageIndex = 1;
                this.getInfo(this.deptCode);
                this.checkboxGroup1 = false;
            },
            currentChange(val) {
                this.checked = false;
                this.pageIndex = val;
                this.getInfo(this.deptCode);
                this.checkboxGroup1 = false;
            },
            getInfo(deptCode) {
                this.$http.get(this.$http.adornUrl('/system/user/getManagerByUserName?date=' + new Date().getTime()), {
                    params: {
                        'rootLevel': this.rootLevel,
                        'status': '1',
                        // 'deptId': deptId,
                        'deptCode': deptCode,
                        'page': this.pageIndex,
                        'limit': this.pageSize,
                        'userName': this.dataForm.userName
                    }
                }).then((response) => {
                    if (response) {
                        this.dataList = response.data.page.list;
                        this.totalPage = response.data.page.totalCount

                        this.calcCheckStatus(this.dataList);
                    }
                }, (response) => {
                    console.error('查询机构用户列表发生错误')
                })
            },
            calcCheckStatus(datas) {
                this.checkboxGroup1 = false;
                let items = [];
                if (this.personList && this.personList.length > 0) {
                    let that = this;
                    // 1.查询当前列表的用户是否都在已选中
                    items = datas.filter(function (val) {
                        let res = false;
                        // 2.当前记录是否已选择
                        res = that.personList.filter(function (val1) {
                            return val1.userId == val.userId;
                        });
                        return res.length
                    })
                    if (items && items.length == datas.length) {
                        this.checkboxGroup1 = true;
                    }
                }

            },

            valueChange() {
                if (!this.userNames) {
                    this.personList = [];
                    this.$emit('input', "");
                    this.$emit('sendUserInfo', this.personList);
                }
            },
            save() {
                if (this.personList && this.personList.length > 0) {
                    let idsStr = this.personList.map(item => item.userId).join(',');
                    let userNameStr = this.personList.map(item => item.username).join(',');

                    this.$emit('input', idsStr);
                    this.$emit('sendUserInfo', this.personList);
                    this.userIds = idsStr;
                    this.userNames = userNameStr;
                    this.innerVisible = false
                } else {
                    this.$message({
                        type: 'warning',
                        message: '请选择人员!'
                    })
                }
            },
            seleAll: function (event) {
                if (event) {
                    // this.personList = [];
                    this.newdataList = this.dataList;
                    for (var i = 0; i < this.newdataList.length; i++) {
                        // console.log("----->this.dataList[i]", this.newdataList[i])
                        let matchPerson = this.personList.filter((item) => {
                            return item.userId === this.newdataList[i].userId
                        });
                        if (matchPerson.length === 0) {
                            this.personList.push(this.newdataList[i]);
                        }
                    }
                } else {
                  this.newdataList = this.dataList;
                    for (var a = 0; a < this.newdataList.length; a++) {
                      this.personList = this.personList.filter((item) => {
                        return item.userId !== this.newdataList[a].userId
                      });
                    }
                }
            }
            // seleAll(event) {
            //   if (event) {
            //     // this.personList = [];
            //     for (var i = 0; i < this.dataList.length; i++) {
            //       console.log("----->this.dataList[i]", this.dataList[i])
            //       this.personList.push(this.dataList[i]);
            //     }
            //   } else {
            //     this.personList = [];
            //   }
            // }
            , initSelectUser: function () {
                if(this.value){
                    this.$http.get(this.$http.adornUrl('/system/user/getManagerByUserName?date=' + new Date().getTime()), {
                        params: {
                            'status': '1',
                            'uidsStr': this.value,
                        }
                    }).then((response) => {
                        if (response) {
                            this.personList = response.data.page.list;
                        }
                    }, (response) => {
                        console.error('查询机构用户列表发生错误')
                    })
                }
            }
        },
        created() {
        },
        watch: {
            uname(val) {
                this.userNames = this.uname;
            },
            innerVisible(booleanVal) {
                if (booleanVal) {
                    this.initSelectUser();
                }
            }

        }
    }
</script>

<style scoped>

</style>
