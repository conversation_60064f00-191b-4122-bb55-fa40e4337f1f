package com.bonc.rrs.workManager.dao;

import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.workManager.entity.ExtField;
import com.bonc.rrs.workManager.entity.WorderFieldId;
import com.bonc.rrs.workManager.entity.vo.DotPositionAddedMaterielStockVo;
import com.bonc.rrs.workManager.entity.vo.ReceiverInfoVo;
import com.bonc.rrs.workManager.entity.vo.RegionCodeVo;
import com.bonc.rrs.workManager.entity.vo.UsedMaterielVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface AutidOrderMapper {

    List<Map<String,Object>> selectFieldValue(@Param(value = "worderNo") String worderNo,
                                              @Param(value = "purpose") Integer purpose,
                                              @Param(value = "type") Integer type);

    List<Integer> getFieldIds(@Param(value = "worderNo") String worderNo,
                                    @Param(value = "purpose") Integer purpose);

    int updateWorderFieldState(@Param(value = "worderNo") String worderNo,
                               @Param(value = "purpose") Integer purpose,
                               @Param(value = "state") Integer state);

    int updateBatchWorderField(@Param(value = "worderNo") String worderNo,
                               @Param(value = "purpose") Integer purpose,
                               @Param(value = "list") List<WorderFieldId> statelist);

    int updateFieldStatus(@Param(value = "worderNo") String worderNo,
                          @Param(value = "fieldId") Integer fieldId,
                          @Param(value = "checkStatus") Integer checkStatus);

    List<Map<String,Object>> selectWorderFieldInfolist(@Param(value = "worderNo") String worderNo,
                                                       @Param(value = "fieldType") Integer fieldType,
                                                       @Param(value = "fieldId") Integer fieldId);

    List<Map<String,String>> selectDictInfo(@Param(value = "key") Integer key);

    int updateAppointInstall(@Param(value = "id") Integer id,
                             @Param(value = "status") Integer status,
                             @Param(value = "statusValue") String statusValue);

    int updateWorderAutidInfo(@Param(value = "list")List<ExtField> fieldList,
                              @Param(value = "worderNo") String worderNo,
                              @Param(value = "purpose") Integer purpose);

    int updateAutidInfo(@Param(value = "fieldId")Integer fieldId,
                              @Param(value = "fieldValue")String fieldValue,
                              @Param(value = "worderNo") String worderNo,
                              @Param(value = "purpose") Integer purpose);

    List<Map<String,Object>> fandByValue(@Param(value = "fieldId")Integer fieldId,
                        @Param(value = "worderNo") String worderNo,
                        @Param(value = "purpose") Integer purpose);

    List<Map<String,Object>> selectOddAndMuchField(@Param(value = "worderNo") String worderNo,
                                                   @Param(value = "fieldType") Integer fieldType,
                                                   @Param(value = "fieldId") Integer fieldId);

    String getSelectData(@Param(value = "fieldType") Integer fieldType,
                         @Param(value = "fieldId") Integer fieldId);

    /**
     * 查询工单执行状态
     * @param worderNo
     * @return
     */
    Integer getWorderExecStatus(String worderNo);

    /**
     * 查询工单执行状态
     * @param worderNo
     * @return
     */
    Integer getWorderStatus(String worderNo);

    /**
     * 查询工单的客户姓名
     * @return
     */
    String getClientName(String worderNo);

    /**
     * 查询需要下载的文件扩展字段
     * @return
     */
    Map<String,Object> getWorderField(@Param(value = "worderNo") String worderNo,
                                      @Param(value = "purpose") Integer purpose,
                                      @Param(value = "fieldId") Integer fieldId);
    Map<String,Object> getWorderFieldName(@Param(value = "worderNo") String worderNo,
                                      @Param(value = "purpose") Integer purpose,
                                      @Param(value = "fieldId") Integer fieldId);

    /**
     * 是否用户自布线
     * @param worderNo
     * @return
     */
    Integer isSelfWiringByWorderNo(@Param(value = "worderNo") String worderNo);

    /**
     * 查询增值物料配置
     * @param worderId
     * @return
     */
    List<Map<String, Object>> queryAddMaterialConfig(@Param(value = "worderId") Integer worderId);

    /**
     * 查询工单使用的增值物料
     * @param worderId
     * @return
     */
    List<UsedMaterielVo> queryUsedAddedMaterielByWorderId(@Param(value = "worderId") Integer worderId);

    /**
     * 查询网点下增值物料库存
     * @param dotId
     * @param materielIdList
     * @return
     */
    List<DotPositionAddedMaterielStockVo> queryDotPositionAddedMaterielStock(@Param(value = "dotId") Integer dotId, @Param(value = "materielIdList") List<Integer> materielIdList);

    Integer getOutMoveCodeAndTime(String currDate);

    ReceiverInfoVo getReceiverInfo(@Param("worderId") Integer worderId);

    @Select("select name ,id,type from biz_region")
    List<RegionCodeVo> selectReginCode();

    Integer cancelLeaveInfo(@Param(value = "leaveNum") String leaveNum);

    Integer cancelLeaveOrder(@Param(value = "leaveNum") String leaveNum);
}
