package com.bonc.rrs.workManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
    * 品牌网点评分表
    */
@ApiModel(description="品牌网点评分表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("rrs_brand_dot_score")
public class BrandDotScoreBean implements Serializable {
    /** 主键 */
    @ApiModelProperty(name = "主键",notes = "")
    @TableId
    private Integer id ;
    /** 地市 */
    @ApiModelProperty(name = "地市",notes = "")
    private Integer city;
    /** 周期 */
    @ApiModelProperty(name = "周期",notes = "")
    private String cycle ;
    /** 品牌ID */
    @ApiModelProperty(name = "品牌ID",notes = "")
    private Integer brandId ;
    /** 品牌名称 */
    @ApiModelProperty(name = "品牌名称",notes = "")
    private String brandName ;
    /** 网点ID */
    @ApiModelProperty(name = "网点ID",notes = "")
    private Integer dotId ;
    /** 业绩 */
    @ApiModelProperty(name = "业绩",notes = "")
    private BigDecimal performance ;
    /** 服务能力 */
    @ApiModelProperty(name = "服务能力",notes = "")
    private BigDecimal serviceAbility ;
    /** 增值业绩 */
    @ApiModelProperty(name = "增值业绩",notes = "")
    private BigDecimal addedPerformance ;
    /** 关键任务 */
    @ApiModelProperty(name = "关键任务",notes = "")
    private BigDecimal missionCritical ;
    /** 扣分 */
    @ApiModelProperty(name = "扣分",notes = "")
    private BigDecimal deduct ;
    /** 综合评分 */
    @ApiModelProperty(name = "综合评分",notes = "")
    private BigDecimal score ;
    /**
     * 手动综合评分
     */
    @ApiModelProperty(name = "手动综合评分",notes = "")
    private BigDecimal manualScore ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private LocalDateTime createTime ;
    /** 修改时间 */
    @ApiModelProperty(name = "修改时间",notes = "")
    private LocalDateTime updateTime ;
    /** 0:正常，1：删除 */
    @ApiModelProperty(name = "0:正常，1：删除",notes = "")
    private Integer deleteState ;

    @TableField(exist = false)
    private String dotName;

    @TableField(exist = false)
    private Long dotArea;

    @TableField(exist = false)
    private Long dotCity;

    @TableField(exist = false)
    private String cityName;

    private static final long serialVersionUID = 1L;

    public BrandDotScoreBean(String cycle,Integer city, Integer brandId, String brandName, Integer dotId, BigDecimal addedPerformance, BigDecimal missionCritical, BigDecimal deduct) {
        this.cycle = cycle;
        this.city = city;
        this.brandId = brandId;
        this.brandName = brandName;
        this.dotId = dotId;
        this.addedPerformance = addedPerformance;
        this.missionCritical = missionCritical;
        this.deduct = deduct;
    }
}