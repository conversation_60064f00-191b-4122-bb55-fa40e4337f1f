<template>
  <div class="echart-box" :id="chartSetting.id" style="width: 100%; height:100%;overflow: hidden;"></div>
</template>
<script>
  export default {
    props: {
      params: Object,
      chartSetting: Object
    },
    data() {
      return {
        myChart: null,
        w: 100,
        h: 10,
        ow: 0,
        oh: 0,
        tempTimeout: {},
        isExeFnish: true,
        markPoint: {
          data: [
            // {type: 'max', name: '最大值'},
            // {type: 'min', name: '最小值'}
          ]
        },
        markLine: {
          data: [
            // {type: 'average', name: '平均值'}
          ]
        }
      }
    },
    methods: {
      refresh1() {
        let that = this;
        clearTimeout(this.tempTimeout);
        this.tempTimeout = setTimeout(function () {
          if (that.isExeFnish) {
            that.isExeFnish = false;
            that.initChart();
          }
        }, 800);
        // this.initChart();
      },
      resize() {
        let self = this; // 当前this指向的是一个组件
        self.refreshBtn = true;
        // 延时处理，解决图表大小计算不准确的问题
        setTimeout(function () {
          self.myChart.resize();
          let ele = document.getElementById(self.chartSetting.id);
          if (ele) {
            self.w = ele.clientWidth;
            self.h = ele.clientHeight;
            self.ow = ele.offsetWidth;
            self.oh = ele.offsetHeight;
            while (self.h <= 110) {
              ele = ele.parentElement;
              self.h = ele.parentElement.clientHeight - 20;
            }
            /*if (self.chartSetting.h && typeof self.chartSetting.h !== 'undefined') {
              if (self.chartSetting.h.length > 0 && self.chartSetting.h.substr(self.chartSetting.h.length - 1) === '%') {
                self.myChart.resize({width: self.w, height: self.h * 0.83});
              } else {
                self.myChart.resize({width: self.w, height: self.h});
              }
            } else {
              self.myChart.resize({width: self.w, height: self.h});
            }*/
            self.myChart.resize({width: self.w, height: self.h});
            //console.log('bar', this.chartSetting, 'width : ', self.w, self.ow, ' height :', self.h, self.oh);
          }
        }, 200);
      },
      drawLine() {
        let self = this; // 当前this指向的是一个组件
        this.w = document.getElementById(this.chartSetting.id).clientWidth;
        this.h = document.getElementById(this.chartSetting.id).clientHeight;
        this.myChart = this.$echarts.init(document.getElementById(this.chartSetting.id));
        // this.myChart.setOption(this.chartSetting.option);
        this.initChart();
        this.resize();
        let myChart = this.myChart;
        //根据窗口的大小变动图表 --- 重点
        window.addEventListener('resize', () => {
          this.resize();
        }, false);
      },
      dataAlignment(category_items, datas, colName) {
        // 数据补全
        let newDatas = [];
        for (let i = 0; i < category_items.length; i++) { // 设置初始值
          newDatas.push(this.getObj(datas, colName, category_items[i]))
        }
        //console.log("newDatas=",newDatas);
        return newDatas;
      },
      getObj(datas, colName, val) {
        let obj = {};
        obj[colName] = val;
        for (let i = 0; i < datas.length; i++) { // 设置初始值
          let item = datas[i];
          if (item && item[colName] && item[colName] == val) {
            return item;
          }
        }
        return obj;
      },

      initChart() {
        // this.params.cuid = this.$store.state.user.id;
        if (this.chartSetting._option.tableName) {
          this.$http({
            url: this.$http.adornUrl('/ichart/qds/ds'),
            method: 'post',
            data: this.$http.adornData({
              'params': this.params,
              'setting': this.chartSetting
            })
          }).then(({data}) => {
            //console.log('chartSetting', this.chartSetting, data);
            if (data && data.datas && data.code === 0) {
              if (this.chartSetting._option.reverse) {
                data.datas = data.datas.reverse();
              }
              //console.log(data);
              let legend = {
                x: 'right',
                y: 'top',
                data: []
              };
              let category_items = this.chartSetting._option.category_items;
              let showSetting = this.chartSetting._option.showSetting;
              // ----------------边距---------------
              if (this.chartSetting._option.grid) {
                this.chartSetting.option.grid = this.chartSetting._option.grid;
              }
              // 1、根据_option.xAxis 来初始化 option.xAxis   data
              let _xAxis = this.chartSetting._option.xAxis;
              let xAxis = this.chartSetting.option.xAxis;
              if (_xAxis) {
                for (let i = 0; i < _xAxis.length; i++) {
                  if (_xAxis[i].columnName && xAxis[i].data) {
                    if (category_items && category_items.length > 0) {
                      data.datas = this.dataAlignment(category_items, data.datas, _xAxis[i].columnName);
                    }
                    xAxis[i].data = this.extractionData(data.datas, _xAxis[i].columnName, _xAxis[i].suffix);
                    legend.data = xAxis[i].data;
                  }
                  //-----------字体倾斜---------
                  if (_xAxis[i].axisLabel) {
                    xAxis[i].axisLabel = {..._xAxis[i].axisLabel, showMaxLabel: true};
                  } else {
                    xAxis[i].axisLabel = {showMaxLabel: true}
                  }
                  // 坐标轴刻度与标签对齐
                  if (_xAxis[i].axisTick) {
                    xAxis[i].axisTick = _xAxis[i].axisTick;
                  }
                }
              }
              //console.log('_xAxis', _xAxis, 'xAxis', xAxis);
              // 2、根据_option.yAxis 来初始化 option.yAxis   data
              let _yAxis = this.chartSetting._option.yAxis;
              let yAxis = this.chartSetting.option.yAxis;
              if (_yAxis) {
                for (let i = 0; i < _yAxis.length; i++) {
                  if (!yAxis[i]) {
                    yAxis[i] = _yAxis[i];
                  }
                  if (_yAxis[i].columnName && yAxis[i].data) {
                    if (category_items && category_items.length > 0) {
                      data.datas = this.dataAlignment(category_items, data.datas, _yAxis[i].columnName);
                    }
                    yAxis[i].data = this.extractionData(data.datas, _yAxis[i].columnName);
                    legend.data = yAxis[i].data;
                  }

                  //-----------字体倾斜---------
                  if (_yAxis[i].axisLabel) {
                    yAxis[i].axisLabel = _yAxis[i].axisLabel;
                  }
                  // 坐标轴刻度与标签对齐
                  if (_yAxis[i].axisTick) {
                    yAxis[i].axisTick = _yAxis[i].axisTick;
                  }
                }
              }
              //console.log('this.chartSetting.option =', this.chartSetting.option);
              // 3、根据_option.series 来初始化 option.series  data
              let _series = this.chartSetting._option.series;
              let series = this.chartSetting.option.series;
              if (_series) {
                legend.data = [];
                for (let i = 0; i < _series.length; i++) {
                  //console.log('_series[i] bar =', series[i], this.extractionData(data.datas, _series[i].columnName));
                  // if (!series[i]) {
                  series[i] = _series[i];
                  // }
                  series[i].data = [];
                  if (_series[i].type) {
                    series[i].type = _series[i].type;
                  } else {
                    series[i].type = "bar";
                  }
                  if (_series[i].yAxisIndex) {
                    series[i].yAxisIndex = _series[i].yAxisIndex;
                  }
                  if (_series[i].axisLabel) {
                    series[i].axisLabel = _series[i].axisLabel;
                  }
                  if (series[i] && _series[i].columnName) {
                    // 判断是否 其他运算
                    if (_series[i].columnName.exp) {
                      series[i].data = this.otherExtractionData(data.datas, _series[i].columnName.exp.val1, _series[i].columnName.exp.val2, _series[i].columnName.exp.op);
                    } else {
                      series[i].data = this.extractionData(data.datas, _series[i].columnName);
                    }
                  }
                  if (series[i]) {
                    // 我就说加一个属性去判断，。这样写的话 又费劲了
                    // 针对y轴判断
                    if (series[i].name) {
                      legend.data.push(series[i].name);
                    } else {
                      if (_series[i].columnName.exp) {
                        series[i].name = this.getColInfo(data.dsInfo, _series[i].columnName.cName).displayName;
                      } else {
                        series[i].name = this.getColInfo(data.dsInfo, _series[i].columnName).displayName;
                      }
                      legend.data.push(series[i].name);
                    }
                  }
                  // --------------------------标记-----------------------
                  if (showSetting && showSetting.markPointMax) {
                    this.markPoint.data.push({type: 'max', name: '最大值'})
                  }
                  if (showSetting && showSetting.markPointMin) {
                    this.markPoint.data.push({type: 'min', name: '最小值'})
                  }
                  if (showSetting && showSetting.markLineAvg) {
                    this.markLine.data.push({type: 'average', name: '平均值'})
                  }
                  // -------------------------------------------------

                  series[i].markPoint = this.markPoint;
                  series[i].markLine = this.markLine;
                  if (showSetting && showSetting.itemWidth) {
                    series[i].barWidth = showSetting.itemWidth;
                  } else {
                    if (data.datas && data.datas.length <= 2) {
                      // series[i].barWidth = 50;
                    }
                  }
                }
              }
              // 是否显示图例
              if (this.chartSetting._option.showSetting) {
                if (this.chartSetting._option.showSetting.showLegend) {
                  if (this.chartSetting._option.legend) {
                    this.chartSetting.option.legend = this.chartSetting._option.legend;
                    this.chartSetting.option.legend.data = legend.data;
                    if (this.chartSetting._option.showSetting.legendPosition) {
                      this.chartSetting.option.legend.x = this.chartSetting._option.showSetting.legendPosition;
                    }
                  }
                  // this.chartSetting.option.legend = legend;
                } else {
                  delete this.chartSetting.option.legend;
                }
              } else {
                delete this.chartSetting.option.legend;
              }
              if (this.chartSetting.option.series.length) {
                this.chartSetting.option.series.map(item => {
                  item.stack = '总量';
                  return item;
                })
              }
              this.myChart.setOption(this.chartSetting.option);
            } else {
              this.myChart.setOption({});
              //this.$message.error('查询数据为空 ' + data.msg + 3)
            }
            this.isExeFnish = true;
          })
        } else {
          this.myChart.setOption(this.chartSetting.option);
        }
      },
      extractionData(dataList, colName, suffix) {
        //console.log('extractionData = ', dataList, colName);
        // 根据字段名将数据提取为数组形式
        if (!suffix) {
          suffix = ''
        }
        let datas = [];
        if (dataList && colName) {
          for (let i = 0; i < dataList.length; i++) {
            let item = dataList[i];
            if (!item[colName]) {
              item[colName] = 0;
            }
            datas.push(item[colName] + suffix);
          }
        }
        return datas;
      },
      // 特殊数据运算处理
      otherExtractionData(dataList, val1, val2, op, suffix) {
        //console.log('extractionData = ', dataList, colName);
        // 根据字段名将数据提取为数组形式
        if (!suffix) {
          suffix = ''
        }
        let datas = [];
        //console.log('data.dataszcx', dataList, val1, val2, op)
        if (dataList) {
          for (let i = 0; i < dataList.length; i++) {
            let val = 0;
            let item = dataList[i];
            item[val1] = item[val1] || 0;
            item[val2] = item[val2] || 0;
            if (item[val1] && item[val2]) {
              if (op === '/') {
                val = parseFloat(item[val1] / item[val2] * 100);
              }
              if (op === '*') {
                val = parseFloat(item[val1] / item[val2] * 100);
              }
            }
            if (op === '+') {
              val = item[val1] + item[val2];
            }
            if (op === '-') {
              val = item[val1] - item[val2];
            }
            datas.push(val.toFixed(2) + suffix);
          }
        }
        return datas;
      },
      getColInfo(dsInfo, colName) {
        //console.log('dsInfo', dsInfo);
        // 根据字段名将数据提取为数组形式
        let item = null;
        if (dsInfo && dsInfo.columns && colName) {
          for (let i = 0; i < dsInfo.columns.length; i++) {
            if (colName === dsInfo.columns[i].fieldName) {
              item = dsInfo.columns[i];
              break;
            }
          }
        }
        return item;
      }
    },
    mounted() {
      this.drawLine();
    }

  }
</script>
<style scoped>

  .echart-box {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

</style>
