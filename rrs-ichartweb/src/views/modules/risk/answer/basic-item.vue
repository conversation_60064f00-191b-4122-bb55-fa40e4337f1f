<template>
  <div>
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item name="1">
        <template slot="title">
          <span class="el-collapse-title">评价范围</span>
        </template>
        <div style="text-align: right;">
          <el-button type="primary" size="mini" @click="openAll()">{{folder ? '展开' : '折叠' }}项目全部流程</el-button>
        </div>
        <el-table
          ref="singleTable"
          border
          @row-click="handleCurrentChange"
          highlight-current-row
          :data="dataList"
          :row-class-name="getRowClass"
          class="pa20"
          :header-cell-style="{background:'#f5f5f5',fontWeight:'bold',color:'#000',textAlign:'center'}"
          max-height="400"
          style="width: 100%;text-align: center;">
          <el-table-column
            type="index"
            label="序号"
            width="50">
          </el-table-column>
          <el-table-column
            :label="item.colName"
            :prop="'data.'+item.colEnName"
            v-for="item in headerList"
            :key="item.id"
            :formatter="(item.inputType === 7 || item.inputType === 6) ? dataFormat : undefined"
            :render-header="renderHeader">
          </el-table-column>
          <el-table-column
            label="截止日期"
            prop="data.tcEndTime">
          </el-table-column>
        </el-table>
        <div class="text-center pa20">
          <pa-textarea :maxlength="500" placeholder="请输入填报说明，最多输入500字" v-if="taskStatus == 10" v-model="fillRemark"></pa-textarea>
          <el-button type="primary" @click="commitAll()" style="margin-top:12px">提交</el-button>
          <!--<el-button type="primary" v-if="tabIndex === tabListSize" @click="sendBack()">退回</el-button>-->
          <!-- <el-button type="primary" @click="commitAll()">提交</el-button>-->
        </div>
      </el-collapse-item>
      <el-collapse-item name="2">
        <template slot="title">
          <span class="el-collapse-title">评价维度</span>
        </template>
        <div ref="answers" v-show="answersShow">
          <div>
            <div class="pa20">
              <el-button type="primary" size="mini" @click="previousOne()" v-if="showNextOne">上一个</el-button>
              <el-button type="primary" size="mini" @click="nextOne()" v-if="showNextOne">下一个</el-button>
              <span style="font-size: 16px;margin-left: 10px">{{title}}</span>
            </div>
          </div>
          <answer-tab
            v-if="tempType && tempType === 2"
            ref="answerTab"
            :qbid="qbid"
            :tid="tid"
            :sid="sid"
            :tabIndex="tabIndex"
            :viewAnswer="viewAnswer"
            :relQuestion="relQuestion"
            :qText="qText"
            :relOption="relOption"
            @refreshItemData="getDataList">
          </answer-tab>
          <common-answer-tab
            v-if="tempType && tempType === 1"
            ref="answerTab"
            :qbid="qbid"
            :tid="tid"
            :sid="sid"
            :tabIndex="tabIndex"
            :viewAnswer="viewAnswer"
            :relQuestion="relQuestion"
            :qText="qText"
            :relOption="relOption"
            @refreshItemData="getDataList"/>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
  import answerTab from './answer-tab';
  import commonAnswerTab from './common-answer-tab';
  import paTextarea from '@/components/component/pa-textarea'

  export default {
    name: 'basic-item',
    props: {
      sid: {
        type: Number,
        required: true
      },
      tid: {
        type: Number,
        required: true
      },
      suid: {
        type: Number,
        required: true
      },
      view: {
        type: Boolean,
        required: true
      },
      tabIndex: {
        type: Number,
        required: true
      },
      tabListSize: {
        type: Number,
        required: true
      },
      relQuestion: {
        type: String,
        required: true
      },
      relOption: {
        type: String,
        required: true
      },
      tempType: {
        type: Number
      },
      taskStatus: {
        type: Number
      },
      dkUserId: {
        type: Number,
        required: false
      },
      isOver: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        activeTabName: '',
        activeName: '1',
        answerDetail: false,
        dataList: [],
        dataListOrigin: [],
        form: {},
        currentRow: null,
        qbid: 0,
        userId: this.$store.state.user.id,
        folder: true,
        title: '',
        viewAnswer: false,
        showNextOne: false,
        headerList: [],
        answersShow: true,
        qText: '',
        fillRemark: '',
        baseDeptList: [],
        deptIds: []
      }
    },
    mounted () {
      if (this.sid) {
        this.init(this.sid);
      }
    },
    methods: {
      init () {
        if (this.sid) {
          this.getTableHeader();
          this.getDataList(0);
          this.findQuestionText();
          this.answersShow = true;
        }
      },
      findQuestionText() {
        if (this.relQuestion && this.relQuestion !== '[]') {
          let jsn = JSON.parse(this.relQuestion);
          if (jsn && jsn.length === 2) {
            this.$http({
              url: this.$http.adornUrl('/operational/questionnaire/select'),
              method: 'get',
              params: this.$http.adornParams({sid: jsn[0], colEnName: jsn[1]})
            }).then(({data}) => {
              if (data && data.code === 0) {
                if (data.data && data.data[0]) {
                  this.qText = data.data[0].colName;
                }
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        }
      },
      getTableHeader () {
        this.$http({
          url: this.$http.adornUrl('/operational/questionnaire/select2'),
          method: 'get',
          params: this.$http.adornParams({basic: true, sid: this.sid})
        }).then(({data}) => {
          if (data.code === 0) {
            this.headerList = data.data;
            this.dataListLoading = false
          } else {
            this.$message.error(data.msg)
          }
          this.dataListLoading = false;
        })
      },
      getDataList (currentRowId) {
        this.dataListLoading = true;
        this.$http({
          url: this.$http.adornUrl('/operational/basic/select'),
          method: 'get',
          params: this.$http.adornParams({sid: this.sid})
        }).then(({data}) => {
          if (data && data.code === 0) {
            for (let item of data.data) {
              item.data['tcEndTime'] = item['tcEndTime']
            }
            this.dataList = data.data;
            for (let d of this.dataList) {
              let dd = d.data;
              this.headerList.forEach(item => {
                if (item.inputType === 6 || item.inputType === 7) {
                  this.deptIds.push(dd[item.colEnName])
                }
              })
            }
            if (this.deptIds.length > 0) {
              this.getBaseDeptList();
            }
            // window.vu = this;
            ;
            if (this.isOver) {
              this.dataList = this.dataList.filter((item) => { return item.taskStatus !== 12 });
            }
            console.log(this.dataList);
            this.dataList = this.dataList.filter((item) => {
              return (item['receiveUserId'] === this.userId && item['sendUserId'] === this.suid) && item['dkUserId'] === this.dkUserId && item.taskStatus !== 3; // ====suid  guolv
            });
            if (this.$refs.singleTable) {
              if (currentRowId) {
                let currentRows = this.dataList.filter((item) => item.id === currentRowId);
                if (currentRows.length !== 0) {
                  this.$refs.singleTable.setCurrentRow(currentRows[0]);
                }
              } else {
                let currentRows = [];
                // console.log('nth !-');
                if (this.currentRow) { // 上一次选中
                  currentRows = this.dataList.filter((item) => item.id === this.currentRow.id);
                  if (currentRows && currentRows.length !== 0) {
                    this.$refs.singleTable.setCurrentRow(currentRows[0]);
                  } else {
                    if (this.dataList[0]) {
                      this.$refs.singleTable.setCurrentRow(this.dataList[0]);
                    } else {
                      // this.answerShow = false;
                      // console.log('nth --');
                      this.answersShow = false;
                      this.$refs.singleTable.setCurrentRow(null);
                    }
                  }
                }
              }
            }
            this.dataListOrigin = data.data.filter((item) => { return !((item['receiveUserId'] === this.userId && item['sendUserId'] === this.suid) && item['dkUserId'] === this.dkUserId && item.taskStatus === 3) });
          } else {
            this.dataList = []
          }
        })
      },
      getBaseDeptList() {
        this.$http({
          url: this.$http.adornUrl('/sys/dept/getDeptList'),
          method: 'post',
          data: this.$http.adornParams({deptIds: this.deptIds})
        }).then(({data}) => {
          this.baseDeptList = data.list;
        })
      },
      detail (id) {
        console.log('view');
      },
      openAll () {
        this.folder = !this.folder;
        // console.log('dataList', this.dataList);
        if (this.dataListOrigin.length === this.dataList.length) {
          this.dataList = this.dataList.filter((item) => {
            return (item['receiveUserId'] === this.userId && item['dkUserId'] === this.dkUserId);
          })
        } else {
          this.dataList = this.dataListOrigin;
        }
      },
      dataFormat(row, column, cellValue, index) {
        for (let comp of this.baseDeptList) {
          if (cellValue === comp.deptId) {
            return comp.deptName
          }
        }
        // return this.$store.getters['bizcache/getDeptNameById'](cellValue);
      },
      removeTab () {
      },
      // 填写/查看 问卷
      fillQuestionnaire (row, viewAnswer) {
        this.answerShow = true;
        this.activeName = '2'
        this.showNextOne = true;
        this.viewAnswer = viewAnswer;
        let titles = [];
        this.headerList.forEach((item) => {
          if (item.basic) {
            if (item.inputType !== 6 && item.inputType !== 7) {
              if (row.data[item.colEnName]) {
                titles.push(row.data[item.colEnName])
              } else {
                titles.push(' ')
              }
            }
          }
          // else {  // 正常排序 ok!
          //   return false;
          // }
        });
        this.title = titles.join(' / ');

        this.qbid = row.id;
        this.$refs.singleTable.setCurrentRow(row);
        this.$nextTick(() => {
          this.$refs.answerTab.init(row);
        })
      },
      // 上一个
      previousOne () {
        // TODO view/answer/view other
        this.$http({
          url: this.$http.adornUrl(`/operational/questionnaire/answer/checkWriteOk/${this.qbid}`),
          method: 'post',
          data: this.$http.adornData()
        }).then(({data}) => {
          if (this.currentRow) {
            for (let i = this.dataList.length - 1; i >= 0; i--) {
              let item = this.dataList[i];
              if (item.id === this.currentRow.id) {
                if (i + 1 !== 1) {
                  console.log('上一个!');
                  this.currentRow = this.dataList[i - 1];
                  if (this.currentRow.answerStatus !== 0 &&
                    this.currentRow.receiveUserId !== this.userId) {
                    this.viewAnswer = true;
                  }
                  // this.$refs.singleTable.setCurrentRow(this.currentRow);
                  this.fillQuestionnaire(this.currentRow, this.viewAnswer);
                  break;
                } else {
                  this.$message.warning('没有上一个!');
                  break;
                }
              }
            }
          }
        });
      },
      nextOne () {
        // TODO view/answer/view other
        // 判断当前流程是否填写完毕，如果填写完成在进行下一条
        this.$http({
          url: this.$http.adornUrl(`/operational/questionnaire/answer/checkWriteOk/${this.qbid}`),
          method: 'post',
          data: this.$http.adornData()
        }).then(({data}) => {
          if (data.count === 0) {
            if (this.currentRow) {
              for (let i = 0; i < this.dataList.length; i++) {
                let item = this.dataList[i];
                if (item.id === this.currentRow.id) {
                  if (i + 1 !== this.dataList.length) {
                    console.log('下一个!');
                    this.currentRow = this.dataList[i + 1];
                    if (this.currentRow.answerStatus !== 0 &&
                      this.currentRow.receiveUserId !== this.userId) {
                      this.viewAnswer = true;
                    }
                    // this.$refs.singleTable.setCurrentRow(this.currentRow);
                    this.fillQuestionnaire(this.currentRow, this.viewAnswer);
                    break;
                  } else {
                    this.$message.warning('没有下一个!');
                    break;
                  }
                }
              }
            }
          } else {
            this.$message.warning('请填写完当前流程在进行下一流程填写!');
          }
        });
      },
      // 判断当前流程是否填写完成
      checkWriteOk (qbid) {
        this.$http({
          url: this.$http.adornUrl(`/operational/questionnaire/answer/checkWriteOk/${qbid}`),
          method: 'post',
          data: this.$http.adornData()
        }).then(({data}) => {
          this.checkWriteCount = data.count;
          console.log(this.checkWriteCount + '===1')
        });
      },
      handleCurrentChange (val) {
        if (!val) {
          return;
        }
        this.currentRow = val;
        let readOnly = false;
        if ((this.currentRow && this.currentRow.receiveUserId !== this.userId) || this.currentRow.dkUserId !== this.dkUserId) {
          readOnly = true;
        }
        this.fillQuestionnaire(val, readOnly)
      },
      // header
      deleteItem: function (id) {
        this.$confirm('确认是否删除该项?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delete(id);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      delete: function (id) {
        this.$http({
          url: this.$http.adornUrl(`/operational/questionnaire/delete/${id}`),
          method: 'post',
          data: this.$http.adornData()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.headerList = this.headerList.filter(item => {
              if (item.id !== id) {
                return item;
              }
            });
            this.$message.success({
              message: '删除成功',
              duration: 1500
            })
          } else {
            this.$message.error(data.msg)
          }
        });
      },
      renderHeader (h, {column, $index}) {
        if (this.view) {
          // return null;
          let head = this.headerList.filter((item) => item.colName === column.label)[0];
          if (head.remark) {
            return [
              column.label,
              h(
                'el-tooltip',
                {
                  props: {
                    content: head.remark,
                    placement: 'top',
                    effect: 'light'
                  }
                },
                [h('span', {
                  class: {
                    'el-icon-question': true
                  }
                })]
              )
            ]
          } else {
            return [
              column.label
            ]
          }
        }
        return (createElement, {column, $index}) => {
          return createElement('span', [
            createElement('span', column.label),
            createElement('i', {
              style: 'margin-left: 8px;cursor:pointer;',
              'class': {
                'el-icon-edit': true
              },
              on: {
                click: () => {
                  this.saveOrUpdateItemHeader(this.headerList[$index - 1].id);
                }
              }
            }),
            createElement('i', {
              style: 'margin-left: 8px;cursor:pointer;',
              'class': {
                'el-icon-close': true
              },
              on: {
                click: () => {
                  this.deleteItem(this.headerList[$index - 1].id);
                }
              }
            })
          ])
        }
      },
      getRowClass(row) {
        if (row.row.receiveUserId === this.userId && row.row.dkUserId === this.dkUserId) {
          if (row.row.answerStatus === 0) { // 未回答
            return 'answer-row';
          } else {  // 已回答
            if (row.row.backAnswerCount > 0) {
              //已填答但是被驳回,优先展示驳回, backAnswerCount模版关联填答驳回数量
              return 'had-reject';
            }
            return 'answered-row';
          }
        } else {  // others
          return 'others-row';
        }
      },
      commitAll () {
        this.$emit('commitAll', this.fillRemark);
      },
      sendBack () {
        this.$emit('sendBack')
      }
    },
    components: {
      answerTab,
      commonAnswerTab,
      paTextarea
    }
  }
</script>

<style>
  .pro-table {
    border: 1px solid #ccd0d4;
    width: 100%;
  }

  .pro-table tr td {
    text-align: center;
    padding: 8px;
    border: 1px solid #ccd0d4;
  }

  .area {
    margin-top: 65px;
  }

  .area .btn {
    margin-top: 10px;
  }

  .tip-border td {
    color: #ff0000;
  }

  .answer-row {
    color: #000000;
  }

  .answered-row {
    color: forestgreen;
  }

  .others-row {
    color: #ff0000;
  }
  .current-row > td {
    background: #e6efff !important;
  }
  .had-reject{
    color: darkred;
  }
</style>
