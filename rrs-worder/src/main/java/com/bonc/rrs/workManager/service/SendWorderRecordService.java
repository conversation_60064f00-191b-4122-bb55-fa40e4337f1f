package com.bonc.rrs.workManager.service;

import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 派单记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface SendWorderRecordService extends IService<SendWorderRecord> {

    /**
     * 获取网点区域周期内派数量Map
     * @param dotIdList
     * @param areaId
     * @return
     */
    List<Map<String,Object>> selectCycleDotSendCount(Set<Integer> dotIdList, Integer areaId, Integer brandId, Date cycleTime);

    List<SendWorderRecord> selectCycleDotSendList(Set<Integer> dotIdList, Integer areaId, Integer brandId, Date cycleTime, Integer worderType);


    List<SendWorderRecord> selectCycleDotSendListByArea(Set<Integer> dotIdList, Integer areaId, Integer brandId, Date cycleTime, Integer worderType);

}
