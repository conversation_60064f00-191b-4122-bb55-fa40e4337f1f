<template>
  <el-dialog :visible.sync="visible" width="40%">
    <el-form :model="addForm" :rules="dataRule" ref="addForm" label-width="80px">
      <el-form-item label="中文名称" prop="dictionaryName">
        <el-input v-model="addForm.dictionaryName"></el-input>
      </el-form-item>
      <el-form-item label="英文名称" prop="dictionaryKey">
        <el-input v-model="addForm.dictionaryKey"></el-input>
      </el-form-item>
      <el-form-item label="字典描述" prop="dictionaryDesc">
        <el-input type="textarea" v-model="addForm.dictionaryDesc"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
        <el-button type="warning" @click="visible=false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script>
  import { Loading } from 'element-ui';
  export default{
    data () {
      return {
        addForm: {
          dictionaryId: 0,
          dictionaryName: '',
          dictionaryKey: '',
          dictionaryDesc: ''
        },
        visible: false,
        dataRule: {
          dictionaryName: [
            { required: true, message: '中文名称不能为空', trigger: 'blur' }
          ],
          dictionaryKey: [
            { required: true, message: '英文名称不能为空', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      init (id) {
        this.visible = true;
        this.addForm.dictionaryId = id || 0;
        this.$nextTick(() => {
          this.$refs['addForm'].resetFields();
          if (this.addForm.dictionaryId) {
            this.$http({
              url: this.$http.adornUrl(`/sys/dict/${this.addForm.dictionaryId}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.addForm.dictionaryKey = data.info.dictionaryKey
                this.addForm.dictionaryName = data.info.dictionaryName
                this.addForm.dictionaryDesc = data.info.dictionaryDesc
              }
            })
          }
        })
      },
      // 表单提交
      dataFormSubmit () {
        // RESTfulAPI
        let methodType = !this.addForm.dictionaryId ? 'post' : 'put';
        let requestUrl = !this.addForm.dictionaryId ? '/sys/dict' : `/sys/dict/${this.addForm.dictionaryId}`;
        this.$refs['addForm'].validate((valid) => {
          if (valid) {
            let loadingInstance = Loading.service({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$http({
              url: this.$http.adornUrl(requestUrl),
              method: methodType,
              data: this.$http.adornData({
                'dictionaryId': this.addForm.dictionaryId || undefined,
                'dictionaryKey': this.addForm.dictionaryKey,
                'dictionaryName': this.addForm.dictionaryName,
                'dictionaryDesc': this.addForm.dictionaryDesc,
                'deleteFlag': 1
              })
            }).then(({data}) => {
              loadingInstance.close();
              if (data && data.code === 0) {
                this.$message({
                  message: '操作成功',
                  type: 'success',
                  duration: 1500,
                  onClose: () => {
                    this.visible = false
                    this.$emit('refreshDataList');
                    this.$emit('closeItem');
                  }
                })
              } else {
                this.$message.error(data.msg)
              }
            })
          }
        })
      }
    }
  }
</script>
