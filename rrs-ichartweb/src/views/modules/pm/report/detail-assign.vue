<template>
  <div>
    <el-dialog
      append-to-body
      :modal="true"
      :close-on-click-modal="false"
      :visible.sync="visible">
      <div class="pa-title">
        <h2>指派111</h2>
      </div>
      <el-form :model="dataForm" label-width="120px" class="pa20" ref="dataForm" :rules="rules">
        <el-form-item label="指派人:" prop="dealUserId">
          <dept-user-select ref="deptUserSelect"
                            v-model="dataForm.persons"
                            @sendUserInfo="sendUserInfo($event, 'dataForm')"
                            :uname="dataForm.personNames">
          </dept-user-select>
        </el-form-item>
        <el-form-item label="指派意见:" prop="remark">
          <el-input type="textarea" placeholder="请输入备注" v-model="dataForm.remark"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="cancel()">取消</el-button>
          <el-button type="primary" @click="assignUser()">指派</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import DeptUserSelect from '@/components/user/dept-user-selector-two'

  export default {
    components: {
      DeptUserSelect
    },
    data() {
      return {
        visible: false,
        dataForm: {
          dealUserId: undefined,
          dealUserName: '',
          opType: 3,
          taskId: '',
          remark: null,
          // project_id: null, ok
          // task_id: null, ok
          persons: null,
          personNames: null,
          writeable: 0,
          commitable: 0,
          status: 0,

          // creater: 0, ok
          // create_time: 0, ok
          // creater_name: 0, ok
          // update_time: 0, ok
          del_flag: 1,

          // bussi_id: 0,
          handleOpinions: "",
          pid: 0

        },
        rules: {
          persons: [
            {required: true, message: '指派人不能为空', trigger: 'blur'}
          ],
          remark: [
            {required: true, message: '指派意见不能为空', trigger: 'blur'}
          ]
        }
      }
    },
    methods: {
      init(taskId, pid) {
        this.dataForm.opType = 3;
        this.dataForm.taskId = taskId;
        this.dataForm.pid = pid;
        this.visible = true
      },
      sendUserInfo(userInfo, key) {
        if (key === 'dataForm') {
          this.dataForm.dealUserName = userInfo.username;
        } else if (typeof (key) === 'number') {
          this.dataForm.participants[key].userName = userInfo.username;
        }
        this.$refs.dataForm.validateField('dealUserId');
      },
      assignUser() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$http({
              url: this.$http.adornUrl('/pm/consultassign/saveAskedToAssign'),
              method: 'post',
              data: this.$http.adornData(this.dataForm)
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message.success({
                  message: '操作成功',
                  duration: 600
                })
                this.visible = false
                this.$emit('refreshDataList')
              } else {
                this.$message.error(data.msg)
              }
              this.$emit('refreshDataList')
            });
          }
        })
      },
      cancel() {
        this.visible = false
        let taskId = this.dataForm.taskId
        this.dataForm = {
          opType: 1
        }
        this.dataForm.taskId = taskId
      }
    }
  }
</script>

<style scoped>
</style>
