package com.bonc.rrs.worder.controller;

import java.util.Arrays;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.bonc.rrs.worder.entity.CompanyContactsEntity;
import com.bonc.rrs.worder.service.CompanyContactsService;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.*;



/**
 * 公司联系人表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-12 14:54:08
 */
@RestController
@RequestMapping("worder/companycontacts")
@Api(tags = {"公司联系人表相关接口"})
public class CompanyContactsController {
    @Autowired
    private CompanyContactsService companyContactsService;

    /**
     * 列表
     */
    @GetMapping("/list")
    @RequiresPermissions("worder:companycontacts:list")
    @ApiOperation(value="获取列表", notes="获取列表" )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
    })
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = companyContactsService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @GetMapping("/info/{contactsId}")
    @RequiresPermissions("worder:companycontacts:info")
    @ApiOperation(value="获取详情", notes="获取详情" )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "path", dataType = "Integer", name = "id", value = "id", required = true)
    })
    public R info(@PathVariable("contactsId") Integer contactsId){
		CompanyContactsEntity companyContacts = companyContactsService.getById(contactsId);

        return R.ok().put("companyContacts", companyContacts);
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    @RequiresPermissions("worder:companycontacts:save")
    @ApiOperation(value="新增记录", notes="新增记录" )
    public R save(@ApiParam @RequestBody CompanyContactsEntity companyContacts){
		companyContactsService.save(companyContacts);

        return R.ok();
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @RequiresPermissions("worder:companycontacts:update")
    @ApiOperation(value="修改记录", notes="修改记录" )
    public R update(@ApiParam @RequestBody CompanyContactsEntity companyContacts){
		companyContactsService.updateById(companyContacts);

        return R.ok();
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @RequiresPermissions("worder:companycontacts:delete")
    @ApiOperation(value="删除记录", notes="删除记录" )
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "body", dataType = "String", name = "ids", value = "ids", required = true)
    })
    public R delete(@RequestBody Integer[] contactsIds){
		companyContactsService.removeByIds(Arrays.asList(contactsIds));

        return R.ok();
    }

}
