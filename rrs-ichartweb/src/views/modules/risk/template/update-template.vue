<template>
  <create-template ref="createTemplate" :ptid="tid" :pview="view"></create-template>
</template>

<script>
  import createTemplate from './create-template'

  export default {
    name: 'update-template',
    data () {
      return {
        tid: 0,
        view: false
      }
    },
    created () {
      if (this.$route.query.tid && this.tid !== this.$route.query.tid) {
        this.$destroy();
      }
      if (this.$route.query.tid) {
        this.tid = Number(this.$route.query.tid);
      } else {
        this.$message.error('操作错误!');
      }
      this.$router.push({query: this.$route.query});
      let nameQuery = {name: this.$route.name, query: this.$route.query};
      this.$store.commit('common/updateMainTabsQuery', nameQuery);
    },
    components: {
      createTemplate
    }
  }
</script>

<style scoped>

</style>
