package com.bonc.rrs.honeywell.entity;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@JsonPropertyOrder({"materialCode", "materialName", "num",  "ratedPower", "skuCode", "skuName", "specColor"})
public class HoneywellOrderDetail {

    @NotBlank(message = "产品名称不能为空")
    private String materialName;

    @NotBlank(message = "产品code不能为空")
    private String materialCode;

    @NotBlank(message = "sku编码不能为空")
    private String skuCode;

    @NotBlank(message = "sku名称不能为空")
    private String skuName;

    @NotBlank(message = "规格颜色不能为空")
    private String specColor;

    // @NotNull(message = "数量不能为空")
    private Integer num;

    // @NotBlank(message = "额定功率不能为空")
    private String ratedPower;
}