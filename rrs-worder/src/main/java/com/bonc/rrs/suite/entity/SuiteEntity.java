package com.bonc.rrs.suite.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by liqingchao on 2020/1/14.
 */
@Data
@TableName("suite_information")
@ApiModel(value = "套包信息表")
public class SuiteEntity implements Serializable{
    @TableId
    @ApiModelProperty(value = "自增主键")
    @NotNull(message = "套包ID不能为空", groups = Update.class)
    private Integer id;

    @ApiModelProperty(value = "套包名称")
    @NotBlank(message = "套包名称不能为空", groups = {Insert.class, Update.class})
    private String name;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "物料列表")
    @NotEmpty(message = "物料列表不能为空", groups = {SuiteEntity.Insert.class, SuiteEntity.Update.class})
    private List<SuiteDetailEntity> materielList;

    public interface Insert {
    }

    public interface Update {
    }
}
