package com.bonc.rrs.worder.controller;

import com.bonc.rrs.worder.entity.WorderFittingsListsEntity;
import com.bonc.rrs.worder.service.WorderFittingsListsService;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;


/**
 * 配件记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-12 14:54:09
 */
@RestController
@RequestMapping("worder/worderfittingslists")
@Api(tags = {"配件记录表相关接口"})
public class WorderFittingsListsController {
	@Autowired
	private WorderFittingsListsService worderFittingsListsService;

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@RequiresPermissions("worder:worderfittingslists:list")
	@ApiOperation(value = "获取列表", notes = "获取列表")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", dataType = "String", name = "params", value = "参数", required = false)
	})
	public R list(@RequestParam Map<String, Object> params) {
		PageUtils page = worderFittingsListsService.queryPage(params);

		return R.ok().put("page", page);
	}


	/**
	 * 信息
	 */
	@GetMapping("/info/{id}")
	@RequiresPermissions("worder:worderfittingslists:info")
	@ApiOperation(value = "获取详情", notes = "获取详情")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "Integer", name = "id", value = "id", required = true)
	})
	public R info(@PathVariable("id") Integer id) {
		WorderFittingsListsEntity worderFittingsLists = worderFittingsListsService.getById(id);

		return R.ok().put("worderFittingsLists", worderFittingsLists);
	}


	@GetMapping("/listbyno/{no}")
	@RequiresPermissions("worder:worderfittingslists:listbyno")
	@ApiOperation(value = "根据订单号获取配件列表", notes = "根据订单号获取配件列表")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "path", dataType = "String", name = "no", value = "no", required = true)
	})
	public R listByNo(@PathVariable("no") String no) {
		return R.ok().putList(worderFittingsListsService.getVoListByWorderNo(no));
	}


	/**
	 * 保存
	 */
	@PostMapping("/save")
	@RequiresPermissions("worder:worderfittingslists:save")
	@ApiOperation(value = "新增记录", notes = "新增记录")
	public R save(@ApiParam @RequestBody WorderFittingsListsEntity worderFittingsLists) {
		worderFittingsListsService.save(worderFittingsLists);

		return R.ok();
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@RequiresPermissions("worder:worderfittingslists:update")
	@ApiOperation(value = "修改记录", notes = "修改记录")
	public R update(@ApiParam @RequestBody WorderFittingsListsEntity worderFittingsLists) {
		worderFittingsListsService.updateById(worderFittingsLists);

		return R.ok();
	}

	/**
	 * 删除
	 */
	@PostMapping("/delete")
	@RequiresPermissions("worder:worderfittingslists:delete")
	@ApiOperation(value = "删除记录", notes = "删除记录")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "body", dataType = "String", name = "ids", value = "ids", required = true)
	})
	public R delete(@RequestBody Integer[] ids) {
		worderFittingsListsService.removeByIds(Arrays.asList(ids));

		return R.ok();
	}

}
