package com.bonc.rrs.balanceprocess.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.dao.CompanyReceivableRecordDao;
import com.bonc.rrs.balanceprocess.entity.CompanyReceivableRecordEtity;
import com.bonc.rrs.balanceprocess.service.CompanyReceivableRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Description: 回款操作记录
 * @Author: liujunpeng
 * @Date: 2021/10/18 15:47
 * @Version: 1.0
 */
@Service
@AllArgsConstructor
public class CompanyReceivableRecordServiceImpl extends ServiceImpl<CompanyReceivableRecordDao,CompanyReceivableRecordEtity> implements CompanyReceivableRecordService{
}
