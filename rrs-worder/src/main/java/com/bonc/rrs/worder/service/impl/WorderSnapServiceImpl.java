package com.bonc.rrs.worder.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.worder.dao.WorderSnapDao;
import com.bonc.rrs.worder.entity.WorderSnapEntity;
import com.bonc.rrs.worder.general.QuerySpecification;
import com.bonc.rrs.worder.service.WorderSnapService;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.Query;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("worderSnapService")
public class WorderSnapServiceImpl extends ServiceImpl<WorderSnapDao, WorderSnapEntity> implements WorderSnapService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<WorderSnapEntity> page = this.page(
                new Query<WorderSnapEntity>().getPage(params),
                new QuerySpecification<WorderSnapEntity>().getWrapper(params, WorderSnapEntity.class)
        );

        return new PageUtils(page);
    }

}