package com.bonc.rrs.message.entity;

import com.bonc.rrs.warning.entity.po.ReceiveUserPo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangyibo on 2020-03-23 14:15
 */

@Data
public class WorderMessageEntity implements Serializable {

    private Integer id;
    // 工单编号
    private String worderNo;
    // 工单子状态
    private String worderExecStatus;
    // 发送人账号ID
    private String sendUserId;
    // 消息标题
    private String messageTitle;
    // 消息内容
    private String messageContent;
    // 发送时间
    private String sendTime;

    private String send;
    // 是否已读 0未读 1已读
    private String read;
    // 消息类型 0：消息 1：预警
    private String messageType;
    // 发送类型 0：站内 1：短信
    private String sendType;
    private LocalDateTime gmtCreate;
    private LocalDateTime gmtModified;

    /**
     * 接受人账号ID
     */
    private String receiveUserId;
    /**
     * 接受人手机号
     */
    private String receiveUserMobile;

    private String detailNumber;
    private String triggerValidate;

    //定时器触发时间
    private Long sendTimeStamp;
    //预警延迟消息状态记录表，处理状态：0:未处理，1:已处理
    private String processState;

    // 接收人信息集合
    private List<ReceiveUser> receiveUserList;

    public void setReceiveUser(ReceiveUserPo user, String roleType) {
        String receiveUserId = null;
        String receiveUserMobile = null;
        // 客服
        if ("1".equals(roleType)) {
            receiveUserId = user.getCreateId();
            receiveUserMobile = user.getCreateUserMobile();
        }
        // 服务经理
        else if ("2".equals(roleType)) {
            receiveUserId = user.getPmId();
            receiveUserMobile = user.getPmUserMobile();
        }
        // 网点管理员 目前找不到管理员账号ID，无法发送消息
        else if ("3".equals(roleType)) {
            receiveUserId = user.getDotAdminUserId();
            receiveUserMobile = user.getDotAdminMobile();
        }
        // 服务兵
        else if ("4".equals(roleType)) {
            receiveUserId = user.getServiceUserId();
            receiveUserMobile = user.getServiceUserMobile();
        }
        this.setReceiveUserId(receiveUserId);
        this.setReceiveUserMobile(receiveUserMobile);
    }

    public void setReceiveUserList(List<ReceiveUser> receiveUserList, String roleType) {
        List<ReceiveUser> receiveUserListTemp = new ArrayList<>();
        for (ReceiveUser receiveUser : receiveUserList) {
            if (roleType.equals(receiveUser.getReceiveRoleId()) && StringUtils.isNotBlank(receiveUser.getReceiveUserMobile())) {
                receiveUserListTemp.add(receiveUser);
            }
        }
        this.receiveUserList = receiveUserListTemp;
    }
}
