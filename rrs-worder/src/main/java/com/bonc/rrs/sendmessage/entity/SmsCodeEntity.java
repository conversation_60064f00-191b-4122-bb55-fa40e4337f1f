package com.bonc.rrs.sendmessage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_sms_code")
public class SmsCodeEntity {

    /** 主键id */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /** 手机号码 */
    private String phone;

    /** 验证码 */
    private String code;

    /** 失效时间 */
    private Date expireTime;

    /** 创建时间 */
    private Date createTime;
}
