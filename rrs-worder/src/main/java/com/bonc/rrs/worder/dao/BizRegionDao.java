/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.bonc.rrs.worder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.BizRegionEntity;
import com.bonc.rrs.worder.entity.RegionTreeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BizRegionDao extends BaseMapper<BizRegionEntity> {

    /**
     * 查询全国树状结构
     * @return
     */
    List<RegionTreeEntity> listNationTree();

    /**
     * 根据工单编号获取工单地区
     * @param worderNo
     * @return
     */
    List<BizRegionEntity> getWorderRegionRegcode(String worderNo);
    /**
     * 根据区省市区查询区县
     * @param ids
     * @return
     */
    List<BizRegionEntity> selectAreaIdByIds(@Param(value = "ids") List<Integer> ids);

    List<BizRegionEntity> selectParentRegions(@Param("id") Integer id);

    List<String> getAllRegion();

    List<String> getRegionByUser(@Param("userId") Long userId);


    List<BizRegionEntity> getProvinceByUser(@Param("userId") Long userId);

    List<BizRegionEntity> getListByUserAndRegCode(@Param("userId") Long userId, @Param("regcode") String regcode);

    List<BizRegionEntity> getProvinceByDotUser(@Param("userId") Long userId);

    List<BizRegionEntity> getListByDotUserAndRegCode(@Param("userId") Long userId,@Param("regcode") String regcode);

    BizRegionEntity getRegionByBydCode(@Param("bydCode") Long bydCode);

    List<String> getTslRegion();
    List<String> getLxRegion();
    List<String> getOtherRegion();

    List<RegionTreeEntity> listNationTreeBrand(@Param("brandId") Integer brandId,@Param("regione") String regione);

    /**
     * 根据区编码获取省市区的id
     * @param areaCode
     * @return
     */
    String getListByAreaCode(@Param("areaCode") String areaCode);

    BizRegionEntity getRegionByName(@Param("pid") Long pid, @Param("regionName") String regionName, @Param("level") Integer level);

    BizRegionEntity getFirstDistrictByCityId(@Param("cityId") Long cityId);
}
