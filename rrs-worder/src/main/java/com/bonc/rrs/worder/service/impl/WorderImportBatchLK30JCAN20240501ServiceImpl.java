
package com.bonc.rrs.worder.service.impl;

import com.bonc.rrs.worder.constant.BrandEnum;
import com.bonc.rrs.worder.constant.TemplateEnum;
import com.bonc.rrs.worder.dto.dto.WorderImportBatchDTO;
import com.bonc.rrs.worder.dto.vo.WorderImportBatchVo;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/29 10:51
 * @Version 1.0.0
 */

@Service
@Scope("prototype")
@Slf4j
public class WorderImportBatchLK30JCAN20240501ServiceImpl  extends AbstractWorderImportBatch {
    @Override
    protected Integer getBrandId() {
        return BrandEnum.LK.getId();
    }

    @Override
    public void setTemplateId() {
        addTemplateId(TemplateEnum.LK_30_JCAN_20240501.getId());
    }

    @Override
    public WorderInfoEntity getData(WorderImportBatchDTO worderImportBatchDTO, WorderImportBatchVo worderImportBatchVo) {
        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address = worderImportBatchDTO.getJ() + "_" + worderImportBatchDTO.getL() + "_" + worderImportBatchDTO.getN() + "_" + worderImportBatchDTO.getO();

        worderInfoEntity.setUserName(worderImportBatchDTO.getG());
        worderInfoEntity.setUserPhone(worderImportBatchDTO.getH());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(worderImportBatchDTO.getF());
        worderInfoEntity.setTemplateId(worderImportBatchVo.getTemplateId());

        worderInfoEntity.setCarBrand(BrandEnum.YH.getId().toString());
        worderInfoEntity.setCarModel("4");
        //
        worderInfoEntity.setCompanyId(Integer.parseInt(worderImportBatchDTO.getD()));
        // 360校验厂商id
        setCheckResult360(Integer.parseInt(worderImportBatchDTO.getD()), 1);
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setWorderTypeId(5);

        worderInfoEntity.setCandidate("系统自动");
        worderInfoEntity.setCreator(89L);
        worderInfoEntity.setCreateBy(worderImportBatchVo.getUserId());
        worderInfoEntity.setWorderSourceTypeValue(worderImportBatchDTO.getP());
        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

        worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", 5));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderImportBatchDTO.getF()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", Integer.parseInt(worderImportBatchDTO.getD())));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", worderImportBatchDTO.getQ()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", worderImportBatchDTO.getP()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", worderImportBatchDTO.getG()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderImportBatchDTO.getH()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(940, "车企系统订单编号", Integer.parseInt(worderImportBatchDTO.getD())));
        worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);
        return worderInfoEntity;
    }
}