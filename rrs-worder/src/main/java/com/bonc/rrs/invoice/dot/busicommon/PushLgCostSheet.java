
package com.bonc.rrs.invoice.dot.busicommon;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for pushLgCostSheet complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="pushLgCostSheet">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="lgCostSheetInObj" type="{http://busiCommon.interfaces.cvp.haier.com/}lgCostSheetInObj" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "pushLgCostSheet", propOrder = {
    "lgCostSheetInObj"
})
public class PushLgCostSheet {

    protected LgCostSheetInObj lgCostSheetInObj;

    /**
     * Gets the value of the lgCostSheetInObj property.
     * 
     * @return
     *     possible object is
     *     {@link LgCostSheetInObj }
     *     
     */
    public LgCostSheetInObj getLgCostSheetInObj() {
        return lgCostSheetInObj;
    }

    /**
     * Sets the value of the lgCostSheetInObj property.
     * 
     * @param value
     *     allowed object is
     *     {@link LgCostSheetInObj }
     *     
     */
    public void setLgCostSheetInObj(LgCostSheetInObj value) {
        this.lgCostSheetInObj = value;
    }

}
