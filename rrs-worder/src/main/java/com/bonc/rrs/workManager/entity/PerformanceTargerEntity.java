package com.bonc.rrs.workManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 业绩指标目标值记录表;
 *
 * <AUTHOR> liujunpeng
 * @date : 2023-8-23
 */
@Data
@ApiModel(value = "业绩指标目标值记录表", description = "")
@TableName("performance_targer")
public class PerformanceTargerEntity implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(name = "主键", notes = "")
    @TableId
    private Integer id;
    /**
     * 周期
     */
    @ApiModelProperty(name = "周期", notes = "")
    private String cycle;
    /**
     * 品牌ID
     */
    @ApiModelProperty(name = "品牌ID", notes = "")
    private Integer brandId;
    /**
     * 省份
     */
    @ApiModelProperty(name = "省份", notes = "")
    private Integer provinceId;
    /**
     * 指标
     */
    @ApiModelProperty(name = "指标", notes = "")
    private Integer indicatorId;
    /**
     * 目标值
     */
    @ApiModelProperty(name = "目标值", notes = "")
    private BigDecimal target;
    /**
     * 创建时间
     */
    @ApiModelProperty(name = "创建时间", notes = "")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(name = "创建人", notes = "")
    private Integer createUser;
    /**
     * 0：正常，1：删除
     */
    @ApiModelProperty(name = "0：正常，1：删除", notes = "")
    private Integer deleteState;

    @TableField(exist = false)
    private String brandName;

    @TableField(exist = false)
    private String provinceName;

    @TableField(exist = false)
    private String indicatorDesc;

    @TableField(exist = false)
    private String unit;
}