<template>
  <!-- 问卷列表页面 -->
  <div class="pa-box">
    <el-tabs class="pa20" v-model="activeTabName" type="card" >
      <el-tab-pane
        :closable="!view"
        @click="tabClick()"
        v-for="item in tabList"
        :key="item.id"
        :label="item.sheetName"
        :name="String(item.id)">
        <span slot="label" style="padding: 8px">
         {{item.sheetName}}
        </span>
        <div style="padding:0 20px;" class="pa20">
          <el-collapse v-model="activeName" accordion>
            <el-collapse-item name="1">
              <template slot="title">
                <span class="el-collapse-title">评价范围</span>
              </template>

              <answer-item ref="basicItem" :sid="item.id" :tid="tid" :view="view"></answer-item>

            </el-collapse-item>
          </el-collapse>
        </div>
      </el-tab-pane>
      <el-tab-pane key="addButton" v-if="!view"></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import answerItem from './answer-item'

  export default{
    data () {
      return {
        tid: 0,
        view: true,
        history: false,
        activeName: '1',
        editableTabs: [],
        list: [],
        activeTabName: '',
        tabList: [],
        tabEditVisible: false,
        questionList: []
      }
    },
    created () {
      this.initQuestion();
      if (this.$route.query.tempId) {
        this.tid = this.$route.query.tempId;
        // TODO
        // this.tid = 251;
        this.getTabList();
      } else {
        console.log('创建模板');
      }
    },
    // created () {
    //   if (this.tid) {
    //     this.getTabList();
    //   } else {
    //     console.log('创建模板');
    //   }
    // },
    methods: {
      delete (id, targetName) {
        this.$http({
          url: this.$http.adornUrl(`/operational/templatesheet/delete/${id}`),
          method: 'post',
          data: this.$http.adornData()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.delTabs(targetName);
            this.$message.success({
              message: '删除成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                // do nth
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        });
      },
      getTabList () {
        this.$http({
          url: this.$http.adornUrl('/operational/templatesheet/select'),
          method: 'get',
          params: this.$http.adornParams({tid: this.tid})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tabList = data.data;
            // TODO this.activeTabName initial value is '0' ?
            if ((!this.activeTabName || this.activeTabName === '0') && this.tabList.length !== 0) {
              this.activeTabName = this.tabList[0].id + '';
            }
          } else {
            this.$message.error(data.msg)
          }
        });
      },
      toGL: function () {
        this.$router.push({name: 'risk-manage'})
      },
      updateStatus (status) {
        this.$http({
          url: this.$http.adornUrl('/operational/template/update'),
          method: 'post',
          data: this.$http.adornData({id: this.tid, status: status})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message.success({
              message: '操作成功',
              duration: 1500
            });
            this.$router.push({name: 'risk-manage'});
          } else {
            this.$message.error(data.msg)
          }
        });
      },
      tabClick () {
        this.dialogFormVisible = true
      },
      initQuestion () {
        this.$http({
          url: this.$http.adornUrl('/operational/questionnaire/queryTreeHeader2'),
          method: 'get',
          params: this.$http.adornParams({basic: false, sid: this.sid, inputType: 1}) // TODO sid
        }).then(({data}) => {
          if (data.code === 0) {
            let datax = data.data;
            this.qList = data.data;
            // console.log('7788', datax);
            let trs = [];
            // let maxLevel = 0;
            datax.forEach((item, index, array) => {
              let tds = [];
              // 控制措施ðß是否有监管规定ðß€$€379_14/419_0/
              let strAry = item.allPath.substr(0, item.allPath.length - 1).split('€$€');
              let names = strAry[0].split('ðß');
              let ids = strAry[1].split('/');
              let level = 0;
              ids.forEach((it, idx, ary) => {
                let newItem = {};
                newItem.id = it.split('_')[0];
                newItem.rowspan = it.split('_')[1];
                newItem.name = names[idx];
                ++level;
                if (level === ary.length) {
                  newItem.xcolspan = level;
                }
                tds.push(newItem);
              });
              if (this.maxLevel < level) {
                this.maxLevel = level;
              }
              trs.push(tds);
            });
            this.questionList = trs;
            // console.log('tds', trs);
            // console.log('maxLevel', this.maxLevel);
          } else {
            this.$message.error(data.msg)
          }
        })
      }
    },
    components: {
      answerItem
    }
  }
</script>
<style>
  .pro-table {
    border: 1px solid #ccd0d4;
    width: 100%;
  }

  .pro-table tr td {
    text-align: center;
    padding: 8px;
    border: 1px solid #ccd0d4;
  }

  .area {
    margin-top: 65px;
  }

  .area .btn {
    margin-top: 10px;
  }

  .el-collapse-item {
    background: red;
  }

  .el-collapse-title {
    font-weight: bold;
    font-size: 15px;
    color: #17B3A3;
  }

</style>
