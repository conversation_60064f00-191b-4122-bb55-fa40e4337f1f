package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushSubmitReviewInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * @Description 服务商提交审核
 * @Description /jumpto/openapi/sp/pushSubmitReviewInfo
 * @Date 2024/2/28 14:04
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushSubmitReviewInfo extends AbstractBusinessProcess {

    final IBydApiService iBydApiService;

    @Override
    public String getProcessCode() {
        return "pushSubmitReviewInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        if (StringUtils.isBlank(businessProcessPo.getWorderNo())) {
            return Result.error("工单号不能为空");
        }
        try {
            WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(businessProcessPo.getWorderNo());
            if (worderInformationEntity == null) {
                return Result.error("非法工单号");
            }

            PushSubmitReviewInfo pushInstallationInfo = new PushSubmitReviewInfo();
            pushInstallationInfo.setOperatePerson(StringUtils.defaultIfBlank(businessProcessPo.getOperator(), OPERATE_PERSON));
            pushInstallationInfo.setOrderCode(worderInformationEntity.getCompanyOrderNumber());
            pushInstallationInfo.setCommitDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis()));
            //服务商提交审核
            OtherApiResponse otherApiResponse = iBydApiService.pushSubmitReviewInfo(pushInstallationInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.otherApiResponseError(otherApiResponse.getErrno(), otherApiResponse.getErrmsg());
            }
        }catch (Exception e){
            log.error("服务商提交审核回传出现异常", e);
            return Result.otherApiResponseError(500,"服务商提交审核回传出现异常");
        }
        return Result.otherApiResponseSuccess();
    }
}
