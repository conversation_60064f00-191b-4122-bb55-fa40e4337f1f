package com.bonc.rrs.workManager.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Description: 网点审批单状态枚举
 * @Author: liujunpeng
 * @Date: 2023/8/2 15:34
 * @Version: 1.0
 * 审批单状态，0：初始，1：一审，-1：一审失败，2：二审，-2：二审失败，3：二审成功
 */
@Getter
@AllArgsConstructor
public enum DotApproveStatusEnum {
    /**
     * 初始
     */
    INITIAL(0, "初始"),
    /**
     * 一审
     */
    FIRST_INSTANCE(1, "一次审核"),
    /**
     * 一审失败
     */
    FIRST_INSTANCE_FAIL(-1, "一审不通过"),
    /**
     * 二审
     */
    SECOND_INSTANCE(2, "二次审核"),
    /**
     * 二次审核失败
     */
    SECOND_INSTANCE_FAIL(-2, "二次审核不通过"),
    /**
     * 删除
     */
    SECOND_INSTANCE_SUCCESS(3, "二次审核通过");

    private final Integer code;

    private final String name;

    public static String getName(Integer code) {
        if(code == null){
            return "";
        }
        Optional<DotApproveStatusEnum> first = Arrays.stream(DotApproveStatusEnum.values()).filter(item -> code.equals(item.getCode())).findFirst();
        if (first.isPresent()) {
            return first.get().getName();
        }
        return "";
    }
}
