<template>
  <div class="mod-datasource">
    <el-row>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="名称:">
          <el-input clearable v-model.trim="seletableName" placeholder="请输入中文名或英文名"
                    @blur="seledataSource(seletableName)"></el-input>
        </el-form-item>
        <el-form-item label="所属系统:">
          <el-select v-model="dataType" placeholder="请输入" @change="seledataSource(dataType)"
                     @visible-change="selectSyStemType($event,1)" clearable>
            <el-option
              v-for="item in systemItem"
              :key="item.itemKey"
              :label="item.itemValue"
              :value="item.itemKey">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="pageIndex = 1;seledataSource(seletableName,dataType)">查询</el-button>
          <el-button v-if="isAuth('ichart:datasource:xinzeng')" type="primary" @click="preview">新增</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row class="row-bg row-query-separator"></el-row>
    <el-row>
      <el-table
        :data="page.list"
        ref="multipleTable"
        tooltip-effect="dark"
        style="width: 100%"
        border
        :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
      >

        <el-table-column
          type="index"
          label="序号"
          width="50%">
        </el-table-column>

        <el-table-column
          prop="tableName"
          label="表中文名"
          width=""
        >
        </el-table-column>

        <el-table-column
          label="表英文名  "
          prop="tableCode"
          width="">
        </el-table-column>

        <el-table-column
          prop="dataType"
          label="所属系统"
          width="100%">
          <template slot-scope="scope">
            <p>{{scope.row.dataType|stateForm}}</p>
          </template>
        </el-table-column>

        <el-table-column
          prop="createTime"
          label="创建时间"
          width="160%">
        </el-table-column>

        <el-table-column label="操作" width="100%">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              v-if="isAuth('ichart:datasource:bianji')"
              @click="preview(scope.row)">编辑
            </el-button>
            <el-button
              type="text"
              size="mini"
              v-if="isAuth('ichart:datasource:shanchu')"
              @click="deleteItem(scope.row,'1')">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        dialogVisible: false,
        formInline: {
          user: '',
          region: ''
        },
        page: [],
        dataSource: {
          fieldName: '',
          disableName: '',
          value: ''
        },
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        tableList: [],
        seletableName: '',
        ableCode: '',
        dataType: '',
        systemItem: []
      }
    },
    created() {
      this.seledataSource()
    },
    activated() {
      this.seledataSource()
    },
      mounted() {
      this.seledataSource();
    },
    filters: {
      stateForm(val) {
        //return val == 1 ? '摸底评价' : val == 2 ? '工单管理' : '项目管理'
        let xtVal = ''
        if(val == 1){
          xtVal = '工单系统'
        }else{
          xtVal = '仓储系统'
        }
        return xtVal
      }
    },
    methods: {
      //查询所属系统
      selectSyStemType(callback, vc) {
        if (callback) {
          this.$http({
            url: this.$http.adornUrl('/ichart/dataSource/getSyStemType'),
            method: 'get',
            params: this.$http.adornParams({})
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.systemItem = data.data;
            }
          })
        }
      },
      removeAaary(_arr, _obj) {
        var length = _arr.length;
        for (var i = 0; i < length; i++) {
          if (_arr[i] === _obj) {
            if (i === 0) {
              _arr.shift(); //删除并返回数组的第一个元素
              return _arr;
            } else if (i === length - 1) {
              _arr.pop();  //删除并返回数组的最后一个元素
              return _arr;
            } else {
              _arr.splice(i, 1); //删除下标为i的元素
              return _arr;
            }
          }
        }
      },
      // deleteItem(val, tname) {
      //   var data = this.removeAaary(this.form.dataList, val);
      //   this.form.dataList = data;
      // },

      deleteItem(val, state) {
        console.log(val)
        this.$confirm('确认是否删除该项?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          val.deleteFlag = 0
          this.enableEntity(val);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.seledataSource()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.seledataSource()
      },
      //主页删数据
      enableEntity(val) {
        // console.log(val)
        this.$http({
          url: this.$http.adornUrl('/ichart/dataSource/delete'),
          method: 'post',
          data: this.$http.adornParams(val)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.seledataSource()
          }
        })
      },
      //添加数据  开关
      addDataSource() {
        this.dialogVisible = true;
      },
      preview(val) {
        this.$router.push({name: 'datasourceUpdate', params: {data: val}});
      },
      seledataSource(val, value) {
        this.$http({
          url: this.$http.adornUrl('/ichart/dataSource/getAll'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'name': this.seletableName,
            'dataType': this.dataType,
            'type': 1
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.page = data.page;
            this.totalPage = data.page.totalCount
            this.totalPage = data.page.totalCount
          }
        })
      }
    }
  }
</script>

<style scoped>

</style>
