<template>
  <div>
    <div class="pa-title">
      <h2>审核流程</h2>
    </div>
    <task-info :task="task"></task-info>

    <div class="pa-title"><h2>问卷列表</h2></div>
    <el-tabs class="pa20" v-model="edTabName" type="card">
      <el-tab-pane
        v-for="(item,index) in tabList"
        :key="item.sheetEnName"
        :label="item.sheetName"
        :name="item.sheetEnName"
        @click="tabClick()">
        <span slot="label" style="padding: 8px">
         {{item.sheetName}}
          <!--<span style="margin-left: 10px;"><i class="el-icon-edit" @click="addOrUpdateTab(item.id)"></i></span>-->
        </span>
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="评价范围" name="1" v-if="!item.relOption || item.relOption == '[]'">
            <!--<basic-item :sid="item.id" :companyId="companyId" :task="task"></basic-item>-->
            <base-item ref="basicItem" :sid="item.id" :tid="task.tempId" :view="view"></base-item>
          </el-collapse-item>
          <el-collapse-item title="评价维度" name="3">
            <fill-item :sid="item.id" :tid="task.tempId" :view="view"></fill-item>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
    </el-tabs>


    <el-form ref="form" :model="form" label-width="80px" class="pa20" v-if="auditList">
      <el-form-item label="审核意见:">
        <el-input type="textarea" style="width:100%;" v-model="form.content"></el-input>

        <div class="pa20 text-center">
          <el-button type="primary" @click="save(6)">同意</el-button>
          <el-button type="primary" @click="save(7)">驳回</el-button>
        </div>
      </el-form-item>
    </el-form>
    <audit-record :taskItem="taskItem" :ruid="taskItem.receiveUserId" :bizType="bizType"></audit-record>
  </div>
</template>
<script>
  import basicItem from './task-list-check-base-item';
  import taskInfo from './task-list-task-info';
  import fillItem from './template/fill-item';
  import baseItem from './template/basic-item';
  import auditRecord from './task-execut-audit-record';
  import { Loading } from 'element-ui';
  export default {
    components: {
      basicItem, taskInfo, fillItem, baseItem, auditRecord
    },
    created: function () {
      // 查询任务信息
      this.task = this.$route.params.task;
      this.taskItem = this.$route.params.task;
      this.companyId = this.task.companyId; // 对应公司ID
      // 查询任务信息 evaluatetask
      this.initAuditList();
      this.getTaskStatusInfo();
      this.queryTaskInfo(this.taskItem.id)
    },
    data() {
      return {
        bizType: '3',
        view: true,
        activeName: ['1'],
        edTabName: 'second',
        tabList: [],
        task: {},
        taskItem: {},
        taskDept: {},
        companyId: '',
        auditList: [],
        form: {}
      }
    },
    methods: {
      goback(row) {
        this.$emit('closeCurrentTab')
      },
      toGL: function () {
      },
      queryTaskInfo(taskId) {
        let me = this;
        // 查询任务信息 evaluatetask
        this.$http({
          url: this.$http.adornUrl('/operational/evaluatetask/getTaskAndTempInfo'),
          method: 'get',
          params: this.$http.adornParams({taskId: taskId})
        }).then(({data}) => {
          me.task = data && data.code === 0 ? data.task : [];
          //console.log('getTaskAndTempInfo=', me.task);
          // 查询sheet列表
          this.getTabList(me.task.tempId);
        });
      },
      getTabList(tempId) {
        let me = this;
        this.$http({
          url: this.$http.adornUrl('/operational/templatesheet/select'),
          method: 'get',
          params: this.$http.adornParams({tid: tempId})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tabList = data.data;
            me.edTabName = this.tabList[0].sheetEnName;
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      getTaskStatusInfo() {
        this.$http({
          url: this.$http.adornUrl('/operational/taskdept/info/' + this.task.id),
          method: 'get',
          params: this.$http.adornParams({})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.taskDept = data.taskDept || {};
          }
        });
      },
      formatterStatus(row, column, cellValue, index) {
        return cellValue === 6 ? '同意' : '驳回';
      },

      save: function (approvalStatus) {
        if (!this.form.content) {
          (approvalStatus === 6) ? (this.form.content = '同意') : (this.form.content = '驳回')
        }
        let loadingInstance = Loading.service({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http({
          url: this.$http.adornUrl('/operational/evaluatetask/upastatus'),
          method: 'get',
          params: this.$http.adornParams({taskId: this.task.id, astatus: approvalStatus, content: this.form.content})
        }).then(({data}) => {
          loadingInstance.close()
          this.log('upastatus  =', data);
          if (data && data.code === 0) {
            this.alert('操作成功。');
          } else {
            this.$message.error(data.msg);
            this.alert('操作失败，请重试。');
          }
          this.initAuditList();
        });
      },
      initAuditList: function () {
        this.$http({
          url: this.$http.adornUrl('/operational/taskauditrecord/list'),
          method: 'get',
          params: this.$http.adornParams({taskId: this.task.id, qbasicIdIsNull: 1, orderBy: 'create_time desc'})
        }).then(({data}) => {
          this.log('upastatus  =', data);
          if (data && data.code === 0) {
            this.auditList = data.datas;
          } else {
            this.$message.error(data.msg);
          }
        });
      }
    }
  }
</script>
