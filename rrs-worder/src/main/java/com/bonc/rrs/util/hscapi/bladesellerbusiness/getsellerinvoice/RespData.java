package com.bonc.rrs.util.hscapi.bladesellerbusiness.getsellerinvoice;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 开票结果详情
 * @Author: liujunpeng
 * @Date: 2022/1/20 14:19
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class RespData implements Serializable {
    /**
     * 单据号
     */
    @JsonProperty("billNumber")
    private String billNumber;

    /**
     * 密文
     */
    @JsonProperty("cipherText")
    private String cipherText;

    /**
     * 数据来源（0 api 1 excel导入(结算单开票 ) 2 手工录入 3 发票同步 4业务单开票）
     */
    @JsonProperty("dataSource")
    private String dataSource;

    /**
     * 折扣金额
     */
    @JsonProperty("discountSum")
    private String discountSum;

    /**
     * 折扣价税合计
     */
    @JsonProperty("discountSumTax")
    private String discountSumTax;

    /**
     * 折扣税额
     */
    @JsonProperty("discountTax")
    private String discountTax;

    /**
     * 开票人
     */
    @JsonProperty("drawer")
    private String drawer;

    /**
     * 失败原因
     */
    @JsonProperty("failReason")
    private String failReason;

    /**
     * 0-业务单生成的发票 其他的为空
     */
    @JsonProperty("flag")
    private String flag;

    /**
     * 发票代码
     */
    @JsonProperty("invoiceCode")
    private String invoiceCode;

    /**
     * 发票号码
     */
    @JsonProperty("invoiceNo")
    private String invoiceNo;

    /**
     * 发票状态（0 正常 1作废 2 红冲）
     */
    @JsonProperty("invoiceState")
    private String invoiceState;

    /**
     * 开票日期
     */
    @JsonProperty("invoiceDate")
    private String invoiceDate;

    /**
     * 发票类型
     */
    @JsonProperty("invoiceType")
    private String invoiceType;

    /**
     * 发票下载地址
     */
    @JsonProperty("invoiceUrl")
    private String invoiceUrl;

    /**
     * 是否打印
     */
    @JsonProperty("isPrint")
    private String isPrint;

    /**
     * 邮箱
     */
    @JsonProperty("mail")
    private String mail;

    /**
     * 开票方式（0 金税盘 1税控盘 2税神通 3电子发票平台 4简易税控盘）
     */
    @JsonProperty("makeInvoiceMode")
    private String makeInvoiceMode;

    /**
     * 开票状态（0 待开 1发送开票 2已开 3 开票失败）
     */
    @JsonProperty("makeInvoiceState")
    private String makeInvoiceState;

    /**
     * 开票类型（0 蓝票 1红票）
     */
    @JsonProperty("makeInvoiceType")
    private String makeInvoiceType;

    /**
     * 原发票代码
     */
    @JsonProperty("oldInvoiceCode")
    private String oldInvoiceCode;

    /**
     * 原发票号码
     */
    @JsonProperty("oldInvoiceNo")
    private String oldInvoiceNo;

    /**
     * 收款人
     */
    @JsonProperty("payee")
    private String payee;

    /**
     * 手机号
     */
    @JsonProperty("phone")
    private String phone;

    /**
     * 打印状态（0 未打印 1已打印）
     */
    @JsonProperty("printState")
    private String printState;

    /**
     * 购方地址电话
     */
    @JsonProperty("purchaserAddressTel")
    private String purchaserAddressTel;

    /**
     * 购方银行账号
     */
    @JsonProperty("purchaserBankAccount")
    private String purchaserBankAccount;

    /**
     * 购方编号
     */
    @JsonProperty("purchaserCode")
    private String purchaserCode;

    /**
     * 购方名称
     */
    @JsonProperty("purchaserName")
    private String purchaserName;

    /**
     * 购方税号
     */
    @JsonProperty("purchaserTaxNo")
    private String purchaserTaxNo;

    /**
     * 红字申请编号
     */
    @JsonProperty("redInfoNo")
    private String redInfoNo;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 复核人
     */
    @JsonProperty("reviewer")
    private String reviewer;

    /**
     * 票税云单号
     */
    @JsonProperty("serialNo")
    private String serialNo;

    /**
     * 销方地址电话
     */
    @JsonProperty("sellerAddressTel")
    private String sellerAddressTel;

    /**
     * 销方银行账号
     */
    @JsonProperty("sellerBankAccount")
    private String sellerBankAccount;

    /**
     * 销方编号
     */
    @JsonProperty("sellerCode")
    private String sellerCode;

    /**
     * 销方名称
     */
    @JsonProperty("sellerName")
    private String sellerName;

    /**
     * 销方税号
     */
    @JsonProperty("sellerTaxNo")
    private String sellerTaxNo;

    /**
     * 来源系统
     */
    @JsonProperty("sourceSystem")
    private String sourceSystem;

    /**
     * 合计金额
     */
    @JsonProperty("totalSum")
    private String totalSum;

    /**
     * 价税合计
     */
    @JsonProperty("totalSumTax")
    private String totalSumTax;

    /**
     * 合计税额
     */
    @JsonProperty("totalTax")
    private String totalTax;

    /**
     * 校验码
     */
    @JsonProperty("checkCode")
    private String checkCode;

    /**
     * 明细对象集合
     */
    @JsonProperty("sellerInvoiceDetailList")
    private List<RespSellerInvoiceDetail> sellerInvoiceDetailList;
}
