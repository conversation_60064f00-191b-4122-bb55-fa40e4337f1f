package com.bonc.rrs.gace.task;

import com.bonc.rrs.gace.config.GaceConfig;
import com.bonc.rrs.gace.service.impl.GaceInstallPullService;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * {@code @Description} 推送订单入库定时任务
 * {@code @Date} 2024/04/3 10:06
 */
@Slf4j
@Component("gacePullOrderTask")
public class GacePullOrderTask implements ITask {

    @Resource
    private GaceInstallPullService gaceInstallPullService;

    @Resource
    private GaceConfig gaceConfig;

    @Override
    public void run(String params) {
        log.info("推送订单入库定时任务 ---- 开始");
        String startTimeStr = LocalDateTime.now().minusHours(gaceConfig.getQueryHours()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        log.info("开始时间{},结束时间:{}", startTimeStr, endTimeStr);
        gaceInstallPullService.pullOrders(startTimeStr, endTimeStr, null);
        log.info("推送订单入库定时任务 ---- 结束");
    }

}
