package com.bonc.rrs.workManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;

import java.util.Date;

public class OperationRecord {
    private Integer id;

    private Long userId;

    private String operationUser;

    private Date createTime;

    private String record;

    private Long affectedUserId;

    private String affectedUser;

    private Integer type;

    private String worderNo;

    private Integer acceptSend;

    private String dotCode;

    private String worderStatus;
    private String worderExecStatus;
    @TableField(exist = false)
    private Long worderId;
    private Integer typeSendOrder;
    /**
     * 改派原因枚举
     */
    private Integer causeEnum;
    /**
     * 改派详细原因
     */
    private String modifyCause;

    /**
     * 类型：1：派单，2：改派
     */
    private Integer sendType;

    /**
     * 补充说明 app
     */
    private String title;

    public OperationRecord(Integer id, Long userId, String operationUser, Date createTime, String record, Long affectedUserId, String affectedUser, Integer type, String worderNo, Integer acceptSend, String dotCode, String worderStatus, String worderExecStatus, Long worderId, Integer typeSendOrder, Integer causeEnum, String modifyCause, Integer sendType, String title) {
        this.id = id;
        this.userId = userId;
        this.operationUser = operationUser;
        this.createTime = createTime;
        this.record = record;
        this.affectedUserId = affectedUserId;
        this.affectedUser = affectedUser;
        this.type = type;
        this.worderNo = worderNo;
        this.acceptSend = acceptSend;
        this.dotCode = dotCode;
        this.worderStatus = worderStatus;
        this.worderExecStatus = worderExecStatus;
        this.worderId = worderId;
        this.typeSendOrder = typeSendOrder;
        this.causeEnum = causeEnum;
        this.modifyCause = modifyCause;
        this.sendType = sendType;
        this.title = title;
    }

    public Integer getTypeSendOrder() {
        return typeSendOrder;
    }

    public void setTypeSendOrder(Integer typeSendOrder) {
        this.typeSendOrder = typeSendOrder;
    }

    public String getWorderStatus() {
        return worderStatus;
    }

    public void setWorderStatus(String worderStatus) {
        this.worderStatus = worderStatus;
    }

    public String getWorderExecStatus() {
        return worderExecStatus;
    }

    public void setWorderExecStatus(String worderExecStatus) {
        this.worderExecStatus = worderExecStatus;
    }

    public OperationRecord(Long userId, String operationUser, String record, Long affectedUserId, String affectedUser, String worderNo, String dotCode) {
        this.userId = userId;
        this.operationUser = operationUser;
        this.record = record;
        this.affectedUserId = affectedUserId;
        this.affectedUser = affectedUser;
        this.worderNo = worderNo;
        this.dotCode = dotCode;
    }

    public OperationRecord(Long userId, String operationUser, String record, String worderNo) {
        this.userId = userId;
        this.operationUser = operationUser;
        this.record = record;
        this.worderNo = worderNo;
    }

    public OperationRecord(String operationUser, String record, Long affectedUserId, String affectedUser, Integer type, String worderNo, String worderStatus, String worderExecStatus) {
        this.operationUser = operationUser;
        this.record = record;
        this.affectedUserId = affectedUserId;
        this.affectedUser = affectedUser;
        this.type = type;
        this.worderNo = worderNo;
        this.worderStatus = worderStatus;
        this.worderExecStatus = worderExecStatus;
    }

    public OperationRecord(Integer id, Long userId, String operationUser, Date createTime, String record, Long affectedUserId, String affectedUser, Integer type, String worderNo, Integer acceptSend) {
        this.id = id;
        this.userId = userId;
        this.operationUser = operationUser;
        this.createTime = createTime;
        this.record = record;
        this.affectedUserId = affectedUserId;
        this.affectedUser = affectedUser;
        this.type = type;
        this.worderNo = worderNo;
        this.acceptSend = acceptSend;
    }

    public OperationRecord() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOperationUser() {
        return operationUser;
    }

    public void setOperationUser(String operationUser) {
        this.operationUser = operationUser == null ? null : operationUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record == null ? null : record.trim();
    }

    public Long getAffectedUserId() {
        return affectedUserId;
    }

    public void setAffectedUserId(Long affectedUserId) {
        this.affectedUserId = affectedUserId;
    }

    public String getAffectedUser() {
        return affectedUser;
    }

    public void setAffectedUser(String affectedUser) {
        this.affectedUser = affectedUser == null ? null : affectedUser.trim();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getWorderNo() {
        return worderNo;
    }

    public void setWorderNo(String worderNo) {
        this.worderNo = worderNo == null ? null : worderNo.trim();
    }

    public Integer getAcceptSend() {
        return acceptSend;
    }

    public void setAcceptSend(Integer acceptSend) {
        this.acceptSend = acceptSend;
    }

    public String getDotCode() {
        return dotCode;
    }

    public void setDotCode(String dotCode) {
        this.dotCode = dotCode;
    }
    public Long getWorderId() {
        return worderId;
    }

    public void setWorderId(Long worderId) {
        this.worderId = worderId;
    }

    public Integer getCauseEnum() {
        return causeEnum;
    }

    public void setCauseEnum(Integer causeEnum) {
        this.causeEnum = causeEnum;
    }

    public String getModifyCause() {
        return modifyCause;
    }

    public void setModifyCause(String modifyCause) {
        this.modifyCause = modifyCause;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}