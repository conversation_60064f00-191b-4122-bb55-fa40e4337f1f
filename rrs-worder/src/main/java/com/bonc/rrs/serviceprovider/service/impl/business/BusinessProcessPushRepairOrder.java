package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushRepairOrderBody;
import com.bonc.rrs.byd.domain.PushRepairOrderData;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 服务商业务-报修订单信息推送服务商
 * @Date 2024/2/26 18:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushRepairOrder extends AbstractPushOrder {

    final BrandService brandService;

    final WorderTemplateService worderTemplateService;

    final SysDictionaryDetailService sysDictionaryDetailService;

    @Override
    public String getProcessCode() {
        return "pushRepairOrder";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        Integer worderTypeId = businessProcessPo.getWorderTypeId();
        PushRepairOrderBody pushRepairOrderBody = businessProcessPo.getPushRepairOrderBody();
        List<PushRepairOrderData> data = pushRepairOrderBody.getData();

        List<Object> errorDataList = new ArrayList<>();
        for (PushRepairOrderData pushRepairOrderData : data) {
            log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " start");
            R r = null;
            try {
                BrandEntity brandEntity = brandService.queryBrandByCompanyBrandId(pushRepairOrderData.getCarBrand());
                if (brandEntity == null) {
                    log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 非法品牌");
                    addError(pushRepairOrderData.getOrderCode(), "非法品牌", errorDataList);
                    continue;
                }

                RegionCode regionCode = convertRegion(pushRepairOrderData.getProvinceCode(), pushRepairOrderData.getCityCode(), pushRepairOrderData.getAreaCode());

                if (regionCode.getProvinceCode() == null || regionCode.getCityCode() == null || regionCode.getAreaCode() == null) {
                    log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 地区不匹配");
                    addError(pushRepairOrderData.getOrderCode(), "地区不匹配", errorDataList);
                    continue;
                }

                List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(brandEntity.getId(), worderTypeId, regionCode.getProvinceCode().intValue(), regionCode.getCityCode().intValue());
                if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
                    log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 没有对应的工单模板");
                    addError(pushRepairOrderData.getOrderCode(), "没有对应的工单模板", errorDataList);
                    continue;
                }
                String requirmentType = pushRepairOrderData.getRequirmentType();
                //报修取最新的模版
                if ("10".equals(requirmentType)) {
                    worderTemplateDtoList.sort(Comparator.comparing(WorderTemplateDto::getTemplateId).reversed());
                }
                WorderTemplateDto worderTemplateDto = worderTemplateDtoList.get(0);

                if (validateCompanyOrderNumberAndBrandExsit(pushRepairOrderData.getOrderCode(), worderTemplateDto.getTemplateId())) {
                    log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 车企订单号已存在，无法创建报修工单");
//                    addError(pushRepairOrderData.getOrderCode(), "车企订单号已存在，无法创建报修工单", errorDataList);
                    continue;
                }

                Integer companyId = 516;

                WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

                String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + pushRepairOrderData.getContactAddress();

                String installOrderCode = pushRepairOrderData.getInstallOrderCode();
                String wallboxName = pushRepairOrderData.getWallboxName();
                String wallboxPower = pushRepairOrderData.getWallboxPower();

                String requirmentTypeName = sysDictionaryDetailService.getDetailName("byd_requirment_type", requirmentType);
                String dispatchTime = pushRepairOrderData.getDispatchTime();

                String orderTag = sysDictionaryDetailService.getDetailName("byd_order_tag", pushRepairOrderData.getType());

                String bydCarSeries = pushRepairOrderData.getCarSeries();
                String bydCarModel = pushRepairOrderData.getCarModel();

                String vin = pushRepairOrderData.getVin();
                String remark = pushRepairOrderData.getRemark();
                String orderCode = pushRepairOrderData.getOrderCode();

                //是否全国联保订单: 0-否 1-是
                String isCountryWarranty = pushRepairOrderData.getGlobalGuarantee();
                //是否可维护: 0-否 1-是
                String isMaintainable = pushRepairOrderData.getMaintenanceStatus();

                worderInfoEntity.setIsMaintainable(YesOrNo.getValueByCode(isMaintainable));

                worderInfoEntity.setPushOrderWorderSource(businessProcessPo.getOrderSouce());
                worderInfoEntity.setUserName(pushRepairOrderData.getContactName());
                worderInfoEntity.setUserPhone(pushRepairOrderData.getContactMobile());
                worderInfoEntity.setAddress(address);
                worderInfoEntity.setCompanyOrderNumber(orderCode);
                worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

                worderInfoEntity.setCarBrand(brandEntity.getId() + "");
                worderInfoEntity.setCarModel("4");
                worderInfoEntity.setCompanyId(companyId);

                worderInfoEntity.setPostcode("");
                worderInfoEntity.setWorderSourceTypeValue("");
                worderInfoEntity.setWorderTypeId(worderTypeId);

                worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
                worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

                List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

                worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", worderInfoEntity.getWorderTypeId()));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", companyId));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", dispatchTime));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(147, "安装单号", installOrderCode));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", ""));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", worderInfoEntity.getUserName()));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderInfoEntity.getUserPhone()));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", wallboxName));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(1682, "比亚迪-品牌", brandEntity.getBrandName()));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(1684, "比亚迪车系", bydCarSeries));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(1686, "比亚迪车型", bydCarModel));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", wallboxPower));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(1697, "售后类型", requirmentTypeName));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(1698, "标签", orderTag));

                worderExtFieldEntityList.add(setWorderExtFieldEntity(1705, "车架号", vin));
                worderExtFieldEntityList.add(setWorderExtFieldEntity(1707, "故障原因", remark));

                //TODO 是否全国联保订单: 0-否 1-是
                worderExtFieldEntityList.add(setWorderExtFieldEntity(2086, "是否全国联保订单", YesOrNo.getValueByCode(isCountryWarranty)));
                //TODO 是否可维护: 0-否 1-是
                worderExtFieldEntityList.add(setWorderExtFieldEntity(2087, "是否可维护", YesOrNo.getValueByCode(isMaintainable)));

                worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

                r = saveWorderInformation(worderInfoEntity);

            } catch (Exception e) {
                log.error("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " 出现异常", e);
                addError(pushRepairOrderData.getOrderCode(), "下单失败，" + e.toString(), errorDataList);
                continue;
            }
            if(!IntegerEnum.ZERO.getValue().equals(r.get(WarningConstant.CODE))){
                log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + r.get("msg"));
                addError(pushRepairOrderData.getOrderCode(), r.get("msg") + "", errorDataList);
                continue;
            }
            String worderNo = r.get("worderNo") + "";
            // 自动派单
            goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME);
            log.info("pushRepairOrder " + pushRepairOrderData.getOrderCode() + " end");
        }

        PushApiResponse pushApiResponse;
        if (errorDataList.size() > 0) {
            pushApiResponse = new PushApiResponse("partially success");
            pushApiResponse.setData(errorDataList);
        } else {
            pushApiResponse = new PushApiResponse("success");
        }

        return Result.pushApiResponse(pushApiResponse);
    }

    //创建枚举类：是--1，否--0
    @Getter
    private enum YesOrNo {
        YES("1", "是"),
        NO("0", "否");
        private final String code;
        private final String value;
        YesOrNo(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValueByCode(String code) {
            for (BusinessProcessPushRepairOrder.YesOrNo yesOrNo : BusinessProcessPushRepairOrder.YesOrNo.values()) {
                if (yesOrNo.getCode().equals(code)) {
                    return yesOrNo.getValue();
                }
            }
            return null;
        }
    }
}
