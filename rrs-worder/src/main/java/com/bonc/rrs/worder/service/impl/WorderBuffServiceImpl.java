package com.bonc.rrs.worder.service.impl;

import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worder.service.WorderBuffService;
import com.bonc.rrs.worderapp.entity.po.FieldPo;
import com.bonc.rrs.worderapp.entity.vo.FieldVo;
import com.bonc.rrs.worderapp.service.WorderOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorderBuffServiceImpl implements WorderBuffService {

    private final WorderExtFieldService worderExtFieldService;
    private final WorderOrderService worderOrderService;


    @Override
    public void saveConveyFieldsInAheadWithDefaultValue(String worderNo) {
        List<Long> allFieldsTemp = worderExtFieldService.getFieldIdsByWorderNoAndPurpose(worderNo, "2");
        List<String> allFields = allFieldsTemp.stream().map(String::valueOf).collect(Collectors.toList());

        // 字段
        List<FieldPo> textFields = worderOrderService.listConveyField(worderNo);
        Map<String, FieldPo> collect1 = textFields.stream().collect(Collectors.toMap(FieldPo::getFieldId, Function.identity(), (e1, e2) -> e1));
        List<FieldVo> collect = collect1.values().stream()
                .filter(e -> !allFields.contains(e.getFieldId()))
                .map(e -> {
                    FieldVo fieldVo = new FieldVo();
                    fieldVo.setWorderNo(worderNo);
                    fieldVo.setFieldId(e.getFieldId());
                    fieldVo.setFieldName(e.getFieldName());
                    fieldVo.setFieldValue("");
                    return fieldVo;
                }).collect(Collectors.toList());
        try {
            for (FieldVo fieldVo : collect) {
                worderOrderService.saveWorderFieldInfo(fieldVo);
            }
            log.info("提前保存勘测字段:{}, fieldIds:{}", worderNo, collect.stream().map(FieldVo::getFieldId).collect(Collectors.joining(",")));
        } catch (Exception e) {
            log.error("提前保存勘测字段失败, {}", collect, e);
        }

        // 资料
        List<FieldPo> dataFields = worderOrderService.listConveyDataField(worderNo);
        Map<String, FieldPo> collect2 = dataFields.stream().collect(Collectors.toMap(FieldPo::getFieldId, Function.identity(), (e1, e2) -> e1));
        List<FieldVo> collect3 = collect2.values().stream()
                .filter(e -> !allFields.contains(e.getFieldId()))
                .map(e -> {
                    FieldVo fieldVo = new FieldVo();
                    fieldVo.setWorderNo(worderNo);
                    fieldVo.setFieldId(e.getFieldId());
                    fieldVo.setFieldName(e.getFieldName());
                    fieldVo.setFieldValue("");
                    return fieldVo;
                }).collect(Collectors.toList());

        try {
            for (FieldVo fieldVo : collect3) {
                worderOrderService.saveWorderFieldInfo(fieldVo);
            }
            log.info("提前保存勘测资料:{}, fieldIds:{}", worderNo, collect3.stream().map(FieldVo::getFieldId).collect(Collectors.joining(",")));
        } catch (Exception e) {
            log.error("提前保存勘测资料失败, {}", collect3, e);
        }
    }

    @Override
    public void saveInstallFieldsInAheadWithDefaultValue(String worderNo) {
        List<Long> allFieldsTemp = worderExtFieldService.getFieldIdsByWorderNoAndPurpose(worderNo, "3");
        List<String> allFields = allFieldsTemp.stream().map(String::valueOf).collect(Collectors.toList());

        // 字段
        List<FieldPo> textFields = worderOrderService.listInstallField(worderNo);
        Map<String, FieldPo> collect1 = textFields.stream().collect(Collectors.toMap(FieldPo::getFieldId, Function.identity(), (e1, e2) -> e1));
        List<FieldVo> collect = collect1.values().stream()
                .filter(e -> !allFields.contains(e.getFieldId()))
                .map(e -> {
                    FieldVo fieldVo = new FieldVo();
                    fieldVo.setWorderNo(worderNo);
                    fieldVo.setFieldId(e.getFieldId());
                    fieldVo.setFieldName(e.getFieldName());
                    fieldVo.setFieldValue("");
                    return fieldVo;
                }).collect(Collectors.toList());

        try {
            for (FieldVo fieldVo : collect) {
                worderOrderService.saveWorderFieldInfo(fieldVo);
            }
            log.info("提前保存安装字段:{}, fieldIds:{}", worderNo, collect.stream().map(FieldVo::getFieldId).collect(Collectors.joining(",")));
        } catch (Exception e) {
            log.error("提前保存安装字段失败, {}", collect, e);
        }

        // 资料
        List<FieldPo> dataFields = worderOrderService.listInstallDataField(worderNo);
        Map<String, FieldPo> collect2 = dataFields.stream().collect(Collectors.toMap(FieldPo::getFieldId, Function.identity(), (e1, e2) -> e1));
        List<FieldVo> collect3 = collect2.values().stream()
                .filter(e -> !allFields.contains(e.getFieldId()))
                .map(e -> {
                    FieldVo fieldVo = new FieldVo();
                    fieldVo.setWorderNo(worderNo);
                    fieldVo.setFieldId(e.getFieldId());
                    fieldVo.setFieldName(e.getFieldName());
                    fieldVo.setFieldValue("");
                    return fieldVo;
                }).collect(Collectors.toList());

        try {
            for (FieldVo fieldVo : collect3) {
                worderOrderService.saveWorderFieldInfo(fieldVo);
            }
            log.info("提前保存安装资料:{}, fieldIds:{}", worderNo, collect3.stream().map(FieldVo::getFieldId).collect(Collectors.joining(",")));
        } catch (Exception e) {
            log.error("提前保存安装资料失败, {}", collect3, e);
        }
    }
}
