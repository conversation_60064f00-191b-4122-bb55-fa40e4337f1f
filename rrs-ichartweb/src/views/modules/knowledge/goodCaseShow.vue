<template>
  <div class="mod-knowledge-gooodCaseShow" style="width: 100%">
    <div style="width: 100%;margin: auto">
      <div style="margin: auto;">
        <el-row class="row-bg row-query-area">
          <div ref="img0">
            <el-carousel trigger="click" :height="dynamicHeight">
              <el-carousel-item v-for="item in page.list" :key="item.id">
                <el-row>
                  <el-col :span="24">
                      <img :src="item.bussiIdAddress" @click="showRes(item)" style="width: 100%"/>
                  </el-col>
                </el-row>
              </el-carousel-item>
            </el-carousel>
            <!--        <el-carousel :interval="3000" type="card" height="400px" ref="carousel">-->
            <!--          <el-carousel-item v-for="(item,index) in page.list" :key="item.id" >-->
            <!--            <img ref="" :src="item.bussiIdAddress" @click="showRes(item)"  style="width: 100%;height: 100%"/>-->
            <!--          </el-carousel-item>-->
            <!--        </el-carousel>-->
          </div>
        </el-row>
      </div>
      <el-row class="row-bg row-query-separator" style="display: none"></el-row>
    </div>
    <div style="width: 100%;margin: auto">
      <el-row>
        <!--数据-->
        <el-table
          :data="page.list"
          ref="multipleTable"
          tooltip-effect="dark"
          style="width: 100%;margin-top: 20px;"
          border
          :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
        >
          <el-table-column
            type="index"
            label="序号"
            width="70">
          </el-table-column>

          <el-table-column
            prop="title"
            label="标题"
          >
          </el-table-column>

          <el-table-column
            label="创建时间"
            prop="updateTime"
            width="180%">
          </el-table-column>


          <el-table-column label="操作" width="80%">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                v-if="isAuth('knowledge:goodCaseShow:yulan')"
                @click="showRes(scope.row)">预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageIndex"
          :page-sizes="[6, 20, 50, 100]"
          :page-size="pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </el-row>
    </div>
  </div>
</template>

<script>
  // import UeDitorGoodcase from '../../demo/ueditor-goodcase';

  export default {
    data() {
      return {
        ruleForm: {
          pass: '',
          checkPass: '',
          age: ''
        },
        disabledFlag: false,
        data: {},
        dynamicHeight: '500px',
        upLoadData: {
          moduleId: 0
        },
        fileList: [],
        uploadUrl: this.$http.adornUrl('/system/sys/attach/upload'),
        dataList: [],
        token: {token: this.$cookie.get('token')},
        title: '',
        value: '',
        pageIndex: 1,
        pageSize: 6,
        totalPage: 0,
        page: [],
        dialogVisible: false
      }
    },
    created() {
      this.$destroy();
      this.selegoodcase()
    },
    components: {
      // UeDitorGoodcase
    },
    methods: {
      setActiveItem(name) {
        this.$refs.carousel.setActiveItem(name);
      },
      handlePreview(file) {
        this.$downloadFileById(file.id, null, () => {
          this.submitted = false;
        });
      },
      showRes(val) {
        this.$router.push({name: 'goodcaseShow', params: {data: val}});
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.selegoodcase()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.selegoodcase()
        window.ue = this;
      },
      selegoodcase(val, value) {
        // console.log(val,value)
        this.$http({
          url: this.$http.adornUrl('/kb/goodcase/getGoodCaseBydeptid'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'title': this.title,
            'type': 1
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.page = data.page;
            if (this.page.list && this.page.list.length > 0) {
              for (const tdata of this.page.list) {
                let path = tdata.oldPath;
                if (path && path !== undefined) {
                  path = path.replace(/\\/g, '/');
                  path = path.replace(/(.*\/)(\d{4}\/\d{2}.*)/g, '$2');
                  tdata.bussiIdAddress = window.SITE_CONFIG.baseUrl + '/img/' + path;
                } else {
                  tdata.bussiIdAddress = '';
                }
              }
            }
            this.totalPage = data.page.totalCount
            if (this.page.list.length === 0 && this.page.currPage !== 1) {
              this.pageIndex = this.page.currPage - 1
              this.selegoodcase();
            }
          }
          this.$nextTick(() => {
            this.dynamicHeight = (this.$refs.img0.offsetWidth / 3) + 'px';
          })
        })
      }
    }
  }
</script>

<style scoped>
  .demo-table-expand {
    font-size: 0;
  }

  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }

  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin-left: 350px;
    margin-bottom: 100px;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 90px;
    height: 90px;
    line-height: 90px;
    text-align: center;
  }

  .avatar {
    width: 78px;
    height: 78px;
    display: block;
  }

  .el-carousel__item h3 {
    color: #475669;
    font-size: 18px;
    opacity: 0.75;
    line-height: 300px;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .el-carousel__item:nth-child(2n+1) {
    background-color: #d3dce6;
  }

  .el-header, .el-footer {
    background-color: #B3C0D1;
    color: #333;
    text-align: center;
    line-height: 60px;
  }

  .el-aside {
    background-color: #D3DCE6;
    color: #333;
    text-align: center;
    line-height: 200px;
  }

  .el-main {
    background-color: #E9EEF3;
    color: #333;
    text-align: center;
    line-height: 160px;
  }

  body > .el-container {
    margin-bottom: 40px;
  }

  .el-container:nth-child(5) .el-aside,
  .el-container:nth-child(6) .el-aside {
    line-height: 260px;
  }

  .el-container:nth-child(7) .el-aside {
    line-height: 320px;
  }

  .el-carousel__item img {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 200px;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .el-carousel__item:nth-child(2n+1) {
    background-color: #d3dce6;
  }

  .el-scrollbar {
    margin-right: 100px;
  }

  .el-scrollbar__wrap {
    margin-right: 100px;
  }

  .row-query-separator {
    margin-top: 20px;
  }
</style>
