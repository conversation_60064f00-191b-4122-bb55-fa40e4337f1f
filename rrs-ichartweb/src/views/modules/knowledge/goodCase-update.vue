<template>
  <div>
    <el-form :model="goodFrom" status-icon label-width="100px" class="demo-ruleForm" :rules="dataRule" ref="goodFrom">
      <el-form-item label="标题" prop="title">
        <el-col :span="14">
          <el-input type="text" v-model="goodFrom.title" autocomplete="off" placeholder="请输入内容"></el-input>
        </el-col>
      </el-form-item>

      <el-form-item :offset="1" label="机构" label-width="" prop="deptId">
        <el-col :span="14">
          <common-async-mgtree @validChange="mgdeptidClock" :asyncdeptName="this.goodFrom.deptName" :mgr="1"
                               :msgDeptId="this.goodFrom.deptIdList"></common-async-mgtree>
        </el-col>
      </el-form-item>

      <el-form-item label="图片">
        <img-cutter
          ref="imgCutter"
          :label="'选择图片'"
          :boxWidth="1200"
          :boxHeight="400"
          v-on:cutDown="cudDown">
          <div class="btn btn-primary" slot="open" @click="handleOpen()">选择图片</div>
        </img-cutter>
        <img :src="imgDataUrl" v-if="imgDataUrl" style="width: 300px;height: 100px;margin-top: 10px;">
      </el-form-item>

      <el-form-item label="文件" prop="bussiId">
        <common-upload
          ref="commonUpload"
          size="mini"
          :moduleId="8"
          v-model="goodFrom.bussiId">
        </common-upload>
      </el-form-item>
      <div ref="editor" style="text-align:left">
      </div>
      <p style="margin-bottom: -30px;
      margin-left: 50px;color: red">*</p>
      <el-form-item label="内容" prop="">
        <div ref="editor" style="text-align:left">
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveEntity(goodFrom)" style="margin-top: 30px;margin-left: 50%">保存
        </el-button>
        <el-button type="warning" @click="closeThisTab" style="margin-top: 30px;;margin-left: 10px">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import UploadImages from '@/components/dept/UploadImages2'
  import imgCutter from '@/components/dept/ImgCutter'
  import E from 'wangeditor'
  import commonUpload from '@/components/upload/common-upload2'
  import commonUploadImg from '@/components/upload/common-upload-img'
  import commonMgtree from '@/components/dept/common-mgtree'
  import commonAsyncMgtree from '@/components/dept/common-async-mgtree'
  // import VueCropper from './vue-cropper'

  export default {
    name: 'goodCase-update',
    data() {
      let validateAttachFile = (rule, value, callback) => {
        if (!this.$refs.commonUploadimg.uploadData.id) {
          callback(new Error('请上传附件'))
        } else {
          callback()
        }
      };
      return {
        imgDataUrl: '', // the datebase64 url of created image
        deptList: [],
        goodFrom: {
          title: '',
          deptIdList: [],
          bussiIdPic: '',
          template: '',
          imgDataUrl: '',
          deptId: ''
        },
        editorContent: '',
        dataRule: {
          title: [
            {required: true, message: '标题不能为空', trigger: 'blur'}
          ],
          bussiIdPic: [
            {required: true, message: '请上传附件!', trigger: 'change'},
            {validator: validateAttachFile, message: '请上传附件', trigger: 'change'}
          ],
          deptId: [
            {required: true, message: '请选择机构!', trigger: 'change'}
          ]
        }
      }
    },
    created() {
      this.$destroy();
      if (this.$route.params.data.id) {
        this.goodFrom = this.$route.params.data;
        console.log('--------------->strAry', this.goodFrom.deptName)
        this.editorContent = this.goodFrom.template;
        this.imgDataUrl = this.goodFrom.bussiIdAddress
      }
    },
    components: {
      commonUpload,
      commonUploadImg,
      commonMgtree,
      E,
      commonAsyncMgtree,
      // VueCropper,
      UploadImages,
      imgCutter
    },
    methods: {
      mgdeptidClock(val) {
        let str = val.join();
        this.goodFrom.deptId = str
      },
      handleOpen() {
        this.$refs.imgCutter.handleOpen();
      },
      cudDown(res) {
        
        this.imgDataUrl = res.dataURL;
        
        // console.log("----------this.imgDataUrl---->", this.imgDataUrl)
      },
      //上传 附件
      handleUploadSuccess(data) {

      },
      closeWin() {
        let oldPath = this.goodFrom.oldPath;
        let bussiIdPic = this.goodFrom.bussiIdPic;
        let fileidListid = this.goodFrom.fileidList;
        let id = this.goodFrom.id;
        if (id === undefined) {
          this.closeThisTab()
        }
        // console.log("--222--->", this.goodFrom)
        this.$http({
          url: this.$http.adornUrl('/sys/attach/updateAttach'),
          method: 'get',
          params: this.$http.adornParams({bussiIdPic: bussiIdPic, oldPath: oldPath, fileidListid: fileidListid, id: id})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.closeThisTab()
          }
        })
      },
      closeThisTab() {
        let com = this;
        let i = 0;
        while (!com.tabsCloseCurrentHandle && i < 20) {
          com = com.$parent;
          i++;
        }
        com.tabsCloseCurrentHandle();
      },
      validateUpload() {
        this.$refs.goodFrom.validateField('bussiIdPic');
      },
      saveEntity(val) {
        this.$refs['goodFrom'].validate((valid) => {
          if (valid) {
            val.imgDataUrl = this.imgDataUrl;
            if (val.imgDataUrl.length > 0) {
              this.save(val);
            } else {
              this.$message({
                message: '请上传图片后保存！',
                type: 'warning'
              });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      save(val) {
        // 获取模板
        val.bussiIdPic = this.imgDataUrl
        val.template = this.editorContent;
        this.$http({
          url: this.$http.adornUrl('/kb/goodcase/save'),
          method: 'post',
          data: this.$http.adornParams(val)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.closeThisTab();
          }
        })
      }
    },
    mounted() {
      this.editor = new E(this.$refs.editor)
      this.editor.customConfig.onchange = (html) => {
        this.editorContent = html
      }
      this.editor.customConfig.uploadImgServer = this.$http.adornUrl(`/system/sys/attach/upload?moduleId=8`)
      this.editor.customConfig.uploadImgHeaders = {token: this.$cookie.get('token')};
      this.editor.customConfig.uploadFileName = 'file'; // 后端接受上传文件的参数名
      this.editor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024; // 将图片大小限制为 2M
      this.editor.customConfig.uploadImgMaxLength = 6; // 限制一次最多上传 3 张图片
      this.editor.customConfig.uploadImgTimeout = 3 * 60 * 1000; // 设置超时时间
      this.editor.customConfig.zindex = 20000;
      this.editor.customConfig.uploadImgHooks = {
        customInsert: function (insertImg, result, editor) {
          var path = result.file;
          path = path.replace(/\\/g, '/');
          path = path.replace(/(.*\/)(\d{4}\/\d{2}.*)/g, '$2');
          //需要获取实际的地址
          let url = window.SITE_CONFIG.baseUrl + '/img/' + path;
          insertImg(url);
        }
      }
      this.editor.customConfig.menus = [
        'head',  // 标题
        'bold',  // 粗体
        'fontSize',  // 字号
        'fontName',  // 字体
        'italic',  // 斜体
        'underline',  // 下划线
        'strikeThrough',  // 删除线
        'foreColor',  // 文字颜色
        'backColor',  // 背景颜色
        'list',  // 列表
        'justify',  // 对齐方式
        // 'quote',  // 引用
        'emoticon',  // 表情
        'image'  // 插入图片
      ]
      this.editor.create()
      this.editor.txt.html(this.goodFrom.template)
    }
  }
</script>

<style>
  ol {
    list-style-type: none;
    counter-reset: sectioncounter;
    width: 100%;
  }

  ol li:before {
    content: counter(sectioncounter) ".";
    counter-increment: sectioncounter;
  }

  ul {
    list-style-type: disc;
  }

  .avatar-uploader .el-upload {
    margin-left: 0px !important;
    margin-bottom: 0px !important;
  }

  /*.el-scrollbar__wrap {*/
  /*  overflow: scroll;*/
  /*  height: 100%;*/
  /*  width: 270px;*/
  /*}*/

  .w-e-menu {
    z-index: 2 !important;
  }

  .w-e-text-container {
    z-index: 1 !important;
  }

  .site-sidebar {
    z-index: 0
  }

  /*.el-form-item__content{*/
  /*  text-align: right;*/
  /*}*/
</style>
