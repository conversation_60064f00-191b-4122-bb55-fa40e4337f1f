package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.rrs.worder.aspect.DicMarker;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 扩展费用字段表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:59
 */
@Data
@TableName("ext_cost")
@ApiModel(value = "扩展费用字段表")
public class ExtCostEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId
    @ApiModelProperty(value = "自增主键", required = false)
    private Integer costId;
    /**
     * 费用名称
     */
    @ApiModelProperty(value = "费用名称", required = false)
    private String contName;
    /**
     * 费用类型1、常规2、其它
     */
    @ApiModelProperty(value = "费用类型（数据字典cost_type）", required = false)
    @DicMarker(key = "cost_type")
    private Integer costType;
    /**
     * 费用单位1、元
     */
    @ApiModelProperty(value = "费用单位(cost_unit)", required = false)
    @DicMarker(key = "cost_unit")
    private Integer costUnit;
    /**
     * 费用描述
     */
    @ApiModelProperty(value = "费用描述", required = false)
    private String costDesc;

    @ApiModelProperty(value = "费用类型冗余", required = false)
    private String costTypeValue;

    @ApiModelProperty(value = "费用单位冗余", required = false)
    private String costUnitValue;

}
