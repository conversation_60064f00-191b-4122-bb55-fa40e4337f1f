<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youngking.renrenwithactiviti.modules.job.dao.ScheduleJobDao">

    <!-- 批量更新状态 -->
    <update id="updateBatch">
        update schedule_job set status = #{status} where job_id in
        <foreach item="jobId" collection="list" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </update>

</mapper>