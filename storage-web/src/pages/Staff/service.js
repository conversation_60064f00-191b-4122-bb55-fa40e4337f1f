import { get, post } from '@/utils/request'

class Service{
    static getDepartment(){
        return get('/sys/sysdictionary/getInfo/staff_department')
    }
    static getList(data=undefined){
        return get('/worder/bizemployee/list',data)
    }
    static addOrUpdate(type,data=undefined){
        return post(`/worder/bizemployee/${type}`,data)
    }
    static deleteStaff(ids=[]){
        return post('/worder/bizemployee/delete',ids)
    }
}

export default Service
