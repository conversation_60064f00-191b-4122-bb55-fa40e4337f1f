package com.bonc.rrs.worder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 字段厂商关联表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ext_field_relation")
public class ExtFieldRelation {
    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车企编号
     */
    @TableField(value = "bid")
    private Integer bid;

    /**
     * 类型 0=字段 1=附件
     */
    @TableField(value = "field_type")
    private Byte fieldType;

    /**
     * 字段编号
     */
    @TableField(value = "field_id")
    private Integer fieldId;

    /**
     * 车企字段编号
     */
    @TableField(value = "out_field_name")
    private String outFieldName;
}