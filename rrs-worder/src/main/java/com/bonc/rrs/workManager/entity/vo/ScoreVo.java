package com.bonc.rrs.workManager.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description:
 * @Author: liujunpeng
 * @Date: 2023/7/17 16:27
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScoreVo implements Serializable {

    /**
     * 指标ID
     */
    private Integer indicatorId;

    /**
     * 网点id
     */
    private Integer dotId;

    /**
     * 标题
     */
    private String title;

    /**
     * 单位
     */
    private String unit;

    /**
     * 0:百分比，1：分值
     */
    private Integer numType;

    /**
     * 数量
     */
    private BigDecimal total;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 名称
     */
    private String name;

    /**
     * 分值
     */
    private BigDecimal score;

    /**
     * 值，饼状图分块使用
     */
    private BigDecimal value;

    public ScoreVo(String title, String unit, Integer numType, String name, BigDecimal score) {
        this.title = title;
        this.unit = unit;
        this.numType = numType;
        this.name = name;
        this.score = score;
    }
}
