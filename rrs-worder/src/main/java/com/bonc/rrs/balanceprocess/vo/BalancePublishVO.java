package com.bonc.rrs.balanceprocess.vo;

import com.bonc.rrs.balanceprocess.entity.BalanceFileEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by liqingchao on 2020/7/8.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(value = "发布单查询")
public class BalancePublishVO implements Serializable{

    @ApiModelProperty(value = "ID, 暂存后重新提交必传")
    private Integer id;

    @ApiModelProperty(value = "激励ID, 暂存后重新提交必传")
    private Integer stimulateId;
    /**
     * 发布单号
     */
    @ApiModelProperty(value = "发布单号")
    private String balancePublishNo;

    /**
     * 发布批次号
     */
    @ApiModelProperty(value = "发布批次号")
    private String batchNo;
    /**
     * 网点ID
     */
    @ApiModelProperty(value = "网点ID")
    private Integer dotId;
    /**
     * 品牌ID
     */
    @ApiModelProperty(value = "品牌ID")
    private Integer brandId;
    /**
     * 状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过
     */
    @ApiModelProperty(value = "状态 1:暂存 2:提交 3:第一次审核通过 4:第二次审核通过 5:已发布 -1:第一次审核不通过 -2:第二次审核不通过")
    private Integer status;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Integer creator;
    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;
    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date submitTime;

    /**
     * 工单列表
     */
    @ApiModelProperty(value = "工单列表")
    private List<PublishWorder> worderList;
    /**
     * 文件列表
     */
    @ApiModelProperty(value = "文件列表")
    private List<BalanceFileEntity> fileList;
}
