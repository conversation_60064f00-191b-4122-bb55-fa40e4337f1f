package bonc.storage.modules.systemmanagement.controller;

import bonc.storage.core.utils.Result;
import bonc.storage.modules.sys.entity.SystemTokenEntity;
import bonc.storage.modules.sys.service.SystemTokenService;
import bonc.storage.modules.systemmanagement.dao.User2Dao;
import bonc.storage.modules.systemmanagement.entity.MenuEntity;
import bonc.storage.modules.systemmanagement.entity.UserEntity;
import bonc.storage.modules.systemmanagement.service.MenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by liqingchao on 2020/1/9.
 */
@RestController
@RequestMapping("menu")
public class MenuController {
    @Autowired
    private MenuService menuService;
    @Autowired
    private SystemTokenService systemTokenService;
    @Autowired
    private User2Dao userDao;

    @RequestMapping("menulist")
    public Result menulist(){
        List<MenuEntity> list = menuService.listAllMenu();
        return Result.ok().put("menuList", list);
    }

    @RequestMapping("menuTree")
    public Result menuTree(String token){
        SystemTokenEntity tokenEntity = systemTokenService.queryToken(token);

        //查询用户信息
        UserEntity user = userDao.queryUserById(Integer.valueOf(tokenEntity.getUserId()));
        String roleIds=user.getRoleId();
        String[] roleIdArr = roleIds.split(",");
        String ss=null;
        for(String s:roleIdArr){

            if(s.equals("0")){

                List<MenuEntity> list = menuService.menuTreeadmin();
                return Result.ok().put("menuList", list);
            }
            ss=s;
        }
        List<MenuEntity> list = menuService.menuTree(Integer.valueOf(ss));
        return Result.ok().put("menuList", list);

    }

    @RequestMapping("addMenu")
    public Result addMenu(@RequestBody MenuEntity menuEntity){
        menuService.addMenu(menuEntity);
        return Result.ok();
    }


    @RequestMapping("updateMenu")

    public Result updateMenu(@RequestBody MenuEntity menuEntity){
        menuService.updateMenu(menuEntity);
        return Result.ok();
    }

    @RequestMapping("deleteMenu")
    public Result deleteMenu(String menuId,String token){
        //判断是否有子菜单或按钮
        List<MenuEntity> menuList = menuService.queryListParentcode(Integer.valueOf(menuId));
        if (menuList.size() > 0) {
            return Result.error("请先删除子菜单或按钮");
        }
        SystemTokenEntity tokenEntity = systemTokenService.queryToken(token);

        //查询用户信息
        UserEntity user = userDao.queryUserById(Integer.valueOf(tokenEntity.getUserId()));
        String roleIds=user.getRoleId();
        String[] roleIdArr = roleIds.split(",");
        //String ss=null;
        for(String s:roleIdArr){

            if(s.equals("0")){

                menuService.delMenuadmin(Integer.valueOf(menuId));
                return Result.ok();
            }
            //ss=s;
        }
        menuService.delMenu(Integer.valueOf(menuId));
        return Result.ok();
    }

    @RequestMapping("addRoleMenu")
    public Result addRoleMenu(String roleId,String menuIds){
        List<Integer> menuIdList = new ArrayList<>();
        String[] menuIdArr = menuIds.split(",");
        for(String s:menuIdArr){
            menuIdList.add(Integer.valueOf(s));
        }
        menuService.addRoleMenu(Integer.valueOf(roleId),menuIdList);
        return  Result.ok();
    }
}
