package com.bonc.rrs.util.flow;


import java.io.BufferedReader;
import java.sql.Clob;

public class PublicUtil {

	/**
	 * @param clob 字段 转成 字符串
	 * @return
	 */
	public  String printTemplate(Clob clob) {
			StringBuffer buf = new StringBuffer();
			BufferedReader reader = null;
			try {
				reader = new BufferedReader(clob.getCharacterStream());
				String line = null;
				while ((line = reader.readLine()) != null) {
					buf.append(line);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return buf.toString();
	}
	public  String printTemplateln(Clob clob) {
		StringBuffer buf = new StringBuffer();
		BufferedReader reader = null;
		try {
			int total = printCount(clob);
			reader = new BufferedReader(clob.getCharacterStream());
			String line = null;
			int i = 0;
			while ((line = reader.readLine()) != null) {
				buf.append(line);
				i++;
				if(i!=total){
					buf.append("#r_n");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return buf.toString();
	}
	private   int printCount(Clob clob) {
		StringBuffer buf = new StringBuffer();
		BufferedReader reader = null;
		int result = 0;
		try {
			reader = new BufferedReader(clob.getCharacterStream());
			String line = null;
			while ((line = reader.readLine()) != null) {
				result ++;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

}
