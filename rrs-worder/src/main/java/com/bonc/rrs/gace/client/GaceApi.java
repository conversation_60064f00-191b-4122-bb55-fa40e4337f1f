package com.bonc.rrs.gace.client;

import com.bonc.rrs.gace.dto.request.RequestDto;
import com.bonc.rrs.gace.dto.response.ResponseDto;
import com.bonc.rrs.gace.enums.GaceApiEnum;
import com.bonc.rrs.gace.util.GaceResponse;

/**
 * <AUTHOR>
 * @date 2024年10月14日 17:37
 */
public interface GaceApi<R extends RequestDto,T extends ResponseDto> {

    GaceApiEnum gaceApiType();

    GaceResponse<T> execute(R request);

}
