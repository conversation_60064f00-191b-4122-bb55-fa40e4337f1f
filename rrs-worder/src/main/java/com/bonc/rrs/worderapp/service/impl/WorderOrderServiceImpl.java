package com.bonc.rrs.worderapp.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.domain.BydFaultCodes;
import com.bonc.rrs.byd.domain.BydFaultCodesMapping;
import com.bonc.rrs.byd.service.BydFaultCodesMappingService;
import com.bonc.rrs.byd.service.BydFaultCodesService;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.entity.entity.WorderOrderLogEntity;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.pay.service.PayService;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.signlocation.entity.SoldierSignLocationEntity;
import com.bonc.rrs.signlocation.service.SignLocationService;
import com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper;
import com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO;
import com.bonc.rrs.util.ChannelQueryUtils;
import com.bonc.rrs.util.DistanceUtils;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.constant.SceneConstant;
import com.bonc.rrs.worder.dao.WorderExtFieldDao;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.bonc.rrs.worder.entity.WorderInfoEntity;
import com.bonc.rrs.worder.entity.WorderInformationAttributeEntity;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.dto.WorderInfoDTO;
import com.bonc.rrs.worder.entity.po.ExecuteFlowResultPo;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worder.service.WorderSceneService;
import com.bonc.rrs.worderapp.constant.Constant;
import com.bonc.rrs.worderapp.constant.FieldConstant;
import com.bonc.rrs.worderapp.constant.SignConstant;
import com.bonc.rrs.worderapp.dao.WorderOperationRecodeDao;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.entity.DicKeyResultEntity;
import com.bonc.rrs.worderapp.entity.WorderInformation;
import com.bonc.rrs.worderapp.entity.WorderOperationRecodeEntity;
import com.bonc.rrs.worderapp.entity.dto.WorderExtFieldQueryDto;
import com.bonc.rrs.worderapp.entity.dto.WorderFieldDto;
import com.bonc.rrs.worderapp.entity.dto.WorderInformationDto;
import com.bonc.rrs.worderapp.entity.po.FieldInfoPo;
import com.bonc.rrs.worderapp.entity.po.FieldInfoResultPo;
import com.bonc.rrs.worderapp.entity.po.FieldPo;
import com.bonc.rrs.worderapp.entity.vo.*;
import com.bonc.rrs.worderapp.service.WorderOrderService;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.entity.dto.SysFileUpdateDto;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.DateUtils;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorderOrderServiceImpl implements WorderOrderService {

    @Autowired(required = false)
    WorderOrderDao worderOrderDao;
    @Autowired(required = false)
    SysFilesMapper sysFilesMapper;
    @Autowired(required = false)
    WorderOrderLogMapper worderOrderLogMapper;
    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    PayService payService;
    @Autowired(required = false)
    BillingRecodeMapper billingRecodeMapper;
    @Autowired
    WorderSceneService worderSceneService;
    @Autowired(required = false)
    WorkMsgDao workMsgDao;

    @Autowired(required = false)
    WorderOperationRecodeDao worderOperationRecodeDao;
    @Autowired
    private WorderInformationAttributeDao worderInformationAttributeDao;
    @Autowired
    private ICaApiService caApiService;
    @Autowired
    private FlowCommon flowCommon;

    @Autowired(required = false)
    WorderExtFieldDao worderExtFieldDao;


    @Autowired
    SignLocationService signLocationService;

    @Autowired
    private ProviderBusinessService providerBusinessService;

    @Autowired
    private WorderExtFieldService worderExtFieldService;

    @Autowired
    private BydFaultCodesService bydFaultCodesService;

    @Autowired
    private BydFaultCodesMappingService bydFaultCodesMappingService;

    protected SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    private Boolean hasWholeProcessCustomerService() {
        SysUserEntity sysUserEntity = getUser();
        return StringUtils.isNotBlank(sysUserEntity.getRoleName()) && sysUserEntity.getRoleName().contains(SceneConstant.WHOLE_PROCESS_CUSTOMER_SERVICE_NAME);
    }

    @Override
    public List<WorderOrderLogDTO> findOrderLogInfoListsByStates(String worderNo) {
        List<String> list = Arrays.asList("1");
        return worderOrderLogMapper.findOrderLogInfoListsByStates(worderNo, list);
    }

    @Override
    public WorderInfoEntity queryById(Integer id) {
        return worderInformationDao.getByWorderId(id);
    }

    @Override
    public void updateWorderTime(String worderNo, String time, String time2,String time3) {
        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", worderNo);
        map.put("conveyAppointTime", time);   //修改勘测预约时间
        map.put("installAppointTime", time2); //修改安装预约时间
        worderInformationDao.updateAppointTime(map);
      //没有被车企回退过 修改勘测完成时间
        if(!isRollback(worderNo)){
            //修改勘测完成时间
            QueryWrapper<WorderExtFieldEntity> q1 = new QueryWrapper<>();

            q1.eq("worder_no", worderNo).eq("field_id", 1718);
            WorderExtFieldEntity w1 = new WorderExtFieldEntity();
            w1.setFieldValue(time);
            worderExtFieldDao.update(w1, q1);
        }
        //修改安装完成完成时间

        QueryWrapper<WorderExtFieldEntity> q2 = new QueryWrapper<>();
        q2.eq("worder_no", worderNo).eq("field_id", 1197);
        WorderExtFieldEntity w2 = new WorderExtFieldEntity();
        w2.setFieldValue(time2);
        worderExtFieldDao.update(w2, q2);
        //电力报装完成时间
        if(StringUtils.isNotBlank(time3)){
            QueryWrapper<WorderExtFieldEntity> q3 = new QueryWrapper<>();
            q3.eq("worder_no", worderNo).eq("field_id", 1715);
            WorderExtFieldEntity w3 = new WorderExtFieldEntity();
            w3.setFieldValue(time3);
            worderExtFieldDao.update(w3, q3);
        }
    }

    @Override
    public Boolean isRollback(String worderNo) {
        QueryWrapper<WorderOperationRecodeEntity> q = new QueryWrapper<>();
        q.eq("worder_no",worderNo).eq("worder_exec_status",23);
        //有数据代表被回退过
        List<WorderOperationRecodeEntity> worderOperationRecodeEntities = worderOperationRecodeDao.selectList(q);
        return !worderOperationRecodeEntities.isEmpty();
    }


    @Override
    public List<WorderInformation> listWorderInformation(Map map) {
        map = getWorderQueryMap(map);
        Map params = new HashMap();
        List<WorderInformation> worderInformations = worderOrderDao.listWorderInformation(map);
        //List<WorderInformation> worderInformationList = handleColorLabel(worderInformations);
        List<WorderInformation> worderInformationList = worderInformations.stream()
//                .filter(info -> {
//                    // 待勘测预约订单 进行全流程客服预约校验 10.21需求有变 先搁置
//                    if((IntegerEnum.ONE.getValue().toString().equals(info.getWorderStatus())  && IntegerEnum.TWO.getValue().toString().equals(info.getWorderExecStatus()))
//                    || (IntegerEnum.TWO.getValue().toString().equals(info.getWorderStatus())  && IntegerEnum.TEN.getValue().toString().equals(info.getWorderExecStatus()))){
//                        Boolean isCustomerServiceAppointment = worderSceneService.getIsCustomerServiceAppointment(Integer.parseInt(info.getWorderId()));
//                        // 判断订单场景是否配置全流程客服勘测预约
//                        if(isCustomerServiceAppointment != null && isCustomerServiceAppointment){
//                            // 判断用户是否拥有全流程客服角色
//                            return hasWholeProcessCustomerService();
//                        }
//                    }
//
//                    return true;
//                })
                .map(info -> {
                    String brandName = worderInformationDao.getBrand(info.getTemplateId()).getBrandName();
                    info.setCarBrand(brandName);
                    return info;
                }).collect(Collectors.toList());
        return worderInformationList;
    }

    @Override
    public Map queryTypeTitleData(Map map) {
        return worderOrderDao.queryTypeTitleData(map);
    }

    /**
     * 处理色标标签
     *
     * @param worders
     * @return
     */
    public List<WorderInformation> handleColorLabel(List<WorderInformation> worders) {
        List<WorderInformation> list = worders.stream().map(info -> {
            //色标标签处理
            Integer colorLabel = 0;
            String worderExecStatus = info.getWorderExecStatus(); //工单执行状态
            switch (worderExecStatus) {
                case "2":
                    //待勘测预约
                    colorLabel = 1;
                    break;
                case "3":
                    //待勘测
                    colorLabel = 1;
                    break;
                case "4":
                    //勘测资料未提交
                    colorLabel = 1;
                    break;
                case "6":
                    //勘测资料整改中
                    colorLabel = 1;
                    break;
                case "9":
                    //等待充电桩及配件
                    colorLabel = 1;
                    break;
                case "10":
                    //待安装预约
                    colorLabel = 1;
                    break;
                case "11":
                    //待安装
                    colorLabel = 1;
                    break;
                case "12":
                    //安装资料未提交
                    colorLabel = 1;
                    break;
                case "14":
                    //安装资料整改中
                    colorLabel = 1;
                    break;
                default:
                    break;
            }
            info.setColorLabel(colorLabel);
            return info;
        }).collect(Collectors.toList());
        //有色标的
        List<WorderInformation> haveColor = list.stream().filter(info -> {
            return info.getColorLabel() == 1;
        }).collect(Collectors.toList());
        //无色标的
        List<WorderInformation> noColor = list.stream().filter(info -> {
            return info.getColorLabel() == 0;
        }).collect(Collectors.toList());
        //把有色标的放在列表前面
        for (int i = 0; i < noColor.size(); i++) {
            haveColor.add(noColor.get(i));
        }
        return haveColor;
    }

    @Override
    public List<FieldPo> listConveyField(String worderNo) {
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        worderFieldDto.setWorderNo(worderNo);
        worderFieldDto.setFieldPurpose(FieldConstant.CONVEY);
        worderFieldDto.setFieldClass(FieldConstant.FIELD);
        List<FieldPo> fields = worderOrderDao.listWorderField(worderFieldDto);
        getFieldData(fields);

//        for (int i = 0; i < fields.size(); i++) {
//            FieldPo fieldPo = fields.get(i);
//            if (fieldPo != null) {
//                fields.get(i).setFieldDicValue(worderOrderDao.listDicKey(fieldPo.getFiledDicKey()));
//            }
//        }
        return fields;
    }

    @Override
    public List<FieldPo> listConveyDataField(String worderNo) {
        System.out.println(worderNo);
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        worderFieldDto.setWorderNo(worderNo);
        worderFieldDto.setFieldPurpose(FieldConstant.CONVEY);
        worderFieldDto.setFieldClass(FieldConstant.DATA);
        List<FieldPo> fields = worderOrderDao.listWorderField(worderFieldDto);
        setUrlById(fields);
        return fields;
    }

    @Override
    public List<FieldPo> listCreateField(String worderNo) {
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        worderFieldDto.setWorderNo(worderNo);
        worderFieldDto.setFieldPurpose(FieldConstant.CREATE);
        List<FieldPo> fields = worderOrderDao.listWorderField(worderFieldDto);
        return fields;
    }

    @Override
    public List<FieldPo> listInstallField(String worderNo) {
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        worderFieldDto.setWorderNo(worderNo);
        worderFieldDto.setFieldPurpose(FieldConstant.INSTALL);
        worderFieldDto.setFieldClass(FieldConstant.FIELD);
        List<FieldPo> fields = worderOrderDao.listWorderField(worderFieldDto);
        getFieldData(fields);
//        for (int i = 0; i < fields.size(); i++) {
//            FieldPo fieldPo = fields.get(i);
//            if (fieldPo != null) {
//                fields.get(i).setFieldDicValue(worderOrderDao.listDicKey(fieldPo.getFiledDicKey()));
//            }
//        }
        return fields;
    }

    @Override
    public List<FieldPo> listInstallDataField(String worderNo) {
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        worderFieldDto.setWorderNo(worderNo);
        worderFieldDto.setFieldPurpose(FieldConstant.INSTALL);
        worderFieldDto.setFieldClass(FieldConstant.DATA);
        List<FieldPo> fields = worderOrderDao.listWorderField(worderFieldDto);

        //比亚迪
        if(providerBusinessService.checkBydOrderByWorderNo(worderNo)) {

            WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
            //维修
            if (worderInformationEntity.getWorderTypeId() == 6) {
                // 工单扩展字段
                List<WorderExtFieldEntity> extFieldEntities = worderExtFieldService.getSpecificFields(worderNo, null);
                Map<Integer, String> fieldMap = new HashMap<>();
                for (WorderExtFieldEntity extFieldEntity : extFieldEntities) {
                    fieldMap.put(extFieldEntity.getFieldId(), extFieldEntity.getFieldValue());
                }

                //1.获取比亚迪故障码
                String falutCode = fieldMap.get(2107);

                if (StringUtils.isNotBlank(falutCode)) {
                    //比亚迪故障码对象
                    BydFaultCodes bydFaultCodes = bydFaultCodesService.lambdaQuery().eq(BydFaultCodes::getCode, falutCode).one();
                    if (bydFaultCodes == null || bydFaultCodes.getIsLeaf() != 1) {
                        throw new RRException("比亚迪故障码不正确，请重新选择");
                    }

                    //2.获取故障码映射字段
                    List<BydFaultCodesMapping> faultCodesMappings = bydFaultCodesMappingService.getBydFaultCodesMapping(falutCode);
                    List<FieldPo> faultCodesFields = new ArrayList<>();
                    for (BydFaultCodesMapping faultCodesMapping : faultCodesMappings) {
                        FieldPo existfieldPo = fields.stream().filter(fieldPo -> fieldPo.getFieldId().equals(faultCodesMapping.getFieldId().toString())).findFirst().orElse(null);
                        if (existfieldPo != null) {
                            faultCodesFields.add(existfieldPo);
                        } else {
                            String savedValue = fieldMap.get(faultCodesMapping.getFieldId());
                            FieldPo fieldPo = new FieldPo();
                            fieldPo.setWorderNo(worderNo);
                            fieldPo.setFieldId(faultCodesMapping.getFieldId().toString());
                            fieldPo.setFieldName(faultCodesMapping.getFieldName());
                            fieldPo.setFieldValues(savedValue);
                            fieldPo.setFieldClass(faultCodesMapping.getFieldClass());
                            fieldPo.setFieldType(faultCodesMapping.getFieldType());
                            fieldPo.setNotNull(faultCodesMapping.getIsNotnull());
                            fieldPo.setNecessary(faultCodesMapping.getIsNessary());
                            faultCodesFields.add(fieldPo);
                        }
                    }

                    List<String> mappingCodes = faultCodesMappings.stream().map(BydFaultCodesMapping::getCode).collect(Collectors.toList());


                    //3.过滤掉已经存在的非当前故障码映射字段
                    // 获取故障码映射表非当前故障码的所有数据
                    List<String> faultCodeFieldIds = bydFaultCodesMappingService.lambdaQuery().notIn(BydFaultCodesMapping::getCode, mappingCodes).list()
                            .stream()
                            .map(faultCodesMapping -> String.valueOf (faultCodesMapping.getFieldId()))
                            .collect(Collectors.toList());
                    //删除已经存在的非当前故障码扩展字段
                    fields.removeIf(fieldPo -> faultCodeFieldIds.contains(fieldPo.getFieldId()));

                    //4.添加故障码映射字段
                    fields.addAll(faultCodesFields);

                    worderExtFieldService.remove(new QueryWrapper<WorderExtFieldEntity>().eq("worder_no", worderNo).in("field_id", faultCodeFieldIds));
                }
            }
        }

        setUrlById(fields);
        return fields;
    }

    @Override
    public Integer saveWorderFieldInfo(FieldVo fieldVo) {
        return worderOrderDao.saveWorderFieldInfo(fieldVo);
    }

    @Override
    public Integer saveWorderDataInfo(DataVo dataVo) {
        dataVo.setFieldValueDup(dataVo.getFileName());
        // 转DataVo对象
        FieldVo fieldVo = objectTrans(dataVo);
        Integer updateNum = worderOrderDao.saveWorderFieldInfo(fieldVo);
        // 获取插入主键
        Integer id = fieldVo.getId();
        // 一组文件ID
        String[] fileIds = null;
        String fieldValue = fieldVo.getFieldValue();
        if (StringUtils.isNotBlank(fieldValue)) {
            fileIds = fieldVo.getFieldValue().split(",");
            // 修改上传文件表
            saveWorderSysFile(fileIds, 1, id);
        }
        return updateNum;
    }

    @Override
    public Integer submitConveyAduit(String worderNo) {
        return updateWorderStatusConverySubmit(worderNo, FieldConstant.CONVEY_NOT_AUDIT);
    }

    @Override
    public Integer updateWorderFieldInfo(FieldVo fieldVo) {
        return worderOrderDao.updateWorderFieldInfo(fieldVo);
    }

    @Override
    public Integer updateWorderDataInfo(DataVo dataVo) {
        dataVo.setFieldValueDup(dataVo.getFileName());
        // 转DataVo对象
        FieldVo fieldVo = objectTrans(dataVo);
        Integer updateNum = worderOrderDao.updateWorderFieldInfo(fieldVo);
        // 获取主键ID
        Integer id = fieldVo.getId();
        // 一组文件ID
        String[] fileIds = null;
        String fieldValue = fieldVo.getFieldValue();
        if (StringUtils.isNotBlank(fieldValue)) {
            fileIds = fieldVo.getFieldValue().split(",");
            // 修改上传文件表
            saveWorderSysFile(fileIds, 1, id);
        }
        return updateNum;
    }

    public void saveWorderSysFile(String[] fileIds, Integer objType, Integer objValue) {
        SysFileUpdateDto sysFileUpdate = new SysFileUpdateDto();
        for (int i = 0; i < fileIds.length; i++) {
            if (StringUtils.isBlank(fileIds[i]) || "null".equalsIgnoreCase(fileIds[i])) {
                continue;
            }
            sysFileUpdate.setFileId(Integer.parseInt(fileIds[i]));
            sysFileUpdate.setObjType(objType);
            sysFileUpdate.setObjValue(objValue);
            sysFilesMapper.updateSysFile(sysFileUpdate);
        }
    }

    public FieldVo objectTrans(DataVo dataVo) {
        // 转字符串
        String jsonString = JSON.toJSONString(dataVo);
        // 去除中括号
        jsonString = StringUtils.replaceSquareToQuetes(jsonString);
        // 转json对象
        JSONObject jsonObject = JSON.parseObject(jsonString);
        // 转DataVo对象
        return jsonObject.toJavaObject(FieldVo.class);
    }

    @Override
    public WorderStatusVo getWorderStatus(String worderNo) {
        WorderStatusVo worderStatusVo = new WorderStatusVo();
        List<WorderInformationEntity> worderInformationEntities = worderOrderDao.getWorderInformation(worderNo);
        if (worderInformationEntities.size() > 0 && worderInformationEntities.get(0) != null) {
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            worderStatusVo.setWorderExecStatus(worderInformationEntity.getWorderExecStatus());
            worderStatusVo.setWorderStatus(worderInformationEntity.getWorderStatus());
            worderStatusVo.setWorderNo(worderNo);
            worderStatusVo.setWorderId(worderInformationEntity.getWorderId());
            worderStatusVo.setConveySignOutTime(worderInformationEntity.getConveySignOutTime());
            worderStatusVo.setInstallSignOutTime(worderInformationEntity.getInstallSignOutTime());
        }
        // 查询支付状态
        List<WorderOrderLogEntity> lst = worderOrderLogMapper.selectList(new QueryWrapper<WorderOrderLogEntity>()
                .eq("worder_no", worderNo).ne("order_status", "4").orderByDesc("order_log_id"));
        Integer payStatus = 0;
        if (lst.size() > 0 && null != lst.get(0)) {
            payStatus = lst.get(0).getOrderStatus();
        }
        worderStatusVo.setPayStatus(payStatus);
        return worderStatusVo;
    }

    @Override
    public Integer updateWorderStatus(WorderStatusVo worderStatusVo) {
        return updateWorderStatus(worderStatusVo.getWorderNo(), worderStatusVo.getWorderExecStatus(),
                worderStatusVo.getWorderStatus());
    }

    public Integer updateWorderStatus(String worderNo, Integer worderExecStatus) {
        return updateWorderStatus(worderNo, worderExecStatus, null);
    }

    public Integer updateWorderStatus(String worderNo, Integer worderExecStatus, Integer worderStatus) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderNo);
        worderInformationDto.setWorderStatus(worderStatus);
        worderInformationDto.setWorderExecStatus(worderExecStatus);
        if (flowCommon.hasFlowByWorderNo(worderNo)) {
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return 0;
            }
            return 1;
        } else {
            return worderOrderDao.updateWorderInformation(worderInformationDto);
        }
    }

    @Override
    public Integer updateWorderStatusInstallSubmit(WorderStatusVo worderStatusVo) {
        return updateWorderStatusInstallSubmit(worderStatusVo.getWorderNo(),
                worderStatusVo.getWorderExecStatus(), worderStatusVo.getWorderStatus(), worderStatusVo.getInstallSignOutTime());
    }

    public Integer updateWorderStatusInstallSubmit(String worderNo, Integer worderExecStatus, Integer worderStatus, String installSignOutTime) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderNo);
        worderInformationDto.setWorderStatus(worderStatus);
        worderInformationDto.setWorderExecStatus(worderExecStatus);
        worderInformationDto.setInstallAppointTime(installSignOutTime);
        if (flowCommon.hasFlowByWorderNo(worderNo)) {
            //安装资料待客服确认
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.Slod2FixDoc, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return 0;
            }
            return 1;
        } else {
            return worderOrderDao.updateWorderInformation(worderInformationDto);
        }
    }

    public Integer updateWorderStatusConverySubmit(String worderNo, Integer worderExecStatus) {
        return updateWorderStatusConverySubmit(worderNo, worderExecStatus, null);
    }

    public Integer updateWorderStatusConverySubmit(String worderNo, Integer worderExecStatus, Integer worderStatus) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderNo);
        worderInformationDto.setWorderStatus(worderStatus);
        worderInformationDto.setWorderExecStatus(worderExecStatus);
        if (flowCommon.hasFlowByWorderNo(worderNo)) {
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.Sold2SurvDoc, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return 0;
            }
            return 1;
        } else {
            return worderOrderDao.updateWorderInformation(worderInformationDto);
        }
    }

    @Override
    public Integer updateConveyAppoint(WorderInformationDto worderInformationDto) {
        return worderOrderDao.updateWorderInformation(worderInformationDto);
    }

    @Override
    public Integer updateAppointConvey(WorderInformationDto worderInformationDto) {
        return worderOrderDao.updateWorderInfo(worderInformationDto);
    }

    @Override
    public Integer updateInstallAppoint(WorderInformationDto worderInformationDto) {
        return worderOrderDao.updateWorderInformation(worderInformationDto);
    }

    @Override
    public Integer updateAppointInstall(WorderInformationDto worderInformationDto) {
        return worderOrderDao.updateWorderInfo(worderInformationDto);
    }

    @Override
    public Integer updateWorderLevel(WorderInformationDto worderInformationDto) {
        return worderOrderDao.updateWorderLevel(worderInformationDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateWorderConveySign(WorderInformationVo worderInformationVo) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderInformationVo.getWorderNo());
        worderInformationDto.setConveySignPicture(worderInformationVo.getConveySignPicture());
        //worderInformationDto.setWorderStatus(SysDictConstant.WORDER_STATUS_5);
        worderInformationDto.setWorderExecStatus(FieldConstant.CONVEY_NOT_COMMIT);
        worderInformationDto.setConveySignArea(worderInformationVo.getConveySignArea());
        worderInformationDto.setConveySignTime(DateUtils.getCurrentTime());
        Integer integer = 0;
        if (flowCommon.hasFlowByWorderNo(worderInformationVo.getWorderNo())) {
            // 调用勘测签到
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationVo.getWorderNo(), FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
//                return Results.message(Integer.parseInt(executeFlowResultPo.getCode()), executeFlowResultPo.getMsg(), null);
                return R.error(executeFlowResultPo.getMsg());
            }
            WorderInformationDto worder = new WorderInformationDto();
            worder.setWorderNo(worderInformationVo.getWorderNo());
            worder.setConveySignArea(worderInformationVo.getConveySignArea());
            worder.setConveySignTime(DateUtils.getCurrentTime());
            integer = worderOrderDao.updateWorderInformation(worder);
        } else {
            integer = worderOrderDao.updateWorderInformation(worderInformationDto);
        }

        WorderInfoDTO worderInfo = worderInformationDao.findWorderInfo(
                worderInformationVo.getWorderNo());
        Map<String, String> userMap = new HashMap<>();
        //Map<String,String> map=new HashMap<>();
        //查询工单地址

        WorderInfoEntity worderInfoEntity = worderInformationDao.getByWorderId(worderInfo.getWorderId());
        //用户地址
        String address = worderInfoEntity.getAddressDup();
        double locationX = 0d;
        double locationY = 0d;
        HashMap<String, Double> nMap = new HashMap<String, Double>();
        try {
            //获取用户坐标
            userMap = ChannelQueryUtils.getCoding(address);
            //服务兵定位维度
            double locationX_1 = Double.parseDouble(worderInformationVo.getLatitude());
            //服务兵定位经度
            double locationY_1 = Double.parseDouble(worderInformationVo.getLongitude());
            nMap = gcjToBd(locationY_1, locationX_1);
            locationX = nMap.get("bdUserX");
            locationY = nMap.get("bdUserY");
            //  map = ChannelQueryUtils.getCoding(worderInformationVo.getConveySignArea());
        } catch (Exception e) {
            e.printStackTrace();
        }
        SoldierSignLocationEntity soldierSignLocation = new SoldierSignLocationEntity();
        if (userMap.get("code").equals("8888")) {
            soldierSignLocation.setWorderId(worderInfo.getWorderId());
            soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
            soldierSignLocation.setSignType(SignConstant.TYPE_ONE);
            soldierSignLocation.setLocationX(String.valueOf(locationX));
            soldierSignLocation.setLocationY(String.valueOf(locationY));
            soldierSignLocation.setDistance(888888);
        } else if (userMap.get("code").equals("9999")) {
            soldierSignLocation.setWorderId(worderInfo.getWorderId());
            soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
            soldierSignLocation.setSignType(SignConstant.TYPE_ONE);
            soldierSignLocation.setLocationX(String.valueOf(locationX));
            soldierSignLocation.setLocationY(String.valueOf(locationY));
            soldierSignLocation.setDistance(999999);
        } else if (userMap.get("code").equals("0000")) {
            soldierSignLocation.setWorderId(worderInfo.getWorderId());
            soldierSignLocation.setUserX(userMap.get("lat"));
            soldierSignLocation.setUserY(userMap.get("lng"));
            soldierSignLocation.setLocationX(String.valueOf(locationX));
            soldierSignLocation.setLocationY(String.valueOf(locationY));
            soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
            //用户经度
            double userY = Double.parseDouble(soldierSignLocation.getUserY());
            //用户维度
            double userX = Double.parseDouble(soldierSignLocation.getUserX());


            //得出结果是千米所有需要乘以一千换算成米
            //double distance1 = DistanceUtils.getDistance(userY, userX, locationY, locationX);
            double distance = DistanceUtils.getDistance(userY, userX, locationY, locationX);
            BigDecimal bg = new BigDecimal(distance * 1000);
            double f = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            int nextIntValue = (int) Math.round(f);
            soldierSignLocation.setDistance(nextIntValue);
            soldierSignLocation.setSignType(SignConstant.TYPE_ONE);
        }

        //新增用户地址表
        signLocationService.savaSignLocation(soldierSignLocation);
        return R.ok().put("num", integer).putWorderNo(worderInformationVo.getWorderNo())
                .putWorderExecStatus(FieldConstant.CONVEY_NOT_COMMIT).putWorderTriggerEvent(WarningConstant.CONVEY_SIGNIN);
    }


    public HashMap<String, Double> gcjToBd(double gcUserY, double gcUserX) {
        HashMap<String, Double> map = new HashMap<String, Double>();
        double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
        double z = Math.sqrt(gcUserX * gcUserX + gcUserY * gcUserY) + 0.00002 * Math.sin(gcUserY * x_pi);
        double theta = Math.atan2(gcUserY, gcUserX) + 0.000003 * Math.cos(gcUserX * x_pi);
        Double bdUserX = z * Math.cos(theta) + 0.0065;
        Double bdUserY = z * Math.sin(theta) + 0.006;
        map.put("bdUserX", bdUserX);
        map.put("bdUserY", bdUserY);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R updateWorderInstallSign(WorderInformationVo worderInformationVo) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(worderInformationVo.getWorderNo());
        worderInformationDto.setInstallSignPicture(worderInformationVo.getInstallSignPicture());
        //worderInformationDto.setWorderStatus(SysDictConstant.WORDER_STATUS_14);
        worderInformationDto.setWorderExecStatus(FieldConstant.INSTALL_NOT_COMMIT);
        WorderInfoDTO worderInfo = worderInformationDao.findWorderInfo(
                worderInformationVo.getWorderNo());
        worderInformationDto.setInstallSignArea(worderInformationVo.getInstallSignArea());
        worderInformationDto.setInstallSignTime(DateUtils.getCurrentTime());
        recodeWorderOperate(worderInformationVo.getWorderNo(), Constant.INSTALL, Constant.INSTALL_NOT_COMMIT, "安装签到");
        Integer integer = 0;
        if (flowCommon.hasFlowByWorderNo(worderInformationVo.getWorderNo())) {
            // 调用安装签到
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationVo.getWorderNo(), FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return R.error(executeFlowResultPo.getMsg());
            }
            WorderInformationDto worder = new WorderInformationDto();
            worder.setWorderNo(worderInformationVo.getWorderNo());
            worder.setInstallSignArea(worderInformationVo.getInstallSignArea());
            worder.setInstallSignTime(DateUtils.getCurrentTime());
            integer = worderOrderDao.updateWorderInformation(worder);
        } else {
            integer = worderOrderDao.updateWorderInformation(worderInformationDto);
        }
        if (StringUtils.isNotBlank(worderInformationVo.getInstallSignArea())) {
            Map<String, String> userMap = new HashMap<>();
            Map<String, String> map = new HashMap<>();
            //查询工单地址
            WorderInfoEntity worderInfoEntity = worderInformationDao.getByWorderId(worderInfo.getWorderId());
            //用户地址
            String address = worderInfoEntity.getAddressDup();
            try {
                //获取用户坐标
                userMap = ChannelQueryUtils.getCoding(address);
                map = ChannelQueryUtils.getCoding(worderInformationVo.getInstallSignArea());
            } catch (Exception e) {
                e.printStackTrace();
            }
            SoldierSignLocationEntity soldierSignLocation = new SoldierSignLocationEntity();
            if (map.get("code").equals("8888")) {
                soldierSignLocation.setWorderId(worderInfo.getWorderId());
                soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
                soldierSignLocation.setSignType(SignConstant.TYPE_ZERO);
                soldierSignLocation.setLocationX(worderInformationVo.getLatitude());
                soldierSignLocation.setLocationY(worderInformationVo.getLongitude());
                soldierSignLocation.setDistance(888888);
            } else if (userMap.get("code").equals("8888")) {
                soldierSignLocation.setWorderId(worderInfo.getWorderId());
                soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
                soldierSignLocation.setSignType(SignConstant.TYPE_ZERO);
                soldierSignLocation.setLocationX(map.get("lat"));
                soldierSignLocation.setLocationY(map.get("lng"));
                soldierSignLocation.setDistance(888888);
            } else if (map.get("code").equals("9999")) {
                soldierSignLocation.setWorderId(worderInfo.getWorderId());
                soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
                soldierSignLocation.setSignType(SignConstant.TYPE_ZERO);
                soldierSignLocation.setLocationX(worderInformationVo.getLatitude());
                soldierSignLocation.setLocationY(worderInformationVo.getLongitude());
                soldierSignLocation.setDistance(999999);
            } else if (userMap.get("code").equals("9999")) {
                soldierSignLocation.setWorderId(worderInfo.getWorderId());
                soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
                soldierSignLocation.setSignType(SignConstant.TYPE_ZERO);
                soldierSignLocation.setLocationX(map.get("lat"));
                soldierSignLocation.setLocationY(map.get("lng"));
                soldierSignLocation.setDistance(999999);
            } else if (userMap.get("code").equals("0000")) {
                soldierSignLocation.setWorderId(worderInfo.getWorderId());
                soldierSignLocation.setUserX(userMap.get("lat"));
                soldierSignLocation.setUserY(userMap.get("lng"));
                soldierSignLocation.setLocationX(map.get("lat"));
                soldierSignLocation.setLocationY(map.get("lng"));
                soldierSignLocation.setSoldierId(worderInfoEntity.getServiceId());
                //用户经度
                double userY = Double.parseDouble(soldierSignLocation.getUserY());
                //用户维度
                double userX = Double.parseDouble(soldierSignLocation.getUserX());
                //服务兵定位维度
                double locationX = Double.parseDouble(soldierSignLocation.getLocationX());
                //服务兵定位经度
                double locationY = Double.parseDouble(soldierSignLocation.getLocationY());
                //得出结果是千米所有需要乘以一千换算成米
                double distance = DistanceUtils.getDistance(userY, userX, locationY, locationX);
                BigDecimal bg = new BigDecimal(distance * 1000);
                double f = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                int nextIntValue = (int) Math.round(f);
                soldierSignLocation.setDistance(nextIntValue);
                soldierSignLocation.setSignType(SignConstant.TYPE_ZERO);
            }
            //新增用户地址表
            signLocationService.savaSignLocation(soldierSignLocation);
        }

        try {
            WorderInformationAttributeEntity entity1 = worderInformationAttributeDao.selectAttributeByWorderNo(worderInformationVo.getWorderNo(), "push_order_complete", "ca");
            if (entity1 != null && entity1.getAttributeValue().equals("0")) {
                CaApiResponse response2 = caApiService.pushOrdersComplete(worderInformationVo.getWorderNo(), worderInfo.getCompanyOrderNumber());
                if (!response2.getSuccess() && !response2.getMessage().contains("已安装完成,不可重复调用")) {
                    return R.error(Integer.valueOf(response2.getCode()), response2.getMessage());
                }
                entity1.setAttributeValue("1");
                worderInformationAttributeDao.updateById(entity1);
            }
        } catch (Exception e) {
            log.error("请求长安接口失败", e);
            throw new RRException("请求长安接口失败");
        }

        return R.ok().put("num", integer).putWorderNo(worderInformationVo.getWorderNo())
                .putWorderExecStatus(FieldConstant.INSTALL_NOT_COMMIT).putWorderTriggerEvent(WarningConstant.INSTALL_SIGNIN);
    }

    @Override
    public List<WorderInformationEntity> getWorderInformation(String worderNo) {
        return worderOrderDao.getWorderInformation(worderNo);
    }

    @Override
    public WorderInformation getWorderDetails(String worderNo) {
        Map params = new HashMap();
        WorderInformation worderInformation = worderOrderDao.getWorderDetails(worderNo);
        if (StringUtils.isNotBlank(worderInformation.getConveyAppointTime())) {
            worderInformation.setConveyAppointTime(worderInformation.getConveyAppointTime().substring(0, 13) + "时");
        }
        if (StringUtils.isNotBlank(worderInformation.getInstallAppointTime())) {
            worderInformation.setInstallAppointTime(worderInformation.getInstallAppointTime().substring(0, 13) + "时");
        }
//        List<WorderOrderLogEntity> lst = worderOrderLogMapper.selectList(new QueryWrapper<WorderOrderLogEntity>()
//                .eq("worder_no", worderNo).eq("order_status", "2").orderByDesc("order_log_id"));
//        // 未支付
//        Integer payStatus = 0;
//        if (null != lst && lst.size() > 0 && null != lst.get(0)) {
//            // 已支付
//            payStatus = 1;
//        }
//        if (null != worderInformation) {
//            worderInformation.setWorderPayStatus(payStatus);
//        }

        try {
            Object worderHadPay = payService.findWorderHadPay(worderNo);
            worderInformation.setWorderHadPay(worderHadPay);
        } catch (Exception e) {
            worderInformation.setWorderHadPay(R.error());
            e.printStackTrace();
        }

        List<BillingRecodeDTO> billingRecords = billingRecodeMapper.getBillingRecordByWorderNo(worderNo);
        if (billingRecords.size() > 0 && null != billingRecords.get(0)) {
            Integer billingStatus = billingRecords.get(0).getBillingStatus();
            worderInformation.setBillingStatus(billingStatus);
        }
        List<WorderOrderLogDTO> orderLogInfoList = worderOrderLogMapper.findOrderLogInfoList(worderNo, IntegerEnum.TWO.getValue());
        if (orderLogInfoList.size() > IntegerEnum.ZERO.getValue()) {
            WorderOrderLogDTO worderOrderLogDTO = orderLogInfoList.get(IntegerEnum.ZERO.getValue());
            if (null != worderOrderLogDTO) {
                worderInformation.setBillingApply(worderOrderLogDTO.isApply());
            }
        }
        return worderInformation;
    }


    @Override
    public JSONObject createOrderAppointment(String time) {
        return null;
    }

    @Override
    public List<DicKeyResultEntity> listDicKey(String dictionaryId) {
        return worderOrderDao.listDicKey(dictionaryId);
    }

    @Override
    public R listDicKeys(List<String> ids) {
        return R.ok().putList(worderOrderDao.listDicKeys(ids));
    }

    public Map getWorderQueryMap(Map map) {
        map.put("worderConveyStatus", FieldConstant.WORDER_CONVEY_STATUS);
        map.put("worderFinished", FieldConstant.WORDER_FINISHED);
        return map;
    }

    @Override
    public String getWorderInformationCount(Map map) {
        map = getWorderQueryMap(map);
        return worderOrderDao.getWorderInformationCount(map);
    }

    @Override
    public Integer getWorderFieldInfoCount(String worderNo, String fieldId) {
        return getWorderFieldInfoCount(worderNo, fieldId, null);
    }

    public Integer getWorderFieldInfoCount(String worderNo, String fieldId, String other) {
        WorderExtFieldQueryDto worderExtFieldQueryDto = new WorderExtFieldQueryDto();
        worderExtFieldQueryDto.setFieldId(fieldId);
        worderExtFieldQueryDto.setWorderNo(worderNo);
        return worderOrderDao.getWorderFieldInfoCount(worderExtFieldQueryDto);
    }

    @Override
    public R installForceSignOut(InstallSignOutVo installSignOutVo) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(installSignOutVo.getWorderNo());
        //worderInformationDto.setWorderStatus(Integer.parseInt(installSignOutVo.getWorderStatus()));
        worderInformationDto.setInstallSignOutTime(installSignOutVo.getInstallSignOutTime());
        worderInformationDto.setInstallSignOutReason(installSignOutVo.getInstallSignOutReason());
        Integer integer = worderOrderDao.updateWorderInformation(worderInformationDto);
        return R.ok().put("num", integer);
    }

    @Override
    public R conveySignOut(ConveySignOutVo conveySignOutVo) {
        WorderInformationDto worderInformationDto = new WorderInformationDto();
        worderInformationDto.setWorderNo(conveySignOutVo.getWorderNo());
        //worderInformationDto.setWorderStatus(Integer.parseInt(FieldConstant.INSTALL));
        worderInformationDto.setConveySignOutTime(conveySignOutVo.getConveySignOutTime());
        worderInformationDto.setConveySignOutReason(conveySignOutVo.getConveySignOutReason());
        //worderInformationDto.setWorderExecStatus(FieldConstant.);
        Integer integer = worderOrderDao.updateWorderInformation(worderInformationDto);
        return R.ok().put("num", integer);
    }


    @Override
    public String getConveyAppointTime(String worderNo) {
        String conveyAppointTime = null;
        List<WorderInformationEntity> worderInformation = worderOrderDao.getWorderInformation(worderNo);
        WorderInformationEntity worderInformationEntity = null;
        if (worderInformation != null && worderInformation.size() > 0) {
            worderInformationEntity = worderInformation.get(0);
            if (worderInformationEntity != null) {
                conveyAppointTime = worderInformationEntity.getConveyAppointTime();
            }
        }
        return conveyAppointTime;
    }

    @Override
    public String getInstallAppointTime(String worderNo) {
        String installAppointTime = null;
        List<WorderInformationEntity> worderInformation = worderOrderDao.getWorderInformation(worderNo);
        WorderInformationEntity worderInformationEntity = null;
        if (worderInformation != null && worderInformation.size() > 0) {
            worderInformationEntity = worderInformation.get(0);
            if (worderInformationEntity != null) {
                installAppointTime = worderInformationEntity.getInstallAppointTime();
            }
        }
        return installAppointTime;
    }

    @Override
    public List<FieldInfoPo> listWorderFieldInfo(WorderFieldDto worderFieldDto) {
        return null;
    }

    public List<FieldInfoResultPo> getFileUrl(List<FieldInfoPo> fieldInfos) {
        FieldInfoResultPo fieldInfoResult = new FieldInfoResultPo();
        List<FieldInfoResultPo> result = new ArrayList<>();
        for (int i = 0; i < fieldInfos.size(); i++) {
            FieldInfoPo fieldInfo = fieldInfos.get(i);
            if (StringUtils.isNotBlank(fieldInfo.getFieldClass())
                    && Constant.STRING_ONE.equals(fieldInfo.getFieldClass())) {
                List<SysFileEntity> sysFileEntities = new ArrayList<>();
                if (StringUtils.isNotBlank(fieldInfo.getFieldValue())) {
                    // 前后加上大括号
                    String fileIds = StringUtils.appendSqureBeforeAndAfter(fieldInfo.getFieldValue());
                    // 返回所有URL
                    List<String> filePaths = sysFilesMapper.listPaths(fileIds);
                    sysFileEntities = sysFilesMapper.listFileInFileIds(fileIds);
                    fieldInfo.setFieldValue(filePaths.toString());
                } else {
                    fieldInfo.setFieldValue("");
                }
                // 实体类转换
                fieldInfoResult = fieldInfoResult.getResult(fieldInfo);
                fieldInfoResult.setSysFiles(sysFileEntities);
                result.add(fieldInfoResult);
            }
        }
        return result;
    }

    public void setUrlById(List<FieldPo> fieldPos) {
        for (int i = 0, len = fieldPos.size(); i < len; i++) {
            FieldPo fieldInfo = fieldPos.get(i);
            if (StringUtils.isNotBlank(fieldInfo.getFieldClass())
                    && Constant.STRING_ONE.equals(fieldInfo.getFieldClass())) {
                // 前后加上大括号
                String fileIds = StringUtils.appendSqureBeforeAndAfter(fieldInfo.getFieldValues());
                System.out.println(fileIds);
                List<String> filePaths = new ArrayList<>();
                List<SysFileEntity> sysFileEntities = new ArrayList<>();
                if (!"()".equals(fileIds)) {
                    // 返回所有URL
                    filePaths = sysFilesMapper.listPaths(fileIds);
                    sysFileEntities = sysFilesMapper.listFileInFileIds(fileIds);
                }
                fieldPos.get(i).setFieldValue(filePaths);
                fieldPos.get(i).setFieldNameValue(sysFileEntities);
                //fieldPos.get(i).setFieldValues(StringUtils.listToString(filePaths));
            }
        }
    }

    @Override
    public List<FieldInfoResultPo> listWorderConveyFieldInfo(String worderNo) {
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        //worderFieldDto.setFieldClass();
        // 用于勘测的字段信息
        worderFieldDto.setFieldPurpose(FieldConstant.CONVEY);
        worderFieldDto.setWorderNo(worderNo);
        // 查询勘测信息
        List<FieldInfoPo> fieldInfos = worderOrderDao.listWorderFieldInfo(worderFieldDto);
        // 遍历勘测信息，查询文本URL
        return getFileUrl(fieldInfos);
    }

    @Override
    public List<FieldInfoResultPo> listWorderInstallFieldInfo(String worderNo) {
        WorderFieldDto worderFieldDto = new WorderFieldDto();
        //worderFieldDto.setFieldClass();
        // 用于安装的字段信息
        worderFieldDto.setFieldPurpose(FieldConstant.INSTALL);
        worderFieldDto.setWorderNo(worderNo);
        List<FieldInfoPo> fieldInfos = worderOrderDao.listWorderFieldInfo(worderFieldDto);
        return getFileUrl(fieldInfos);
    }

    public List<FieldPo> getFieldData(List<FieldPo> fields) {
        for (int i = 0; i < fields.size(); i++) {
            FieldPo fieldPo = fields.get(i);
            if (fieldPo != null) {
                if ("2".equals(fieldPo.getFieldType())) {
                    if ("1".equals(fieldPo.getFieldDicKeyMark())) {
                        // 字典表
                        fields.get(i).setFieldDicValue(worderOrderDao.listDicKey(fieldPo.getFiledDicKey()));
                    }
                    if ("2".equals(fieldPo.getFieldDicKeyMark())) {
                        // 业务表
                        Map params = new HashMap();
                        String sql = fieldPo.getFiledDicKey();
//                        String[] fieldDicKeys = fieldPo.getFiledDicKey().split(",");
//                        params.put("tableName",fieldDicKeys[0]);
//                        params.put("id",fieldDicKeys[1]);
//                        params.put("name",fieldDicKeys[2]);
//                        params.put("",fieldDicKeys[3]);
//                        params.put("",fieldDicKeys[4]);
                        params.put("sql", sql);
                        List<DicKeyResultEntity> dicKeyResultEntities = worderOrderDao.listTable(params);
                        fields.get(i).setFieldDicValue(dicKeyResultEntities);
                    }
                }
                if ("6".equals(fieldPo.getFieldType()) || "7".equals(fieldPo.getFieldType())) {
                    String[] selectDatas = fieldPo.getSelectData().split(",");
                    List<DicKeyResultEntity> fieldDicValue = new ArrayList<>();
                    for (int j = 0; j < selectDatas.length; j++) {
                        DicKeyResultEntity dicKeyResultEntity = new DicKeyResultEntity();
                        dicKeyResultEntity.setSelectValue(selectDatas[j]);
                        dicKeyResultEntity.setSelectKey(selectDatas[j]);
                        fieldDicValue.add(dicKeyResultEntity);
                    }
                    fields.get(i).setFieldDicValue(fieldDicValue);
                    //fields.get(i).setSelectDatas(selectDatas);
                }
            }
        }
        return fields;
    }

    public List<FieldPo> getFieldData(List<FieldPo> fields, String companySql) {
        for (int i = 0; i < fields.size(); i++) {
            FieldPo fieldPo = fields.get(i);
            if (fieldPo != null) {
                if ("2".equals(fieldPo.getFieldType())) {
                    if ("1".equals(fieldPo.getFieldDicKeyMark())) {
                        // 字典表
                        fields.get(i).setFieldDicValue(worderOrderDao.listDicKey(fieldPo.getFiledDicKey()));
                    }
                    if ("2".equals(fieldPo.getFieldDicKeyMark())) {
                        // 业务表
                        Map<String, String> params = new HashMap<>();
                        String sql = fieldPo.getFiledDicKey();
                        if (sql.contains("order by")) {
                            params.put("sql", sql.replace("order by", "and company_id in " + companySql + " order by"));
                        } else {
                            params.put("sql", sql + " and company_id in " + companySql);
                        }
                        List<DicKeyResultEntity> dicKeyResultEntities = worderOrderDao.listTable(params);
                        fields.get(i).setFieldDicValue(dicKeyResultEntities);
                    }
                }
                if ("6".equals(fieldPo.getFieldType()) || "7".equals(fieldPo.getFieldType())) {
                    String[] selectDatas = fieldPo.getSelectData().split(",");
                    List<DicKeyResultEntity> fieldDicValue = new ArrayList<>();
                    for (int j = 0; j < selectDatas.length; j++) {
                        DicKeyResultEntity dicKeyResultEntity = new DicKeyResultEntity();
                        dicKeyResultEntity.setSelectValue(selectDatas[j]);
                        dicKeyResultEntity.setSelectKey(selectDatas[j]);
                        fieldDicValue.add(dicKeyResultEntity);
                    }
                    fields.get(i).setFieldDicValue(fieldDicValue);
                    //fields.get(i).setSelectDatas(selectDatas);
                }
            }
        }
        return fields;
    }

    public void recodeWorderOperate(String worderNo, String worderStatus, String worderExecStatus, String result) {
        SysUserEntity sysUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        OperationRecord operationRecord = new OperationRecord(sysUser.getUserId(), sysUser.getUsername(), result, worderNo);
        operationRecord.setWorderStatus(worderStatus);
        operationRecord.setWorderExecStatus(worderExecStatus);
        //添加操作记录
        workMsgDao.insertOperation(operationRecord);
    }


    public static void main(String[] args) {
        //6.8712080502376605千米
        //6.86352千米
        WorderOrderServiceImpl worderOrderService = new WorderOrderServiceImpl();
        //文通没转前
        double userY = 121.515796;
        double userX = 31.263552;

        double locationY = 121.521511633411;
        double locationX = 31.27039794917583;

        HashMap<String, Double> map = worderOrderService.gcjToBd(userY, userX);
        //121.522236,31.269399
        System.out.println(map.get("bdUserY") + "," + map.get("bdUserX"));
        //得出结果是千米所有需要乘以一千换算成米
        double distance1 = DistanceUtils.getDistance(121.522236d, 31.269399d, locationY, locationX);
        double distance2 = DistanceUtils.getDistance(121.515796d, 31.263552d, locationY, locationX);
        double distance = DistanceUtils.getDistance(map.get("bdUserY"), map.get("bdUserX"), locationY, locationX);

        System.out.println(distance1);
        System.out.println(distance);
        System.out.println(distance2);
    }

}
