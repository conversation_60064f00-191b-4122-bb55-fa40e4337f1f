/*
 * @Author: nightowl
 * @Date: 2019-09-04 09:21:11
 * @LastEditors: nightowl
 * @LastEditTime: 2019-09-26 14:12:56
 * @Description:
 */
import { get, post, postFormData } from '@/utils/request'

class Service{
    //根据出库单号查询列表信息
    static queryExpressMessage(data = undefined){
        return post('RRSGoodsEnterOrder/getMaterielInformation',data)
    }
    //获取快递公司
    static getExpressCompany(data = undefined){
        return post('RRSGoodsEnterOrder/getFastMail')
    }
    //自动生成入库单
    static getGoodsEntry(data = undefined){
        return post('RRSGoodsEnterOrder/getEnterCode',data)
    }
    static uploadFile(data=undefined){
        return postFormData('/sys/sysuploadrecord/uploadfile',data)
    }
    //入库完成
    static addEnter(data = undefined) {
        return post('/RRSGoodsEnterOrder/enterStore',data)
    }
    static getInfo(id){
        return get(`/worder/bizattendant/info/${id}`)
    }
    static getStore(data = undefined) {
        return get('/RRSGoodsEnterOrder/queryStoreBasicList',data)
    }
    static getPosition(data=undefined){
        return post('/RRSGoodsEnterOrder/queryStorePositionList',data)
    }

    static getWuList(data=undefined){
        return post('/RRSGoodsEnterOrder/queryGoodsBasicList',data)
    }

    static getRegionList(data=undefined){
        return get('/RRSGoodsEnterOrder/queryRegionList',data)
    }
}

export default Service
