<template>
  <div class="mod-user">
    <el-form :inline="true" :model="dataForm" @submit.native.prevent @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.userName" placeholder="用户名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-popover
          ref="deptListPopover"
          placement="bottom-start"
          v-model="showVar"
          trigger="click">
          <el-tree
            lazy
            :load="loadNode"
            style="width:350px;height: 200px;overflow-y: scroll;"
            :data="deptList"
            :props="deptListTreeProps"
            node-key="deptId"
            ref="deptTree"
            :expand-on-click-node="false"
            :highlight-current="true"
            @current-change="deptListTreeCurrentChangeHandle"
          >
          </el-tree>
        </el-popover>
        <el-input v-model="dataForm.deptName" v-popover:deptListPopover :readonly="true" placeholder="机构"
                  class="menu-list__input" clearable @clear="deptNameClear"></el-input>

      </el-form-item>
      <el-form-item>
        <el-select v-model='dataForm.roleId' placeholder='角色' clearable>
          <el-option
            v-for='(item,index) in roleList'
            :key='index'
            :label='item.roleName'
            :value='item.roleId'
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="pageIndex = 1;getDataList()">查询</el-button>
        <el-button v-if="isAuth('sys:user:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
        <el-button v-if="isAuth('sys:user:delete')" type="danger" @click="deleteHandle()"
                   :disabled="dataListSelections.length <= 0">批量删除
        </el-button>
        <el-button v-if="isAuth('sys:user:delete')" type="warning" @click="assignDeptHandle()"
                   :disabled="dataListSelections.length <= 0">批量分配机构
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="dataList"
      border
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        type="index"
        header-align="center"
        align="center"
        width="80"
        label="序号">
      </el-table-column>
      <el-table-column
        prop="username"
        header-align="center"
        align="center"
        label="用户名">
      </el-table-column>
      <el-table-column
        prop="email"
        header-align="center"
        align="center"
        label="邮箱">
      </el-table-column>
      <el-table-column
        prop="mobile"
        header-align="center"
        align="center"
        label="手机号">
      </el-table-column>
      <el-table-column
        prop="status"
        header-align="center"
        align="center"
        label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small">正常</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        header-align="center"
        align="center"
        width="180"
        label="创建时间">
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        align="center"
        width="150"
        label="操作">
        <template slot-scope="scope">
          <el-button v-if="isAuth('sys:user:update')" type="text" size="small"
                     @click="addOrUpdateHandle(scope.row.userId)">修改
          </el-button>
          <el-button v-if="isAuth('sys:user:delete')" type="text" size="small" @click="deleteHandle(scope.row.userId)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    <assign-dept v-if="assignDeptVisible" ref="assignDept" @refreshDataList="getDataList"></assign-dept>
  </div>
</template>

<script>
  import AddOrUpdate from './user-add-or-update'
  import AssignDept from './user-assign-dept'
  import {treeDataTranslate} from '@/utils'
  import {Loading} from 'element-ui';

  export default {
    data() {
      return {
        dataForm: {
          userName: '',
          deptId: '',
          deptName: '',
          roleId: ''
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        dataListSelections: [],
        addOrUpdateVisible: false,
        assignDeptVisible: false,
        showVar: false,
        deptList: [],
        deptListTreeProps: {
          label: 'deptName',
          children: 'children'
        },
        roleList: []
      }
    },
    components: {
      AddOrUpdate,
      AssignDept
    },
    activated() {
      this.getDataList();
      // this.initDeptList();
      this.getRoleList();
    },
    methods: {
      // 异步树叶子节点懒加载逻辑
      loadNode(node, resolve) {
        // 一级节点处理
        if (node.level === 0) {
          let url = this.$http.adornUrl('/sys/dept/getOneLevelDeptList?deptLevel=1');
          if (this.mgr === 1) {
            url = this.$http.adornUrl('/sys/dept/getRootTsDeptManger');
          }
          this.getOneTree(url);
        }
        // 其余节点处理
        if (node.level >= 1) {
          // 注意！把resolve传到你自己的异步中去
          this.getOtherTree(node, resolve)
        }
      },
      getOneTree(url) {
        this.$http({
          url: url,
          method: 'get',
          params: ''
        }).then(({data}) => {
          this.deptList = treeDataTranslate(data.list, 'deptId', 'parentDeptId');
          // console.log("-- this.deptList---->", this.deptList)
        })
      },
      //获取其他树节点
      getOtherTree(val, resolve) {
        this.$http({
          url: this.$http.adornUrl('/sys/dept/getOtherTree'),
          method: 'get',
          params: this.$http.adornParams({deptid: val.key})
        }).then(({data}) => {
          if (data.list && data.list.length > 0) {
            resolve(data.list);
            // this.$nextTick(() => {
            //   this.mgdeptListTreeSetCurrentNode();
            // })
          } else {
            resolve([]);
          }
        })
      },
      // 获取数据列表
      getDataList() {
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/system/user/list'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'username': this.dataForm.userName,
            'deptId': this.dataForm.deptId,
            'roleId': this.dataForm.roleId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // initDeptList() {
      //   // 查询二级机构
      //   this.$http({
      //     url: this.$http.adornUrl('/sys/dept/list'),
      //     method: 'get',
      //     params: this.$http.adornParams({status: '1'})
      //   }).then(({data}) => {
      //     //this.deptList = data && data.code === 0 ? data.list : [];
      //     if (data && data.code === 0 && data.list) {
      //
      //     }
      //     this.deptList = treeDataTranslate(data.list, 'deptId', 'parentDeptId')
      //
      //     // console.log(this.deptList)
      //   })
      // },
      // 查询全部角色
      getRoleList() {
        this.$http({
          url: this.$http.adornUrl('/sys/role/all'),
          method: 'get'
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.roleList = data.list;
          } else {
            this.roleList = [];
          }
        });
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getDataList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getDataList()
      },
      // 多选
      selectionChangeHandle(val) {
        this.dataListSelections = val
      },
      // 新增 / 修改
      addOrUpdateHandle(id) {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.init(id)
        })
      },
      // 删除
      deleteHandle(id) {
        var userIds = id ? [id] : this.dataListSelections.map(item => {
          return item.userId
        })
        this.$confirm(`确定对[id=${userIds.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/system/user/delete'),
            method: 'post',
            data: this.$http.adornData(userIds, false)
          }).then(({data}) => {
            loadingInstance.close();
            if (data && data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 1500,
                onClose: () => {
                  this.getDataList()
                }
              })
            } else {
              this.$message.error(data.msg)
            }
          })
        }).catch(() => {
        })
      },
      // 批量分配机构
      assignDeptHandle(id) {
        this.assignDeptVisible = true;
        var userIds = id ? [id] : this.dataListSelections.map(item => {
          return item.userId
        })
        this.$nextTick(() => {
          this.$refs.assignDept.init(userIds)
        })
      },
      // 查询树点击事件
      deptListTreeCurrentChangeHandle(data, node) {
        this.dataForm.deptId = data.deptId;
        this.dataForm.deptName = data.deptName;
        this.showVar = false;
      },
      // 机构清除
      deptNameClear() {
        this.dataForm.deptId = '';
      }
    }
  }
</script>
