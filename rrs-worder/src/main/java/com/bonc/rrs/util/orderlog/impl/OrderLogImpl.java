package com.bonc.rrs.util.orderlog.impl;

import com.alibaba.fastjson.JSON;
import com.bonc.rrs.util.orderlog.OrderLog;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worderExecuteFlow.dao.FlowWorderMapper;
import com.bonc.rrs.worderExecuteFlow.entity.FlowWorder;
import com.youngking.lenmoncore.common.exception.RRException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrderLogImpl implements OrderLog {

    @Autowired(required = false)
    private WorderInformationDao worderInformationDao;

    @Autowired(required = false)
    private FlowWorderMapper flowWorderMapper;

    @Override
    // @Transactional(rollbackFor = Exception.class)
    public void saveOrderState(WorderInformationEntity order, FlowWorder flowWorder) {
        //更新订单状态
        log.info("flow execute to update order:{}, flowWorder:{}", JSON.toJSONString(order), JSON.toJSONString(flowWorder));
        int maxRetries = 2;
        int countOfUpdateWorder = 0;

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                countOfUpdateWorder = worderInformationDao.updateById(order);
                if (countOfUpdateWorder > 0) {
                    break;
                }
            } catch (Exception e) {
                log.warn("Update failed on attempt {}: {}", attempt + 1, e.getMessage());
            }
        }
        if (countOfUpdateWorder <= 0 ) {
            log.error("update worderInformation error, order:{}", JSON.toJSONString(order));
            throw new RRException("请稍候再试");
        }
        int countOfUpdateFlow = flowWorderMapper.updateById(flowWorder);
        log.info("countOfUpdateFlow:{}, countOfUpdateWorder:{}", countOfUpdateWorder, countOfUpdateFlow);
    }

}
