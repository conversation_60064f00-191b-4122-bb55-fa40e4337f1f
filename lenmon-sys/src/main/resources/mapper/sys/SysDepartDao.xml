<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.youngking.renrenwithactiviti.modules.sys.dao.SysDepartDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.youngking.renrenwithactiviti.modules.sys.entity.SysDepartEntity" id="sysDepartMap">
        <result property="id" column="ID"/>
        <result property="departname" column="departname"/>
        <result property="description" column="description"/>
        <result property="parentdepartid" column="parentdepartid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgType" column="org_type"/>
        <result property="mobile" column="mobile"/>
        <result property="fax" column="fax"/>
        <result property="address" column="address"/>
        <result property="departOrder" column="depart_order"/>
        <result property="departnameEn" column="departname_en"/>
        <result property="departnameAbbr" column="departname_abbr"/>
        <result property="memo" column="memo"/>
        <result property="createName" column="create_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="updateName" column="update_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="sysCompanyCode" column="sys_company_code"/>
        <result property="sysOrgCode" column="sys_org_code"/>
    </resultMap>

    <resultMap id="departTreeMap" type="com.youngking.renrenwithactiviti.modules.sys.entity.DepartTreeEntity">
        <result property="id" column="id1"></result>
        <result property="departname" column="departname1"></result>
        <result property="departnameAbbr" column="departnameAbbr1"></result>
        <result property="departnameEn" column="departnameEn1"></result>
        <result property="departOrder" column="departOrder1"></result>
        <result property="address" column="address1"></result>
        <result property="mobile" column="mobile1"></result>
        <result property="memo" column="memo1"></result>
        <result property="description" column="description1"></result>
        <result property="orgCode" column="orgCode1"></result>
        <result property="orgType" column="orgType1"></result>
        <result property="sysCompanyCode" column="sysCompanyCode1"></result>
        <result property="sysOrgCode" column="sysOrgCode1"></result>
        <collection property="children" ofType="com.youngking.renrenwithactiviti.modules.sys.entity.DepartTreeEntity">
            <result property="id" column="id2"></result>
            <result property="departname" column="departname2"></result>
            <result property="parentName" column="parentName2"></result>
            <result property="departnameAbbr" column="departnameAbbr2"></result>
            <result property="departnameEn" column="departnameEn2"></result>
            <result property="departOrder" column="departOrder2"></result>
            <result property="address" column="address2"></result>
            <result property="mobile" column="mobile2"></result>
            <result property="memo" column="memo2"></result>
            <result property="description" column="description2"></result>
            <result property="orgCode" column="orgCode2"></result>
            <result property="orgType" column="orgType2"></result>
            <result property="sysCompanyCode" column="sysCompanyCode2"></result>
            <result property="sysOrgCode" column="sysOrgCode2"></result>
            <collection property="children" ofType="com.youngking.renrenwithactiviti.modules.sys.entity.DepartTreeEntity">
                <result property="id" column="id3"></result>
                <result property="departname" column="departname3"></result>
                <result property="parentName" column="parentName3"></result>
                <result property="departnameAbbr" column="departnameAbbr3"></result>
                <result property="departnameEn" column="departnameEn3"></result>
                <result property="departOrder" column="departOrder3"></result>
                <result property="address" column="address3"></result>
                <result property="mobile" column="mobile3"></result>
                <result property="memo" column="memo3"></result>
                <result property="description" column="description3"></result>
                <result property="orgCode" column="orgCode3"></result>
                <result property="orgType" column="orgType3"></result>
                <result property="sysCompanyCode" column="sysCompanyCode3"></result>
                <result property="sysOrgCode" column="sysOrgCode3"></result>
                <collection property="children" ofType="com.youngking.renrenwithactiviti.modules.sys.entity.DepartTreeEntity">
                    <result property="id" column="id4"></result>
                    <result property="departname" column="departname4"></result>
                </collection>
            </collection>
        </collection>
    </resultMap>

    <select id="listDepartTree" resultMap="departTreeMap">
        select a1.id id1, a1.departname departname1, a1.departname_abbr departnameAbbr1
        , a1.departname_en departnameEn1, a1.depart_order departOrder1, a1.address address1, a1.mobile mobile1
        , a1.memo memo1, a1.description description1, a1.org_code orgCode1, a1.org_type orgType1
        , a1.sys_company_code sysCompanyCode1, a1.sys_org_code sysOrgCode1
        , a2.id id2, a2.departname departname2, a1.departname parentName2, a2.departname_abbr departnameAbbr2
        , a2.departname_en departnameEn2, a2.depart_order departOrder2, a2.address address2, a2.mobile mobile2
        , a2.memo memo2, a2.description description2, a2.org_code orgCode2, a2.org_type orgType2
        , a2.sys_company_code sysCompanyCode2, a2.sys_org_code sysOrgCode2
        , a3.id id3, a3.departname departname3, a3.departname parentName3, a3.departname_abbr departnameAbbr3
        , a3.departname_en departnameEn3, a3.depart_order departOrder3, a3.address address3, a3.mobile mobile3
        , a3.memo memo3, a2.description description3, a3.org_code orgCode3, a3.org_type orgType3
        , a3.sys_company_code sysCompanyCode3, a3.sys_org_code sysOrgCode3
        , a4.id id4, a4.departname departname4
        from sys_depart a1
        left join sys_depart a2 on a2.parentdepartid = a1.id
        left join sys_depart a3 on a3.parentdepartid = a2.id
        left join sys_depart a4 on a4.parentdepartid = a3.id
        where a1.parentdepartid = 0
    </select>


</mapper>