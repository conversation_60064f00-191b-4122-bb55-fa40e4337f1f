package com.bonc.rrs.worderapp.controller;

import com.bonc.rrs.sendmessage.service.SmsService;
import com.bonc.rrs.worderapp.service.LogService;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.lenmoncore.common.utils.RedisUtils;
import com.youngking.renrenwithactiviti.modules.sys.controller.AbstractController;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserTokenService;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/app/setting")
public class SysSettingController extends AbstractController {

    @Autowired
    private  LogService logService;
    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SmsService smsService;

    @Autowired
    private  SysUserTokenService sysUserTokenService;

    @PostMapping("/mobile/update")
    @ApiOperation("绑定手机号或修改手机号")
    public R updateMobile(HttpServletRequest request) {
        String phoneNumber = request.getParameter("mobile");
        // 验证码验证失败
        if (!smsService.testCodeByMobile(phoneNumber)) {
            return R.error(201,"验证码错误");
        }
        String userId = String.valueOf(((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUserId());

        // 根据userId修改手机号
        return R.result(logService.updateUserPhone(userId, phoneNumber));
    }

//    public boolean validateCode(HttpServletRequest request) {
//        String code = request.getParameter("code");
//        String phoneNumber = request.getParameter("mobile");
//        String code1 = redisUtils.get(Constant.SMS_PHONE + phoneNumber,300L);
//        if(code.equals(code1)){
//            return true;
//        } else {
//            return false;
//        }
//    }

    /**
     * 退出
     */
    @PostMapping("/exit")
    public R logout() {
        sysUserTokenService.logout(getUserId());
        return R.ok();
    }
}
