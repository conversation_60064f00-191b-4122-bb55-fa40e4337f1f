package com.youngking.renrenwithactiviti.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserDepartEntity;
import com.youngking.lenmoncore.common.utils.PageUtils;

import java.util.Map;

/**
 * 用户组织机构关联表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-08-09 16:43:58
 */
public interface SysUserDepartService extends IService<SysUserDepartEntity> {

    PageUtils queryPage(Map<String, Object> params);
}

