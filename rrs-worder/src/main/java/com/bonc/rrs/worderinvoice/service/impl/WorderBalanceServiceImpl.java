package com.bonc.rrs.worderinvoice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bonc.rrs.balanceprocess.entity.CompanyInvoiceEntity;
import com.bonc.rrs.balanceprocess.service.CompanyInvoiceService;
import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.BalanceEnterprisesDetailRecordDao;
import com.bonc.rrs.worderinformationaccount.entity.BalanceEnterprisesDetailRecordEntity;
import com.bonc.rrs.worderinformationaccount.entity.WorderInformationAccountEntity;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.worderinvoice.dao.WorderBalanceDao;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.bonc.rrs.worderinvoice.service.WorderBalanceService;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by zhangyibo on 2020-11-23 09:48
 */
@Slf4j
@Service
public class WorderBalanceServiceImpl implements WorderBalanceService {

    @Autowired(required = false)
    WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    WorderWaitAccountService worderWaitAccountService;
    @Autowired(required = false)
    CompanyInvoiceService companyInvoiceService;
    @Autowired(required = false)
    BranchBalanceService branchBalanceService;
    @Autowired(required = false)
    WorderBalanceDao worderBalanceDao;
    @Autowired(required = false)
    BalanceEnterprisesDetailRecordDao balanceEnterprisesDetailRecordDao;
    @Autowired(required = false)
    WorderInformationAccountService worderInformationAccountService;
    @Autowired(required = false)
    private BalanceProperties balanceProperties;


    @Override
    @Transactional
    public void adjustByInvoiceId(List<Integer> invoiceIdList) {
        List<WorderInformationAccountEntity> worderInformations = worderBalanceDao.listWorderByInvoiceId(invoiceIdList);
        // 全部重算
        for (WorderInformationAccountEntity worderInformation : worderInformations) {
            this.recountNotInvoiceByWorderId(worderInformation.getWorderId());
        }
        List<BalanceEnterprisesDetailRecordEntity> balanceEnterprisesDetailRecords = balanceEnterprisesDetailRecordDao.listSumByInvoiceId(invoiceIdList);

        for (BalanceEnterprisesDetailRecordEntity balanceEnterprisesDetailRecord : balanceEnterprisesDetailRecords) {
            String invoiceId = balanceEnterprisesDetailRecord.getInvoiceId();
            BigDecimal allCompanyBalanceFeeSum = BigDecimal.ZERO;  // 含税总额
            BigDecimal allCompanyBalanceFee = BigDecimal.ZERO;   // 不含税总额
            BigDecimal allCompanyBalanceFeeTax = BigDecimal.ZERO;   // 税额总额
            List<WorderWaitAccountEntity> worderWaitAccountList = worderWaitAccountService.list(new QueryWrapper<WorderWaitAccountEntity>()
                    .eq("invoice_id", invoiceId));
            for (WorderWaitAccountEntity worderWaitAccount : worderWaitAccountList) {
                allCompanyBalanceFeeSum = allCompanyBalanceFeeSum.add(worderWaitAccount.getCompanyBalanceFeeSum());
                allCompanyBalanceFee = allCompanyBalanceFee.add(worderWaitAccount.getCompanyBalanceFee());
                allCompanyBalanceFeeTax = allCompanyBalanceFeeTax.add(worderWaitAccount.getCompanyBalanceFeeTax());
            }
            // 开票单含税总额
            BigDecimal invoiceTaxFeeSum = balanceEnterprisesDetailRecord.getInvoiceTaxFeeSum();
            // 含税总额误差
            BigDecimal taxFeeDifferential = allCompanyBalanceFeeSum.subtract(invoiceTaxFeeSum);
            // 开票单不含税总额
            BigDecimal invoiceNoTaxFeeSum = balanceEnterprisesDetailRecord.getInvoiceNoTaxFeeSum();
            // 不含税总额误差
            BigDecimal noTaxFeeDifferential = allCompanyBalanceFee.subtract(invoiceNoTaxFeeSum);
            // 开票单税额总额
            BigDecimal invoiceTaxSum = balanceEnterprisesDetailRecord.getInvoiceTaxSum();
            // 税额总额误差
            BigDecimal taxDifferential = allCompanyBalanceFeeTax.subtract(invoiceTaxSum);

            WorderWaitAccountEntity worderWaitAccount = worderWaitAccountList.get(worderWaitAccountList.size() - IntegerEnum.ONE.getValue());
            log.info("invoiceId: " + invoiceId + "   含税总额误差: " + taxFeeDifferential + "   不含税总额误差: "
                    + noTaxFeeDifferential + "   税额总额误差: " + taxDifferential);
            if(BigDecimal.ZERO.compareTo(taxFeeDifferential) != IntegerEnum.ZERO.getValue()
                || BigDecimal.ZERO.compareTo(noTaxFeeDifferential) != IntegerEnum.ZERO.getValue()
                    || BigDecimal.ZERO.compareTo(taxDifferential) != IntegerEnum.ZERO.getValue()){
                // 更新工单结算费用
                this.updateWorderFee(worderWaitAccount, taxFeeDifferential, noTaxFeeDifferential, taxDifferential);
                // 更新开票单开票费用
                CompanyInvoiceEntity entity = companyInvoiceService.getById(invoiceId);
                this.updateCompanyInvoiceFee(entity, invoiceTaxFeeSum, invoiceNoTaxFeeSum, invoiceTaxSum);
            }
        }
    }

    /**
     * 更新开票单开票费用
     * @param entity
     * @param invoiceTaxFeeSum
     * @param invoiceNoTaxFeeSum
     * @param invoiceTaxSum
     */
    public void updateCompanyInvoiceFee(CompanyInvoiceEntity entity, BigDecimal invoiceTaxFeeSum,
        BigDecimal invoiceNoTaxFeeSum, BigDecimal invoiceTaxSum){
        //更新开票单状态
        BigDecimal goodsNum = entity.getGoodsNum();
        BigDecimal taxPrice = invoiceTaxFeeSum.divide(goodsNum, 2, balanceProperties.ROUND_MODE);
        BigDecimal noTaxPrice = invoiceNoTaxFeeSum.divide(goodsNum, 2, balanceProperties.ROUND_MODE);

        CompanyInvoiceEntity companyInvoiceEntity = new CompanyInvoiceEntity();
        companyInvoiceEntity.setInvoiceOrderNo(entity.getInvoiceOrderNo());
        companyInvoiceEntity.setId(entity.getId());
        companyInvoiceEntity.setInvoiceFee(invoiceTaxFeeSum);
        companyInvoiceEntity.setNoTaxFee(invoiceNoTaxFeeSum);
        companyInvoiceEntity.setTax(invoiceTaxSum);
        companyInvoiceEntity.setTaxPrice(taxPrice);
        companyInvoiceEntity.setNoTaxPrice(noTaxPrice);
        log.info(companyInvoiceEntity.toString());
        companyInvoiceService.updateById(companyInvoiceEntity);
    }

    /**
     * 更新工单结算费用
     * @param worderWaitAccount
     * @param taxFeeDifferential
     * @param noTaxFeeDifferential
     * @param taxDifferential
     */
    public void updateWorderFee(WorderWaitAccountEntity worderWaitAccount, BigDecimal taxFeeDifferential,
        BigDecimal noTaxFeeDifferential, BigDecimal taxDifferential){

        BigDecimal companyBalanceFeeSum = worderWaitAccount.getCompanyBalanceFeeSum();
        BigDecimal companyBalanceFee = worderWaitAccount.getCompanyBalanceFee();
        BigDecimal companyBalanceFeeTax = worderWaitAccount.getCompanyBalanceFeeTax();
        // 减去误差
        companyBalanceFeeSum = companyBalanceFeeSum.subtract(taxFeeDifferential);
        companyBalanceFee = companyBalanceFee.subtract(noTaxFeeDifferential);
        companyBalanceFeeTax = companyBalanceFeeTax.subtract(taxDifferential);
        worderWaitAccount.setCompanyBalanceFeeSum(companyBalanceFeeSum);
        worderWaitAccount.setCompanyBalanceFee(companyBalanceFee);
        worderWaitAccount.setCompanyBalanceFeeTax(companyBalanceFeeTax);
        // 修改
        worderWaitAccountService.updateById(worderWaitAccount);
        Integer worderInvoiceType = worderWaitAccount.getWorderInvoiceType();
        // 工单类型
        if(IntegerEnum.ZERO.getValue().equals(worderInvoiceType)){
            worderInformationAccountService.update(new UpdateWrapper<WorderInformationAccountEntity>()
                    .eq("worder_id", worderWaitAccount.getWorderId())
                    .set("company_balance_fee", companyBalanceFee)
                    .set("company_balance_fee_sum", companyBalanceFeeSum)
                    .set("company_balance_fee_tax", companyBalanceFeeTax));
        }
    }

    @Override
    public void recountNotInvoiceByWorderId(Integer worderId) {
        worderWaitAccountService.recountWorderBalanceFee(worderId, IntegerEnum.ZERO.getValue());
    }

    @Override
    @Transactional
    public void recountNotInvoice(Integer brandId, Integer templateId){
        // 根据模版id查询未开票工单
        List<WorderInformationEntity> worderInformationList = worderInformationDao.listNoInvoiceWorderByBrandOrTemplate(brandId, templateId);
        Set<Integer> invoiceIdSet = new HashSet<>();
        // 重新计算价格并修改
        for (WorderInformationEntity worderInformationEntity : worderInformationList) {
            // 添加invoiceId
            if(worderInformationEntity.getInvoiceId() != null){
                invoiceIdSet.add(worderInformationEntity.getInvoiceId());
            }
            WorderWaitAccountEntity worderWaitAccountEntity = new WorderWaitAccountEntity();
            worderWaitAccountEntity.setWorderId(worderInformationEntity.getWorderId());
            worderWaitAccountEntity.setWorderInvoiceType(IntegerEnum.ZERO.getValue());
            // 计算更新
            worderWaitAccountService.recountWorderBalanceFee(worderInformationEntity.getWorderId(),
                    IntegerEnum.ZERO.getValue());
        }
        // 更新开票单
//        for (Integer invoiceId : invoiceIdSet) {
//            companyInvoiceService.recountInvoice(invoiceId);
//        }
    }



    @Override
    @Transactional
    public void recountNotAcs(Integer brandId, Integer templateId) {
        // 查询
        List<WorderInformationEntity> worderInformationList =
                worderInformationDao.listInvoiceNoAcsWorderByBrandOrTemplate(brandId, templateId);
        // 修改
        for (WorderInformationEntity worderInformationEntity : worderInformationList) {
            // 只修改网点工单费用
            branchBalanceService.recountCalculateBalanceFeeByWorderId(worderInformationEntity.getWorderId(), null, false);
        }
    }


    @Override
    @Transactional
    public void recountTest() {

        ArrayList<String> array=new ArrayList<String>();
        array.add("k2023-04-240004");
        array.add("k2023-04-260001");
        array.add("k2023-04-260002");
        array.add("k2023-04-260017");
        for(int i=0;i<array.size();i++) {
            // 查询
            List<WorderInformationEntity> worderInformationList =
                    worderInformationDao.listWorderByInovice(array.get(i));
            ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(20);
            // 修改
            for (WorderInformationEntity worderInformationEntity : worderInformationList) {
                executor.execute(new RefreshThread(worderInformationEntity.getWorderId(),this.branchBalanceService));
            }
        }
    }


}

class RefreshThread implements Runnable {

    private  Integer worderId;

    private BranchBalanceService branchBalanceService;

    public RefreshThread(Integer worderId,BranchBalanceService branchBalanceService){
        this.worderId=worderId;
        this.branchBalanceService=branchBalanceService;
    }


    @Override
    public void run() {
        // 只修改网点工单费用
        WorderWaitAccountEntity worderWaitAccountEntity=new WorderWaitAccountEntity();
        this.branchBalanceService.recountCalculateBalanceFeeByWorderId(this.worderId, worderWaitAccountEntity, true);
    }
}
