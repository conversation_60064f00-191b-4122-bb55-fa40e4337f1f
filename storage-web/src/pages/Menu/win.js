import {useEffect, useState} from 'react'
import {connect} from 'dva'
import {Modal, Form, Input, Message, Radio, Popover, Icon, TreeSelect, InputNumber, Button} from 'antd'
import Service from './service'
import Style from './index.less'

const IconArray = ["lock","unlock","bars","book","calendar","cloud","cloud-download","code","copy","credit-card","delete","desktop","download","ellipsis","more","file","file-text","file-unknown","file-pdf","file-word","file-excel","file-jpg","file-ppt","file-markdown","file-add","folder","folder-open","folder-add","hdd","frown","meh","smile","inbox","laptop","appstore","link","mail","mobile","notification","paper-clip","picture","poweroff","reload","search","setting","share-alt","shopping-cart","tablet","tag","tags","to-top","upload","user","video-camera","home","loading","loading-3-quarters","cloud-upload","star","heart","environment","eye","eye-invisible","camera","save","team","solution","phone","filter","exception","import","export","customer-service","qrcode","scan","like","dislike","message","pay-circle","calculator","pushpin","bulb","select","switcher","rocket","bell","disconnect","database","compass","barcode","hourglass","key","flag","layout","printer","sound","usb","skin","tool","sync","wifi","car","schedule","user-add","user-delete","usergroup-add","usergroup-delete","man","woman","shop","gift","idcard","medicine-box","red-envelope","coffee","copyright","trademark","safety","wallet","bank","trophy","contacts","global","shake","api","fork","dashboard","table","profile","alert","audit","branches","build","border","crown","experiment","fire","money-collect","property-safety","read","reconciliation","rest","security-scan","insurance","interation","safety-certificate","project","thunderbolt","block","cluster","deployment-unit","dollar","euro","pound","file-done","file-exclamation","file-protect","file-search","file-sync","gateway","gold","robot","shopping","area-chart"];

const FormItem = Form.Item
const MenuWin = ({visible, close, form, title = '新建菜单', currentMenu, menuData, reloadMenu, changeMenu}) => {
  const [type, setType] = useState(0)
  const [loading, setLoading] = useState(false)// 确定按钮显示loading
  const [showIcon, setShowIcon] = useState(false)
  const [icon, setIcon] = useState('')
  const formItemLayout = {
    labelCol: {
      xs: {span: 24},
      sm: {span: 5}
    },
    wrapperCol: {
      xs: {span: 24},
      sm: {span: 19}
    }
  }
  //组件挂载
  useEffect(() => {
    if (currentMenu !== null) {
      setType(currentMenu.type)
      setIcon(currentMenu.icon)
    }
  }, [currentMenu])
  // 关闭
  const handleClose = () => {
    close && close()
    form.resetFields()
  }
  // 提交
  const handleOk = (e) => {
    e.preventDefault()
    form.validateFields((err, params) => {
      if (err) return
      postData(params)
    })
  }
  const postData = async (params) => {
    setLoading(true)
    let url = `/sys/menu/${currentMenu === null ? 'save' : 'update'}`
    let data = {
      'menuId': currentMenu ? currentMenu.menuId : undefined,
      ...params,
      'type': type,
      'icon': icon
    };
    const response = await Service.postMenu(url, data)
    setLoading(false)
    if (response.code === 0) {
      Message.info('成功!', 1).then(() => {
        changeMenu()
        reloadMenu()
        close && close();
      })
    }
  }
  // 菜单图标气泡
  const handleVisibleChange = (showIcon) => {
    setShowIcon(showIcon)
  }
  // 选择类型
  const onChange = (e) => {
    setType(e.target.value)
    form.resetFields()
    setIcon('')
  }
  // 选择图标,隐藏图标气泡
  const chooseIcon = (item) => {
    setShowIcon(false)
    setIcon(item)
  }
  // 图标气泡内容
  const getIcon = () => {
    return IconArray.map((item, idx) => {
      return <div key={idx} className={Style.iconContent} onClick={chooseIcon.bind(this, item)}><Icon type={item}/>
      </div>
    });
  }
  const content = <div className={Style.iconBox}>{getIcon()}</div>

  const {getFieldDecorator} = form
  return <Modal width={600}
                maskClosable={false}
                onCancel={handleClose}
                title={title}
                visible={visible}
                footer={[
                  <Button key="back" onClick={handleClose}>
                    取消
                  </Button>,
                  <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
                    确定
                  </Button>,
                ]}
  >
    <Form>
      <FormItem hasFeedback {...formItemLayout} label='菜单名称'>
        {getFieldDecorator('name', {
          rules: [{required: true, message: '请输入菜单名称'}],
          initialValue: currentMenu ? currentMenu.name : ''
        })(
          <Input placeholder='请输入菜单名称'/>
        )}
      </FormItem>
      <FormItem {...formItemLayout} label='类型'>
        <Radio.Group onChange={onChange} value={type}>
          <Radio value={0}>目录</Radio>
          <Radio value={1}>菜单</Radio>
          <Radio value={2}>按钮</Radio>
        </Radio.Group>
      </FormItem>
      {type === 2 ? '' : <FormItem {...formItemLayout} label='菜单url'>
        {getFieldDecorator('url', {
          initialValue: currentMenu ? currentMenu.url : ''
        })(
          <Input placeholder='请输入菜单url'/>
        )}
      </FormItem>}
      <FormItem {...formItemLayout} label='上级菜单'>
        {getFieldDecorator('parentId', {
          initialValue: currentMenu ? currentMenu.parentId : '',
          rules: [{required: true, message: '请选择上级菜单'}],
        })(
          <TreeSelect
            // value={parentId}
            dropdownStyle={{maxHeight: 400, overflow: 'auto'}}
            treeData={menuData}
            placeholder="请选择上级菜单"
            // onChange={}
          />
        )}
      </FormItem>
      {type !== 2 ? <FormItem {...formItemLayout} label='排序'>
        {getFieldDecorator('orderNum', {
          initialValue: currentMenu ? currentMenu.orderNum : '',
          rules: [{required: true, message: '请输入排序'}]
        })(
          <InputNumber placeholder='请输入排序'/>
        )}
      </FormItem> : ''}
      {type !== 0 ? <FormItem {...formItemLayout} label='授权标识'>
        {getFieldDecorator('perms', {
          initialValue: currentMenu ? currentMenu.perms : ''
        })(
          <Input placeholder='请输入授权标识'/>
        )}
      </FormItem> : ''}
      {type === 0 ? <FormItem {...formItemLayout} label='菜单图标'>
        <Popover content={content} trigger="click" visible={showIcon} onVisibleChange={handleVisibleChange}>
          <Input disabled={true}/>
          <div className={Style.currentIcon}>{icon ? <Icon style={{fontSize: 18}} type={icon}/> : ''}</div>
        </Popover>
      </FormItem> : ''}
    </Form>
  </Modal>
}

export default connect(({menuMgr}) => ({
  currentMenu: menuMgr.currentMenu,
  menuData: menuMgr.menuData
}))(Form.create({})(MenuWin))
