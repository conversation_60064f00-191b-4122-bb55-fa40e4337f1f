import Routes from './router.config'
import defaultSettings from '../src/defaultSettings'
const {pwa, primaryColor} = defaultSettings

export default {
    publicPath: process.env.NODE_ENV==="development"?"/":"/store/",//打包文件路径
    base: process.env.NODE_ENV==="development"?"/":"/store/",//打包文件路径
    treeShaking: true,
    plugins: [
        ['umi-plugin-react', {
            // locale: {
            //   enable: false,
            //   default: 'zh-CN',
            //   baseNavigator: true
            // },
            antd: true,
            dva: {
                immer: true
            },
            dynamicImport: {webpackChunkName: true},
            title: 'BackAdmin',
            dll: false,
            routes: {
                exclude: [
                    /models\//,
                    /services\//,
                    /model\.(t|j)sx?$/,
                    /service\.(t|j)sx?$/,
                    /components\//
                ],
            },
        }],
    ],
    routes: Routes,
    theme: {
        'primary-color': primaryColor
    }
}

