package com.bonc.rrs.invoice.enterprises.util;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * Created by liqingchao on 2020/3/13.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class ROWSET {
    @XmlElement(name = "row")
    private List<InvoiceResults> invoiceResults;
    @XmlElement(name = "ROWSET")
    private ROWSET rowset;

    public List<InvoiceResults> getInvoiceResults() {
        return invoiceResults;
    }

    public void setInvoiceResults(List<InvoiceResults> invoiceResults) {
        this.invoiceResults = invoiceResults;
    }

    public ROWSET getRowset() {
        return rowset;
    }

    public void setRowset(ROWSET rowset) {
        this.rowset = rowset;
    }
}
