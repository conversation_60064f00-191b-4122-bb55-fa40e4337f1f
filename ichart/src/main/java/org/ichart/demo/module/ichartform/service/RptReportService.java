package org.ichart.demo.module.ichartform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.ichart.demo.module.ichartform.model.entity.RptReport;
import org.ichart.demo.module.ichartform.utils.PageUtils;

import java.util.List;
import java.util.Map;

/**
 * 报表所有的配置项，都存储在CONFIG字段中
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-08 17:10:19
 */
public interface RptReportService extends IService<RptReport> {

    PageUtils queryPage(Map<String, Object> params) throws Exception;

    RptReport getReportByid(String id);
    List<RptReport> getChart();
}

