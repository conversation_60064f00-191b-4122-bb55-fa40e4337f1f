/*
 * @Author: nightowl
 * @Date: 2019-09-24 15:50:41
 * @LastEditors: nightowl
 * @LastEditTime: 2019-09-27 16:09:45
 * @Description:
 */
import { useEffect, useState } from 'react'
import { Upload, Message, Icon } from 'antd'
import { connect } from 'dva'
import getUUID from '@/utils/uuid'

import { IMGURL, beforeUpload } from '@/utils/common'
import Service from '../../services/commonService'


const UploadComponent = ({ maxLenght=1, upFileList=[],defaultFileList=[]},upDisabled=true) => {
    const [fileBack, setFileBack] = useState({ loading: false, previewVisible: false, previewImage: '', fileList: [] })
    const [uuid, setUUID] = useState('')// 上传文件时带上uuid

    useEffect(() => {
        let _uuid = getUUID()
        setUUID(_uuid)
    }, [])


    const customRequest = async (option) => {
        const formData = new FormData();
        formData.append('uuid', uuid)
        formData.append('file', option.file);

        const res = await Service.uploadFile(formData, {
            onUploadProgress: ({ total, loaded }) => {
                option.onProgress({ percent: Math.round(loaded / total * 100).toFixed(2) }, option.file);
            }
        })
        if (res.code !== 0) {
            Message.warning(res.msg)
            return
        } else {
            const { id, fileName, fileUrl } = res.file
            let fileData = {
                uid: id,
                name: fileName,
                status: 'done',
                url: IMGURL + fileUrl
            }
            setFileBack({ ...fileBack, fileList: [...fileBack.fileList, { ...fileData }] })

            upFileList.push(fileUrl)
        }
    }
    const uploadButton = (
        <div>
            <Icon type={fileBack.loading ? 'loading' : 'plus'} />
            <div className="ant-upload-text">上传文件</div>
        </div>
    );

    return (
        <div>
            <Upload
                accept='image/*'
                listType="picture-card"
                fileList={fileBack.fileList.concat( defaultFileList.map((item,index)=>{
                    let fileData = {
                        uid: index,
                        name: item,
                        status: 'done',
                        url: IMGURL + item
                        }
                    return fileData
                }))}
                customRequest={customRequest}
                showUploadList={{ showRemoveIcon: true }}
            >
            {upDisabled&&(fileBack.fileList.length + defaultFileList.length) >= maxLenght ? null : uploadButton}
            </Upload>
        </div>
    )
}

export default connect(({ }) => ({

}))(UploadComponent);
