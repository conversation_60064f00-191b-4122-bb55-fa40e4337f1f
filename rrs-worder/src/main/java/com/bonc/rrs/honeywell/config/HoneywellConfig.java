package com.bonc.rrs.honeywell.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "honeywell")
@Data
public class HoneywellConfig {
    private String baseUrl;
    private String clientId;
    private String clientSecret;
    private String grantType;
    private String scope;
}
