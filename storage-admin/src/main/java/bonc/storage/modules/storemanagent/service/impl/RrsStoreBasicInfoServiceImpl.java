package bonc.storage.modules.storemanagent.service.impl;


import bonc.storage.core.utils.PageUtils;
import bonc.storage.core.utils.Query;
import bonc.storage.core.utils.Result;
import bonc.storage.modules.storemanagent.dao.RrsStoreBasicInfoMapper;
import bonc.storage.modules.storemanagent.entity.RrsStoreBasicInfo;
import bonc.storage.modules.storemanagent.service.IRrsStoreBasicInfoService;
import bonc.storage.modules.storemanager.util.UserRoleFilter;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysRoleService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 仓库基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2020-01-08
 */
@Service
@AllArgsConstructor
public class RrsStoreBasicInfoServiceImpl implements IRrsStoreBasicInfoService {
    @Autowired
    private RrsStoreBasicInfoMapper rrsStoreBasicInfoMapper;
    @Autowired(required = false)
    private SysRoleService sysRoleService;

    private final UserRoleFilter userRoleFilter;

    @Override
    public PageUtils selectRrsStoreBasicInfoList(Query query) {
        //获取用户角色信息
        SysUserEntity user = getUser();
        //判断是否包含项目经理
        List<String> listIds = userRoleFilter.filterRole(getUser());
        if (Objects.nonNull(listIds)) {
            query.put("userId", String.join(",", listIds));
        }
        //总记录条数
        int totalCount = rrsStoreBasicInfoMapper.countStore(query);
        //每页条数
        int limit = query.getLimit();
        //总页数
        int countPage = (int) Math.ceil((double) totalCount / limit);
        countPage = Math.max(countPage, 1);
        //当前页数
        int page = Math.max(1, query.getPage());
        page = Math.min(page, countPage);
        if (page != query.getPage()) {
            query.setPage(page);
            query.put("offset", (page - 1) * limit);
            query.put("page", page);
        }
        //根据条件查询用户信息
        List<RrsStoreBasicInfo> list = rrsStoreBasicInfoMapper.selectRrsStoreBasicInfoList(query);
        for (RrsStoreBasicInfo rrsStoreBasicInfo : list) {
            if (rrsStoreBasicInfo.getNum() == null) {
                rrsStoreBasicInfo.setNum(0);
            }
        }
        return new PageUtils(list, totalCount, limit, page);
    }

    /**
     * 当前角色下的仓库
     * @return
     */
    public  List<Map<String,Object>> selectRrsStoreBasicInfoListRole(Query query){
        //获取用户角色信息
        SysUserEntity user = getUser();
        //判断是否包含项目经理
        List<String> listIds = userRoleFilter.filterRole(getUser());
        if (Objects.nonNull(listIds)) {
            query.put("userId", String.join(",", listIds));
        }
        //根据条件查询用户信息
        List<Map<String, Object>> maps = rrsStoreBasicInfoMapper.selectRrsStoreBasicInfoRole(query);
        return maps ;
    }

    @Override
    public List<RrsStoreBasicInfo> selectRrsStoreBasicInfoListAll() {
        return rrsStoreBasicInfoMapper.selectRrsStoreBasicInfoList(null);
    }

    @Override
    public int insertRrsStoreBasicInfo(RrsStoreBasicInfo rrsStoreBasicInfo) {
        /*Date now = new Date();
        rrsStoreBasicInfo.setCreatTime(now);*/
        List<String> storeName = rrsStoreBasicInfoMapper.getStoreName();
        for (String s : storeName) {
            if (s.equals(rrsStoreBasicInfo.getStoreName())) {
                return -1;
            }
        }
        rrsStoreBasicInfoMapper.insertRrsStoreBasicInfo(rrsStoreBasicInfo);
        SysUserEntity user = getUser();
        return rrsStoreBasicInfoMapper.insertStoreUser(rrsStoreBasicInfo.getId(), user.getUserId());
    }

    @Override
    public RrsStoreBasicInfo selectRrsStoreBasicInfoOne(Map<String, Object> map) {
        return rrsStoreBasicInfoMapper.selectRrsStoreBasicInfoOne(map);
    }

    @Override
    public int updateRrsStoreBasicInfo(RrsStoreBasicInfo rrsStoreBasicInfo) {
        //根据ID查询仓库信息
        QueryWrapper<RrsStoreBasicInfo> rrsStoreBasicInfoQueryWrapper = new QueryWrapper<>();
        rrsStoreBasicInfoQueryWrapper.eq("id", rrsStoreBasicInfo.getId());
        RrsStoreBasicInfo rrsStoreBasic = rrsStoreBasicInfoMapper.selectOne(rrsStoreBasicInfoQueryWrapper);
        //如果仓库名称修改，需要校验仓库名称是否重复，否则不校验
        if (!rrsStoreBasicInfo.getStoreName().equals(rrsStoreBasic.getStoreName())) {
            List<String> storeName = rrsStoreBasicInfoMapper.getStoreName();
            for (String s : storeName) {
                if (s.equals(rrsStoreBasicInfo.getStoreName())) {
                    return -1;
                }
            }
        }
        return rrsStoreBasicInfoMapper.updateRrsStoreBasicInfo(rrsStoreBasicInfo);
    }

    @Override
    public int deleteRrsStoreBasicInfoById(String id) {
        return rrsStoreBasicInfoMapper.deleteRrsStoreBasicInfoById(id);
    }

    /**
     * 删除仓库前校验
     * @param id
     * @return
     */
    @Override
    public Result deleteStoreCheck(String id) {

        List<String> checkEnterOrderList = rrsStoreBasicInfoMapper.checkEnterOrderByStoreId(id);
        if (checkEnterOrderList != null && checkEnterOrderList.size() > 0) {
            return Result.error("仓库还有预录入库单，不能删除!入库单号:" + StringUtils.join(checkEnterOrderList, ","));
        }
        List<String> checkLeaveOrderList = rrsStoreBasicInfoMapper.checkLeaveOrderByStoreId(id);
        if (checkLeaveOrderList != null && checkLeaveOrderList.size() > 0) {
            return Result.error("仓库还有预录出库单，不能删除!出库单号:" + StringUtils.join(checkLeaveOrderList, ","));
        }
        List<String> checkShopApplyEnterOrderList = rrsStoreBasicInfoMapper.checkShopApplyEnterOrderByStoreId(id);
        if (checkShopApplyEnterOrderList != null && checkShopApplyEnterOrderList.size() > 0) {
            return Result.error("仓库还有在途调拨单，不能删除!调拨单号:" + StringUtils.join(checkShopApplyEnterOrderList, ","));
        }
        List<String> checkShopApplyList = rrsStoreBasicInfoMapper.checkShopApplyByStoreId(id);
        if (checkShopApplyList != null && checkShopApplyList.size() > 0) {
            return Result.error("仓库还有待审核调拨单，不能删除!调拨单号:" + StringUtils.join(checkShopApplyList, ","));
        }
        Integer checkStockGoodsNum = rrsStoreBasicInfoMapper.checkStockGoodsByStoreId(id);
        if (checkStockGoodsNum != null && checkStockGoodsNum > 0) {
            return Result.error("仓库还有库存，不能删除");
        }
        return Result.ok();
    }

    @Override
    @Transactional
    public Result deleteRrsStoreBasicInfoByIds(HashMap<String, Object> hashMap) {
        List ids = (List<Integer>) hashMap.get("ids");
        if (ids == null || ids.isEmpty()) {
            return Result.error("请选择要删除的仓库");
        }
        List<String> checkEnterOrderList = rrsStoreBasicInfoMapper.checkEnterOrderByStoreIdList(ids);
        if (checkEnterOrderList != null && checkEnterOrderList.size() > 0) {
            return Result.error("仓库还有预录入库单，不能删除!入库单号:" + StringUtils.join(checkEnterOrderList, ","));
        }
        List<String> checkLeaveOrderList = rrsStoreBasicInfoMapper.checkLeaveOrderByStoreIdList(ids);
        if (checkLeaveOrderList != null && checkLeaveOrderList.size() > 0) {
            return Result.error("仓库还有预录出库单，不能删除!出库单号:" + StringUtils.join(checkLeaveOrderList, ","));
        }
        List<String> checkShopApplyEnterOrderList = rrsStoreBasicInfoMapper.checkShopApplyEnterOrderByStoreIdList(ids);
        if (checkShopApplyEnterOrderList != null && checkShopApplyEnterOrderList.size() > 0) {
            return Result.error("仓库还有在途调拨单，不能删除!调拨单号:" + StringUtils.join(checkShopApplyEnterOrderList, ","));
        }
        List<String> checkShopApplyList = rrsStoreBasicInfoMapper.checkShopApplyByStoreIdList(ids);
        if (checkShopApplyList != null && checkShopApplyList.size() > 0) {
            return Result.error("仓库还有待审核调拨单，不能删除!调拨单号:" + StringUtils.join(checkShopApplyList, ","));
        }

        List<Integer> list = new ArrayList<>();
        list = rrsStoreBasicInfoMapper.selectRrsStoreBasicInfoById(hashMap);
        for (Integer o : list) {
            if (o != null) {
                if (o > 0) {
                    Result.error("库存不为0");
                }
            }
        }
        rrsStoreBasicInfoMapper.deleteBatchIds(ids);
        return Result.ok();
        //return rrsStoreBasicInfoMapper.deleteRrsStoreBasicInfoByIds(hashMap);
    }

    @Override
    public JSONObject storequery(Query query) {
        //总记录条数
        int totalCount = rrsStoreBasicInfoMapper.storequerycount(query);
        //每页条数
        int limit = query.getLimit();
        //总页数
        int countPage = (int) Math.ceil((double) totalCount / limit);
        countPage = Math.max(countPage, 1);
        //当前页数
        int page = Math.max(1, query.getPage());
        page = Math.min(page, countPage);
        if (page != query.getPage()) {
            query.setPage(page);
            query.put("offset", (page - 1) * limit);
            query.put("page", page);
        }
        //根据条件查询用户信息
        List<Map> list = rrsStoreBasicInfoMapper.storequery(query);
        //System.out.println("list"+list.get(0).get("totalNum"));
        int total = 0;
        for (Map records : list) {
            if (records.get("totalNum") != null) {
                total += (Integer) records.get("totalNum");
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("total", total);
        jsonObject.put("PageUtils", new PageUtils(list, totalCount, limit, page));
        return jsonObject;


    }

    @Override
    public List<Map<String, Object>> selectArealist(Integer pid) {
        List<Map<String, Object>> arealist = rrsStoreBasicInfoMapper.selectArealist(pid);
        return arealist;
    }

    public SysUserEntity getUser() {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        return user;
    }

}
